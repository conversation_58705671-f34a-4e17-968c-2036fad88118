package com.unipus.digitalbook.service.common;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DeadlockLoserDataAccessException;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 死锁防范服务测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DeadlockPreventionServiceTest {
    
    private DeadlockPreventionService deadlockPreventionService;
    
    @BeforeEach
    void setUp() {
        deadlockPreventionService = new DeadlockPreventionService();
    }
    
    @Test
    void testExecuteWithOrderedLocking_WithValidIds_ShouldExecuteSuccessfully() {
        // Given
        List<Long> ids = Arrays.asList(3L, 1L, 2L); // 乱序ID
        AtomicInteger executeCount = new AtomicInteger(0);
        Supplier<String> operation = () -> {
            executeCount.incrementAndGet();
            return "success";
        };
        
        // When
        String result = deadlockPreventionService.executeWithOrderedLocking(ids, operation);
        
        // Then
        assertEquals("success", result);
        assertEquals(1, executeCount.get());
    }
    
    @Test
    void testExecuteWithOrderedLocking_WithEmptyIds_ShouldExecuteDirectly() {
        // Given
        List<Long> ids = Arrays.asList();
        AtomicInteger executeCount = new AtomicInteger(0);
        Supplier<String> operation = () -> {
            executeCount.incrementAndGet();
            return "success";
        };
        
        // When
        String result = deadlockPreventionService.executeWithOrderedLocking(ids, operation);
        
        // Then
        assertEquals("success", result);
        assertEquals(1, executeCount.get());
    }
    
    @Test
    void testExecuteWithOrderedLocking_WithNullIds_ShouldExecuteDirectly() {
        // Given
        List<Long> ids = null;
        AtomicInteger executeCount = new AtomicInteger(0);
        Supplier<String> operation = () -> {
            executeCount.incrementAndGet();
            return "success";
        };
        
        // When
        String result = deadlockPreventionService.executeWithOrderedLocking(ids, operation);
        
        // Then
        assertEquals("success", result);
        assertEquals(1, executeCount.get());
    }
    
    @Test
    void testExecuteWithDeadlockRetry_WithSuccessfulOperation_ShouldReturnResult() {
        // Given
        Supplier<String> operation = () -> "success";
        
        // When
        String result = deadlockPreventionService.executeWithDeadlockRetry(operation);
        
        // Then
        assertEquals("success", result);
    }
    
    @Test
    void testExecuteWithDeadlockRetry_WithDeadlockException_ShouldRetry() {
        // Given
        AtomicInteger attemptCount = new AtomicInteger(0);
        Supplier<String> operation = () -> {
            int count = attemptCount.incrementAndGet();
            if (count == 1) {
                throw new DeadlockLoserDataAccessException("Deadlock detected", new SQLException("Deadlock", "40001", 1213));
            }
            return "success_after_retry";
        };
        
        // When
        String result = deadlockPreventionService.executeWithDeadlockRetry(operation);
        
        // Then
        assertEquals("success_after_retry", result);
        assertEquals(2, attemptCount.get());
    }
    
    @Test
    void testExecuteWithDeadlockRetry_WithNonDeadlockException_ShouldThrowImmediately() {
        // Given
        Supplier<String> operation = () -> {
            throw new RuntimeException("Non-deadlock exception");
        };
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> deadlockPreventionService.executeWithDeadlockRetry(operation));
        
        assertEquals("Non-deadlock exception", exception.getMessage());
    }
    
    @Test
    void testExecuteBatchOperation_WithValidItems_ShouldProcessInBatches() {
        // Given
        List<String> items = Arrays.asList("item1", "item2", "item3", "item4", "item5");
        int batchSize = 2;
        AtomicInteger batchCount = new AtomicInteger(0);
        AtomicInteger totalProcessed = new AtomicInteger(0);
        
        Consumer<List<String>> batchOperation = batch -> {
            batchCount.incrementAndGet();
            totalProcessed.addAndGet(batch.size());
        };
        
        // When
        deadlockPreventionService.executeBatchOperation(items, batchSize, batchOperation);
        
        // Then
        assertEquals(3, batchCount.get()); // 5个items，batch size=2，应该分成3批
        assertEquals(5, totalProcessed.get()); // 总共处理5个items
    }
    
    @Test
    void testExecuteBatchOperation_WithEmptyItems_ShouldNotProcess() {
        // Given
        List<String> items = Arrays.asList();
        int batchSize = 2;
        AtomicInteger batchCount = new AtomicInteger(0);
        
        Consumer<List<String>> batchOperation = batch -> batchCount.incrementAndGet();
        
        // When
        deadlockPreventionService.executeBatchOperation(items, batchSize, batchOperation);
        
        // Then
        assertEquals(0, batchCount.get());
    }
    
    @Test
    void testSafeBatchUpdate_WithValidIds_ShouldProcessInOrder() {
        // Given
        List<Long> ids = Arrays.asList(3L, 1L, 4L, 2L); // 乱序ID
        int batchSize = 2;
        AtomicInteger batchCount = new AtomicInteger(0);
        
        Consumer<List<Long>> updateOperation = batch -> {
            batchCount.incrementAndGet();
            // 验证批次内的ID是有序的
            for (int i = 1; i < batch.size(); i++) {
                assertTrue(batch.get(i) > batch.get(i - 1), 
                    "IDs should be in ascending order within batch");
            }
        };
        
        // When
        deadlockPreventionService.safeBatchUpdate(ids, batchSize, updateOperation);
        
        // Then
        assertEquals(2, batchCount.get()); // 4个items，batch size=2，应该分成2批
    }
    
    @Test
    void testSafeBatchUpdate_WithEmptyIds_ShouldNotProcess() {
        // Given
        List<Long> ids = Arrays.asList();
        int batchSize = 2;
        AtomicInteger batchCount = new AtomicInteger(0);
        
        Consumer<List<Long>> updateOperation = batch -> batchCount.incrementAndGet();
        
        // When
        deadlockPreventionService.safeBatchUpdate(ids, batchSize, updateOperation);
        
        // Then
        assertEquals(0, batchCount.get());
    }
    
    @Test
    void testGetRecommendedBatchSize_WithDifferentSizes_ShouldReturnAppropriateSize() {
        // Given & When & Then
        assertEquals(10, deadlockPreventionService.getRecommendedBatchSize(10));
        assertEquals(50, deadlockPreventionService.getRecommendedBatchSize(50));
        assertEquals(50, deadlockPreventionService.getRecommendedBatchSize(100));
        assertEquals(100, deadlockPreventionService.getRecommendedBatchSize(1000));
        assertEquals(200, deadlockPreventionService.getRecommendedBatchSize(5000));
    }
    
    @Test
    void testHasActiveDeadlocks_ShouldReturnFalse() {
        // Given & When
        boolean result = deadlockPreventionService.hasActiveDeadlocks();
        
        // Then
        assertFalse(result); // 当前实现总是返回false
    }
    
    @Test
    void testExecuteInTransaction_WithSuccessfulOperation_ShouldReturnResult() {
        // Given
        Supplier<String> operation = () -> "transaction_success";
        
        // When
        String result = deadlockPreventionService.executeInTransaction(operation);
        
        // Then
        assertEquals("transaction_success", result);
    }
    
    @Test
    void testExecuteInTransaction_WithException_ShouldPropagateException() {
        // Given
        Supplier<String> operation = () -> {
            throw new RuntimeException("Transaction failed");
        };
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> deadlockPreventionService.executeInTransaction(operation));
        
        assertEquals("Transaction failed", exception.getMessage());
    }
}