package com.unipus.digitalbook.service.highconcurrency;

import com.unipus.digitalbook.common.utils.DatabaseOperationUtils;
import com.unipus.digitalbook.dao.BatchOperationDataMapper;
import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationRequest;
import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationResponse;
import com.unipus.digitalbook.model.entity.highconcurrency.BatchOperationData;
import com.unipus.digitalbook.service.common.DeadlockPreventionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 批量操作服务测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class BatchOperationServiceTest {
    
    @Mock
    private BatchOperationDataMapper batchOperationDataMapper;
    
    @Mock
    private DatabaseOperationUtils databaseOperationUtils;
    
    @Mock
    private DeadlockPreventionService deadlockPreventionService;
    
    private BatchOperationService batchOperationService;
    
    @BeforeEach
    void setUp() {
        batchOperationService = new BatchOperationService();
        
        // 使用反射设置依赖
        try {
            java.lang.reflect.Field mapperField = BatchOperationService.class.getDeclaredField("batchOperationDataMapper");
            mapperField.setAccessible(true);
            mapperField.set(batchOperationService, batchOperationDataMapper);
            
            java.lang.reflect.Field utilsField = BatchOperationService.class.getDeclaredField("databaseOperationUtils");
            utilsField.setAccessible(true);
            utilsField.set(batchOperationService, databaseOperationUtils);
            
            java.lang.reflect.Field preventionField = BatchOperationService.class.getDeclaredField("deadlockPreventionService");
            preventionField.setAccessible(true);
            preventionField.set(batchOperationService, deadlockPreventionService);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    @Test
    void testProcessBatchOperation_WithInsertOperation_ShouldReturnSuccessResponse() {
        // Given
        BatchOperationRequest request = createInsertRequest();
        
        when(databaseOperationUtils.calculateBatchSize(anyInt(), anyInt())).thenReturn(100);
        when(databaseOperationUtils.safeBatchInsert(anyList(), anyInt(), any())).thenReturn(3);
        
        // When
        BatchOperationResponse response = batchOperationService.processBatchOperation(request);
        
        // Then
        assertNotNull(response);
        assertEquals("SUCCESS", response.getStatus());
        assertEquals(3, response.getSuccessCount());
        assertEquals(0, response.getFailedCount());
        assertEquals(3, response.getTotalCount());
        assertNotNull(response.getBatchNo());
        assertNotNull(response.getStartTime());
        assertNotNull(response.getEndTime());
        assertNotNull(response.getProcessingTime());
        
        verify(databaseOperationUtils).calculateBatchSize(3, 1);
        verify(databaseOperationUtils).safeBatchInsert(anyList(), eq(100), any());
    }
    
    @Test
    void testProcessBatchOperation_WithUpdateOperation_ShouldReturnSuccessResponse() {
        // Given
        BatchOperationRequest request = createUpdateRequest();
        
        when(databaseOperationUtils.calculateBatchSize(anyInt(), anyInt())).thenReturn(100);
        when(databaseOperationUtils.safeBatchUpdate(anyList(), anyInt(), any())).thenReturn(2);
        
        // When
        BatchOperationResponse response = batchOperationService.processBatchOperation(request);
        
        // Then
        assertNotNull(response);
        assertEquals("SUCCESS", response.getStatus());
        assertEquals(2, response.getSuccessCount());
        assertEquals(0, response.getFailedCount());
        assertEquals(2, response.getTotalCount());
        
        verify(databaseOperationUtils).calculateBatchSize(2, 2);
        verify(databaseOperationUtils).safeBatchUpdate(anyList(), eq(100), any());
    }
    
    @Test
    void testProcessBatchOperation_WithDeleteOperation_ShouldReturnSuccessResponse() {
        // Given
        BatchOperationRequest request = createDeleteRequest();
        
        when(databaseOperationUtils.calculateBatchSize(anyInt(), anyInt())).thenReturn(100);
        when(databaseOperationUtils.safeBatchDelete(anyList(), anyInt(), any())).thenReturn(2);
        
        // When
        BatchOperationResponse response = batchOperationService.processBatchOperation(request);
        
        // Then
        assertNotNull(response);
        assertEquals("SUCCESS", response.getStatus());
        assertEquals(2, response.getSuccessCount());
        assertEquals(0, response.getFailedCount());
        assertEquals(2, response.getTotalCount());
        
        verify(databaseOperationUtils).calculateBatchSize(2, 1);
        verify(databaseOperationUtils).safeBatchDelete(anyList(), eq(100), any());
    }
    
    @Test
    void testProcessBatchOperation_WithUnsupportedOperation_ShouldReturnFailedResponse() {
        // Given
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType("UNSUPPORTED");
        request.setDataItems(Arrays.asList(new BatchOperationRequest.BatchDataItem()));
        
        // When
        BatchOperationResponse response = batchOperationService.processBatchOperation(request);
        
        // Then
        assertNotNull(response);
        assertEquals("FAILED", response.getStatus());
        assertEquals(0, response.getSuccessCount());
        assertEquals(1, response.getFailedCount());
        assertTrue(response.getMessage().contains("不支持的操作类型"));
    }
    
    @Test
    void testProcessBatchOperation_WithUpdateOperationWithoutIds_ShouldThrowException() {
        // Given
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType("UPDATE");
        request.setDataType("TEST");
        
        BatchOperationRequest.BatchDataItem item = new BatchOperationRequest.BatchDataItem();
        item.setBusinessData("test data");
        // 没有设置ID
        
        request.setDataItems(Arrays.asList(item));
        
        // When
        BatchOperationResponse response = batchOperationService.processBatchOperation(request);
        
        // Then
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getMessage().contains("批量更新时ID不能为空"));
    }
    
    @Test
    void testProcessBatchOperation_WithDeleteOperationWithoutIds_ShouldThrowException() {
        // Given
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType("DELETE");
        request.setDataType("TEST");
        
        BatchOperationRequest.BatchDataItem item = new BatchOperationRequest.BatchDataItem();
        item.setBusinessData("test data");
        // 没有设置ID
        
        request.setDataItems(Arrays.asList(item));
        
        // When
        BatchOperationResponse response = batchOperationService.processBatchOperation(request);
        
        // Then
        assertEquals("FAILED", response.getStatus());
        assertTrue(response.getMessage().contains("批量删除时ID不能为空"));
    }
    
    @Test
    void testGetBatchStatus_WithExistingBatch_ShouldReturnStatus() {
        // Given
        String batchNo = "TEST_BATCH_001";
        List<BatchOperationDataMapper.StatusCount> statusCounts = Arrays.asList(
            createStatusCount("COMPLETED", 8),
            createStatusCount("FAILED", 2)
        );
        
        when(batchOperationDataMapper.countByStatus(batchNo)).thenReturn(statusCounts);
        
        // When
        BatchOperationResponse response = batchOperationService.getBatchStatus(batchNo);
        
        // Then
        assertNotNull(response);
        assertEquals(batchNo, response.getBatchNo());
        assertEquals("PARTIAL_SUCCESS", response.getStatus());
        assertEquals(10, response.getTotalCount());
        assertEquals(8, response.getSuccessCount());
        assertEquals(2, response.getFailedCount());
        
        verify(batchOperationDataMapper).countByStatus(batchNo);
    }
    
    @Test
    void testGetBatchStatus_WithNonExistingBatch_ShouldThrowException() {
        // Given
        String batchNo = "NON_EXISTING_BATCH";
        when(batchOperationDataMapper.countByStatus(batchNo)).thenReturn(Arrays.asList());
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> batchOperationService.getBatchStatus(batchNo));
        
        assertTrue(exception.getMessage().contains("批次不存在"));
    }
    
    @Test
    void testGetBatchStatus_WithAllCompletedStatus_ShouldReturnSuccess() {
        // Given
        String batchNo = "TEST_BATCH_002";
        List<BatchOperationDataMapper.StatusCount> statusCounts = Arrays.asList(
            createStatusCount("COMPLETED", 10)
        );
        
        when(batchOperationDataMapper.countByStatus(batchNo)).thenReturn(statusCounts);
        
        // When
        BatchOperationResponse response = batchOperationService.getBatchStatus(batchNo);
        
        // Then
        assertEquals("SUCCESS", response.getStatus());
        assertEquals(10, response.getSuccessCount());
        assertEquals(0, response.getFailedCount());
    }
    
    @Test
    void testGetBatchStatus_WithProcessingStatus_ShouldReturnProcessing() {
        // Given
        String batchNo = "TEST_BATCH_003";
        List<BatchOperationDataMapper.StatusCount> statusCounts = Arrays.asList(
            createStatusCount("COMPLETED", 5),
            createStatusCount("PROCESSING", 3),
            createStatusCount("PENDING", 2)
        );
        
        when(batchOperationDataMapper.countByStatus(batchNo)).thenReturn(statusCounts);
        
        // When
        BatchOperationResponse response = batchOperationService.getBatchStatus(batchNo);
        
        // Then
        assertEquals("PROCESSING", response.getStatus());
        assertEquals(10, response.getTotalCount());
        assertEquals(5, response.getSuccessCount());
        assertEquals(0, response.getFailedCount());
    }
    
    // 辅助方法
    private BatchOperationRequest createInsertRequest() {
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType("INSERT");
        request.setDataType("TEST");
        
        BatchOperationRequest.BatchDataItem item1 = new BatchOperationRequest.BatchDataItem();
        item1.setBusinessData("test data 1");
        
        BatchOperationRequest.BatchDataItem item2 = new BatchOperationRequest.BatchDataItem();
        item2.setBusinessData("test data 2");
        
        BatchOperationRequest.BatchDataItem item3 = new BatchOperationRequest.BatchDataItem();
        item3.setBusinessData("test data 3");
        
        request.setDataItems(Arrays.asList(item1, item2, item3));
        
        return request;
    }
    
    private BatchOperationRequest createUpdateRequest() {
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType("UPDATE");
        request.setDataType("TEST");
        
        BatchOperationRequest.BatchDataItem item1 = new BatchOperationRequest.BatchDataItem();
        item1.setId(1L);
        item1.setBusinessData("updated data 1");
        item1.setVersion(1);
        
        BatchOperationRequest.BatchDataItem item2 = new BatchOperationRequest.BatchDataItem();
        item2.setId(2L);
        item2.setBusinessData("updated data 2");
        item2.setVersion(1);
        
        request.setDataItems(Arrays.asList(item1, item2));
        
        return request;
    }
    
    private BatchOperationRequest createDeleteRequest() {
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType("DELETE");
        request.setDataType("TEST");
        
        BatchOperationRequest.BatchDataItem item1 = new BatchOperationRequest.BatchDataItem();
        item1.setId(1L);
        
        BatchOperationRequest.BatchDataItem item2 = new BatchOperationRequest.BatchDataItem();
        item2.setId(2L);
        
        request.setDataItems(Arrays.asList(item1, item2));
        
        return request;
    }
    
    private BatchOperationDataMapper.StatusCount createStatusCount(String status, int count) {
        BatchOperationDataMapper.StatusCount statusCount = new BatchOperationDataMapper.StatusCount();
        statusCount.setStatus(status);
        statusCount.setCount(count);
        return statusCount;
    }
}