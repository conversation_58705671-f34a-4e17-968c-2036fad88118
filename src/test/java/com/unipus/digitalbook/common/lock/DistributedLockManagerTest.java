package com.unipus.digitalbook.common.lock;

import com.unipus.digitalbook.conf.lock.DistributedLockConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 分布式锁管理器测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DistributedLockManagerTest {
    
    @Mock
    private RedissonClient redissonClient;
    
    @Mock
    private DistributedLockConfig lockConfig;
    
    @Mock
    private RLock lock;
    
    private DistributedLockManager lockManager;
    
    @BeforeEach
    void setUp() {
        lockManager = new DistributedLockManager();
        
        // 使用反射设置依赖
        try {
            java.lang.reflect.Field redissonField = DistributedLockManager.class.getDeclaredField("redissonClient");
            redissonField.setAccessible(true);
            redissonField.set(lockManager, redissonClient);
            
            java.lang.reflect.Field configField = DistributedLockManager.class.getDeclaredField("lockConfig");
            configField.setAccessible(true);
            configField.set(lockManager, lockConfig);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        
        // 设置默认配置
        when(lockConfig.getLockPrefix()).thenReturn("distributed_lock:");
        when(lockConfig.getDefaultWaitTime()).thenReturn(3000L);
        when(lockConfig.getDefaultLeaseTime()).thenReturn(60000L);
        when(lockConfig.isEnableMonitoring()).thenReturn(true);
        when(lockConfig.getTimeoutAlertThreshold()).thenReturn(30000L);
    }
    
    @Test
    void testGetLock_ShouldReturnLockWithCorrectKey() {
        // Given
        String lockKey = "test_lock";
        String expectedFullKey = "distributed_lock:test_lock";
        
        when(redissonClient.getLock(expectedFullKey)).thenReturn(lock);
        
        // When
        RLock result = lockManager.getLock(lockKey);
        
        // Then
        assertSame(lock, result);
        verify(redissonClient).getLock(expectedFullKey);
    }
    
    @Test
    void testTryLock_WithDefaultConfig_ShouldUseDefaultValues() throws InterruptedException {
        // Given
        String lockKey = "test_lock";
        String expectedFullKey = "distributed_lock:test_lock";
        
        when(redissonClient.getLock(expectedFullKey)).thenReturn(lock);
        when(lock.tryLock(3000L, 60000L, TimeUnit.MILLISECONDS)).thenReturn(true);
        
        // When
        boolean result = lockManager.tryLock(lockKey);
        
        // Then
        assertTrue(result);
        verify(lock).tryLock(3000L, 60000L, TimeUnit.MILLISECONDS);
    }
    
    @Test
    void testTryLock_WithCustomTimes_ShouldUseProvidedValues() throws InterruptedException {
        // Given
        String lockKey = "test_lock";
        String expectedFullKey = "distributed_lock:test_lock";
        long waitTime = 5000L;
        long leaseTime = 30000L;
        
        when(redissonClient.getLock(expectedFullKey)).thenReturn(lock);
        when(lock.tryLock(waitTime, leaseTime, TimeUnit.MILLISECONDS)).thenReturn(true);
        
        // When
        boolean result = lockManager.tryLock(lockKey, waitTime, leaseTime);
        
        // Then
        assertTrue(result);
        verify(lock).tryLock(waitTime, leaseTime, TimeUnit.MILLISECONDS);
    }
    
    @Test
    void testTryLock_WhenInterrupted_ShouldReturnFalse() throws InterruptedException {
        // Given
        String lockKey = "test_lock";
        String expectedFullKey = "distributed_lock:test_lock";
        
        when(redissonClient.getLock(expectedFullKey)).thenReturn(lock);
        when(lock.tryLock(anyLong(), anyLong(), any(TimeUnit.class)))
            .thenThrow(new InterruptedException("Test interruption"));
        
        // When
        boolean result = lockManager.tryLock(lockKey);
        
        // Then
        assertFalse(result);
        assertTrue(Thread.currentThread().isInterrupted());
    }
    
    @Test
    void testUnlock_WithValidLock_ShouldUnlockSuccessfully() {
        // Given
        when(lock.isHeldByCurrentThread()).thenReturn(true);
        
        // When
        lockManager.unlock(lock);
        
        // Then
        verify(lock).isHeldByCurrentThread();
        verify(lock).unlock();
    }
    
    @Test
    void testUnlock_WithLockNotHeldByCurrentThread_ShouldNotUnlock() {
        // Given
        when(lock.isHeldByCurrentThread()).thenReturn(false);
        
        // When
        lockManager.unlock(lock);
        
        // Then
        verify(lock).isHeldByCurrentThread();
        verify(lock, never()).unlock();
    }
    
    @Test
    void testExecuteWithLock_WhenLockAcquired_ShouldExecuteOperationAndReleaseLock() throws InterruptedException {
        // Given
        String lockKey = "test_lock";
        String expectedFullKey = "distributed_lock:test_lock";
        String expectedResult = "operation_result";
        
        Supplier<String> operation = () -> expectedResult;
        
        when(redissonClient.getLock(expectedFullKey)).thenReturn(lock);
        when(lock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(lock.isHeldByCurrentThread()).thenReturn(true);
        
        // When
        String result = lockManager.executeWithLock(lockKey, operation);
        
        // Then
        assertEquals(expectedResult, result);
        verify(lock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        verify(lock).unlock();
    }
    
    @Test
    void testExecuteWithLock_WhenLockNotAcquired_ShouldThrowException() throws InterruptedException {
        // Given
        String lockKey = "test_lock";
        String expectedFullKey = "distributed_lock:test_lock";
        
        Supplier<String> operation = () -> "operation_result";
        
        when(redissonClient.getLock(expectedFullKey)).thenReturn(lock);
        when(lock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(false);
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> lockManager.executeWithLock(lockKey, operation));
        
        assertTrue(exception.getMessage().contains("Failed to acquire lock"));
        verify(lock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        verify(lock, never()).unlock();
    }
    
    @Test
    void testExecuteWithLock_WhenOperationThrowsException_ShouldReleaseLockAndRethrow() throws InterruptedException {
        // Given
        String lockKey = "test_lock";
        String expectedFullKey = "distributed_lock:test_lock";
        
        Supplier<String> operation = () -> {
            throw new RuntimeException("Operation failed");
        };
        
        when(redissonClient.getLock(expectedFullKey)).thenReturn(lock);
        when(lock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(lock.isHeldByCurrentThread()).thenReturn(true);
        
        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> lockManager.executeWithLock(lockKey, operation));
        
        assertEquals("Failed to execute operation with lock", exception.getMessage());
        verify(lock).unlock(); // 确保锁被释放
    }
    
    @Test
    void testIsHeldByCurrentThread_ShouldReturnLockStatus() {
        // Given
        String lockKey = "test_lock";
        String expectedFullKey = "distributed_lock:test_lock";
        
        when(redissonClient.getLock(expectedFullKey)).thenReturn(lock);
        when(lock.isHeldByCurrentThread()).thenReturn(true);
        
        // When
        boolean result = lockManager.isHeldByCurrentThread(lockKey);
        
        // Then
        assertTrue(result);
        verify(lock).isHeldByCurrentThread();
    }
    
    @Test
    void testIsLocked_ShouldReturnLockExistence() {
        // Given
        String lockKey = "test_lock";
        String expectedFullKey = "distributed_lock:test_lock";
        
        when(redissonClient.getLock(expectedFullKey)).thenReturn(lock);
        when(lock.isLocked()).thenReturn(true);
        
        // When
        boolean result = lockManager.isLocked(lockKey);
        
        // Then
        assertTrue(result);
        verify(lock).isLocked();
    }
}