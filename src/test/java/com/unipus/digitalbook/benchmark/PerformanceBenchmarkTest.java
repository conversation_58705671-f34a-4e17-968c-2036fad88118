package com.unipus.digitalbook.benchmark;

import com.unipus.digitalbook.controller.HighConcurrencyController;
import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationRequest;
import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationResponse;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 性能基准测试
 * 
 * 测试系统在高并发场景下的性能表现
 * 包括吞吐量、响应时间和资源利用率
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Disabled("需要完整环境才能运行，请在实际测试时启用")
class PerformanceBenchmarkTest {
    
    private static final Logger logger = LoggerFactory.getLogger(PerformanceBenchmarkTest.class);
    
    @Autowired
    private HighConcurrencyController highConcurrencyController;
    
    /**
     * QPS基准测试
     * 测试每秒查询数(Queries Per Second)
     */
    @Test
    void testQPSBenchmark() throws Exception {
        // Given
        int duration = 60; // 测试持续60秒
        int threadCount = 50; // 50个并发线程
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        
        AtomicLong totalRequests = new AtomicLong(0);
        AtomicLong successRequests = new AtomicLong(0);
        AtomicLong errorRequests = new AtomicLong(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        
        List<Long> responseTimes = new CopyOnWriteArrayList<>();
        
        long startTime = System.currentTimeMillis();
        long endTime = startTime + duration * 1000L;
        
        // When
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                while (System.currentTimeMillis() < endTime) {
                    long requestStart = System.currentTimeMillis();
                    
                    try {
                        // 执行查询请求
                        ResponseEntity<Object> response = highConcurrencyController.queryData((long) threadIndex);
                        
                        long requestEnd = System.currentTimeMillis();
                        long responseTime = requestEnd - requestStart;
                        
                        totalRequests.incrementAndGet();
                        totalResponseTime.addAndGet(responseTime);
                        responseTimes.add(responseTime);
                        
                        if (response.getStatusCode().is2xxSuccessful()) {
                            successRequests.incrementAndGet();
                        } else {
                            errorRequests.incrementAndGet();
                        }
                        
                        // 控制请求频率，避免过度消耗资源
                        Thread.sleep(10);
                        
                    } catch (Exception e) {
                        errorRequests.incrementAndGet();
                        totalRequests.incrementAndGet();
                        logger.debug("Request failed in thread {}: {}", threadIndex, e.getMessage());
                    }
                }
            }, executorService);
        }
        
        // Wait for completion
        CompletableFuture.allOf(futures).get(duration + 10, TimeUnit.SECONDS);
        
        long actualDuration = System.currentTimeMillis() - startTime;
        
        // Then - 计算性能指标
        double qps = (totalRequests.get() * 1000.0) / actualDuration;
        double avgResponseTime = (double) totalResponseTime.get() / totalRequests.get();
        double successRate = (successRequests.get() * 100.0) / totalRequests.get();
        
        // 计算P99响应时间
        responseTimes.sort(Long::compareTo);
        long p99ResponseTime = responseTimes.get((int) (responseTimes.size() * 0.99));
        long p95ResponseTime = responseTimes.get((int) (responseTimes.size() * 0.95));
        
        logger.info("QPS Benchmark Test Results:");
        logger.info("- Duration: {}ms", actualDuration);
        logger.info("- Thread count: {}", threadCount);
        logger.info("- Total requests: {}", totalRequests.get());
        logger.info("- Success requests: {}", successRequests.get());
        logger.info("- Error requests: {}", errorRequests.get());
        logger.info("- QPS: {:.2f}", qps);
        logger.info("- Average response time: {:.2f}ms", avgResponseTime);
        logger.info("- P95 response time: {}ms", p95ResponseTime);
        logger.info("- P99 response time: {}ms", p99ResponseTime);
        logger.info("- Success rate: {:.2f}%", successRate);
        
        // 性能基准验证
        assertTrue(qps >= 100, "QPS应该不低于100"); // 根据实际需求调整
        assertTrue(avgResponseTime <= 500, "平均响应时间应该不超过500ms");
        assertTrue(p99ResponseTime <= 2000, "P99响应时间应该不超过2秒");
        assertTrue(successRate >= 99.0, "成功率应该不低于99%");
        
        executorService.shutdown();
    }
    
    /**
     * 批量操作性能测试
     * 测试批量操作的吞吐量和延迟
     */
    @Test
    void testBatchOperationPerformance() throws Exception {
        // Given
        int[] batchSizes = {10, 50, 100, 200}; // 不同的批次大小
        int threadsPerBatch = 5;
        int iterationsPerThread = 10;
        
        for (int batchSize : batchSizes) {
            logger.info("Testing batch size: {}", batchSize);
            
            ExecutorService executorService = Executors.newFixedThreadPool(threadsPerBatch);
            
            AtomicInteger totalOperations = new AtomicInteger(0);
            AtomicLong totalTime = new AtomicLong(0);
            AtomicInteger successCount = new AtomicInteger(0);
            
            List<Long> batchResponseTimes = new CopyOnWriteArrayList<>();
            
            // When
            CompletableFuture<Void>[] futures = new CompletableFuture[threadsPerBatch];
            
            for (int i = 0; i < threadsPerBatch; i++) {
                final int threadIndex = i;
                futures[i] = CompletableFuture.runAsync(() -> {
                    for (int iteration = 0; iteration < iterationsPerThread; iteration++) {
                        try {
                            BatchOperationRequest request = createBatchRequest(
                                "INSERT", batchSize, threadIndex, iteration);
                            
                            long start = System.currentTimeMillis();
                            ResponseEntity<BatchOperationResponse> response = 
                                highConcurrencyController.batchOperation(request);
                            long end = System.currentTimeMillis();
                            
                            long responseTime = end - start;
                            batchResponseTimes.add(responseTime);
                            totalTime.addAndGet(responseTime);
                            totalOperations.incrementAndGet();
                            
                            if (response.getStatusCode().is2xxSuccessful() && 
                                "SUCCESS".equals(response.getBody().getStatus())) {
                                successCount.incrementAndGet();
                            }
                            
                        } catch (Exception e) {
                            logger.warn("Batch operation failed: {}", e.getMessage());
                            totalOperations.incrementAndGet();
                        }
                    }
                }, executorService);
            }
            
            // Wait for completion
            CompletableFuture.allOf(futures).get(120, TimeUnit.SECONDS);
            
            // Then - 分析性能
            double avgResponseTime = (double) totalTime.get() / totalOperations.get();
            double throughput = (totalOperations.get() * batchSize * 1000.0) / totalTime.get();
            double successRate = (successCount.get() * 100.0) / totalOperations.get();
            
            batchResponseTimes.sort(Long::compareTo);
            long p95 = batchResponseTimes.get((int) (batchResponseTimes.size() * 0.95));
            long p99 = batchResponseTimes.get((int) (batchResponseTimes.size() * 0.99));
            
            logger.info("Batch Size {} Performance Results:", batchSize);
            logger.info("- Total batch operations: {}", totalOperations.get());
            logger.info("- Total records processed: {}", totalOperations.get() * batchSize);
            logger.info("- Success rate: {:.2f}%", successRate);
            logger.info("- Average response time: {:.2f}ms", avgResponseTime);
            logger.info("- P95 response time: {}ms", p95);
            logger.info("- P99 response time: {}ms", p99);
            logger.info("- Throughput: {:.2f} records/second", throughput);
            logger.info("---");
            
            // 性能验证
            assertTrue(successRate >= 95.0, "批量操作成功率应该不低于95%");
            assertTrue(avgResponseTime <= 5000, "批量操作平均响应时间应该不超过5秒");
            
            executorService.shutdown();
        }
    }
    
    /**
     * 内存使用率测试
     * 监控高并发操作下的内存使用情况
     */
    @Test
    void testMemoryUsageBenchmark() throws Exception {
        // Given
        Runtime runtime = Runtime.getRuntime();
        int threadCount = 20;
        int operationsPerThread = 100;
        
        // 记录初始内存状态
        System.gc(); // 强制垃圾回收
        Thread.sleep(1000);
        
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        logger.info("Initial memory usage: {}MB", initialMemory / 1024 / 1024);
        
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        AtomicLong maxMemoryUsage = new AtomicLong(initialMemory);
        
        // When - 执行高并发操作
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    try {
                        // 执行批量操作
                        BatchOperationRequest request = createBatchRequest("INSERT", 50, threadIndex, j);
                        highConcurrencyController.batchOperation(request);
                        
                        // 执行查询操作
                        highConcurrencyController.queryData((long) (threadIndex * 1000 + j));
                        
                        // 定期检查内存使用
                        if (j % 10 == 0) {
                            long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                            maxMemoryUsage.updateAndGet(max -> Math.max(max, currentMemory));
                        }
                        
                        Thread.sleep(50); // 避免过度消耗资源
                        
                    } catch (Exception e) {
                        logger.debug("Operation failed: {}", e.getMessage());
                    }
                }
            }, executorService);
        }
        
        // 在执行过程中监控内存
        CompletableFuture<Void> memoryMonitor = CompletableFuture.runAsync(() -> {
            try {
                while (!CompletableFuture.allOf(futures).isDone()) {
                    long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                    maxMemoryUsage.updateAndGet(max -> Math.max(max, currentMemory));
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        // Wait for completion
        CompletableFuture.allOf(futures).get(300, TimeUnit.SECONDS);
        memoryMonitor.cancel(true);
        
        // 强制垃圾回收并检查最终内存
        System.gc();
        Thread.sleep(2000);
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // Then
        long memoryIncrease = maxMemoryUsage.get() - initialMemory;
        long memoryLeak = finalMemory - initialMemory;
        
        logger.info("Memory Usage Benchmark Results:");
        logger.info("- Initial memory: {}MB", initialMemory / 1024 / 1024);
        logger.info("- Max memory usage: {}MB", maxMemoryUsage.get() / 1024 / 1024);
        logger.info("- Final memory: {}MB", finalMemory / 1024 / 1024);
        logger.info("- Memory increase during test: {}MB", memoryIncrease / 1024 / 1024);
        logger.info("- Potential memory leak: {}MB", memoryLeak / 1024 / 1024);
        logger.info("- Total operations: {}", threadCount * operationsPerThread);
        
        // 内存使用验证
        assertTrue(memoryIncrease < 500 * 1024 * 1024, "内存增长应该少于500MB");
        assertTrue(memoryLeak < 100 * 1024 * 1024, "潜在内存泄漏应该少于100MB");
        
        executorService.shutdown();
    }
    
    /**
     * CPU使用率测试
     * 监控高并发操作下的CPU使用情况
     */
    @Test
    void testCPUUsageBenchmark() throws Exception {
        // This is a simplified CPU monitoring test
        // In production, you would use more sophisticated monitoring tools
        
        // Given
        int duration = 30; // 30秒测试
        int threadCount = Runtime.getRuntime().availableProcessors() * 2;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        
        AtomicLong operationCount = new AtomicLong(0);
        long startTime = System.currentTimeMillis();
        long endTime = startTime + duration * 1000L;
        
        // When
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                while (System.currentTimeMillis() < endTime) {
                    try {
                        // CPU密集型操作
                        BatchOperationRequest request = createBatchRequest("INSERT", 20, threadIndex, 0);
                        highConcurrencyController.batchOperation(request);
                        
                        operationCount.incrementAndGet();
                        
                    } catch (Exception e) {
                        logger.debug("CPU test operation failed: {}", e.getMessage());
                    }
                }
            }, executorService);
        }
        
        // Wait for completion
        CompletableFuture.allOf(futures).get(duration + 10, TimeUnit.SECONDS);
        
        long actualDuration = System.currentTimeMillis() - startTime;
        
        // Then
        double operationsPerSecond = (operationCount.get() * 1000.0) / actualDuration;
        
        logger.info("CPU Usage Benchmark Results:");
        logger.info("- Duration: {}ms", actualDuration);
        logger.info("- Thread count: {}", threadCount);
        logger.info("- Available processors: {}", Runtime.getRuntime().availableProcessors());
        logger.info("- Total operations: {}", operationCount.get());
        logger.info("- Operations per second: {:.2f}", operationsPerSecond);
        logger.info("- Operations per thread per second: {:.2f}", operationsPerSecond / threadCount);
        
        // CPU效率验证
        assertTrue(operationCount.get() > 0, "应该完成一些操作");
        assertTrue(operationsPerSecond > 10, "每秒操作数应该大于10");
        
        executorService.shutdown();
    }
    
    // 辅助方法
    private BatchOperationRequest createBatchRequest(String operationType, int batchSize, 
                                                   int threadIndex, int iteration) {
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType(operationType);
        request.setDataType("BENCHMARK_TEST");
        request.setBatchNo("BENCHMARK_" + threadIndex + "_" + iteration + "_" + System.currentTimeMillis());
        
        List<BatchOperationRequest.BatchDataItem> items = new ArrayList<>();
        for (int i = 0; i < batchSize; i++) {
            BatchOperationRequest.BatchDataItem item = new BatchOperationRequest.BatchDataItem();
            if ("INSERT".equals(operationType)) {
                item.setBusinessData("Benchmark data " + threadIndex + "_" + iteration + "_" + i);
            } else {
                item.setId((long) (threadIndex * 1000 + iteration * 100 + i));
                item.setBusinessData("Updated benchmark data " + System.currentTimeMillis());
                item.setVersion(1);
            }
            items.add(item);
        }
        
        request.setDataItems(items);
        return request;
    }
}