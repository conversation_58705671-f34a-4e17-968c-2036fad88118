package com.unipus.digitalbook.deadlock;

import com.unipus.digitalbook.dao.DeadlockTestMapper;
import com.unipus.digitalbook.service.common.DeadlockPreventionService;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DeadlockLoserDataAccessException;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 死锁测试用例
 * 
 * 测试数据库死锁的产生和防范机制
 * 注意：这些测试需要完整的Spring上下文和数据库连接
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Disabled("需要完整环境才能运行，请在实际测试时启用")
class DeadlockTest {
    
    private static final Logger logger = LoggerFactory.getLogger(DeadlockTest.class);
    
    @Autowired
    private DeadlockTestMapper deadlockTestMapper;
    
    @Autowired
    private DeadlockPreventionService deadlockPreventionService;
    
    /**
     * 测试经典死锁场景
     * 两个事务以不同顺序访问相同的资源
     */
    @Test
    void testClassicDeadlockScenario() throws Exception {
        // Given
        int threadCount = 2;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger deadlockCount = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        
        // When
        CompletableFuture<Void> thread1 = CompletableFuture.runAsync(() -> {
            try {
                // 线程1：先锁table1，再锁table2
                deadlockTestMapper.simulateDeadlockScenario1(1L, "Thread1 Value1", "Thread1 Value2");
                successCount.incrementAndGet();
                logger.info("Thread 1 completed successfully");
            } catch (DeadlockLoserDataAccessException e) {
                deadlockCount.incrementAndGet();
                logger.warn("Thread 1 encountered deadlock: {}", e.getMessage());
            } catch (Exception e) {
                logger.error("Thread 1 failed with exception", e);
            }
        }, executorService);
        
        CompletableFuture<Void> thread2 = CompletableFuture.runAsync(() -> {
            try {
                // 稍微延迟，增加死锁概率
                Thread.sleep(10);
                // 线程2：先锁table2，再锁table1
                deadlockTestMapper.simulateDeadlockScenario2(1L, "Thread2 Value1", "Thread2 Value2");
                successCount.incrementAndGet();
                logger.info("Thread 2 completed successfully");
            } catch (DeadlockLoserDataAccessException e) {
                deadlockCount.incrementAndGet();
                logger.warn("Thread 2 encountered deadlock: {}", e.getMessage());
            } catch (Exception e) {
                logger.error("Thread 2 failed with exception", e);
            }
        }, executorService);
        
        // Wait for completion
        CompletableFuture.allOf(thread1, thread2).get(30, TimeUnit.SECONDS);
        
        // Then
        logger.info("Classic deadlock test completed:");
        logger.info("- Success count: {}", successCount.get());
        logger.info("- Deadlock count: {}", deadlockCount.get());
        
        // 至少应该有一个线程成功，可能有死锁发生
        assertTrue(successCount.get() > 0, "至少应有一个线程成功执行");
        
        executorService.shutdown();
    }
    
    /**
     * 测试批量更新死锁防范
     * 多个线程同时更新相同的记录集，但使用排序避免死锁
     */
    @Test
    void testBatchUpdateDeadlockPrevention() throws Exception {
        // Given
        int threadCount = 10;
        List<Long> targetIds = Arrays.asList(1L, 2L, 3L, 4L, 5L);
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger deadlockCount = new AtomicInteger(0);
        AtomicInteger otherErrorCount = new AtomicInteger(0);
        
        // When
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    // 使用死锁防范服务执行批量更新
                    deadlockPreventionService.executeWithOrderedLocking(targetIds, () -> {
                        try {
                            // 模拟批量更新操作
                            List<Long> shuffledIds = Arrays.asList(5L, 1L, 3L, 2L, 4L); // 故意打乱顺序
                            int updated = deadlockTestMapper.safeBatchUpdateTable1(
                                shuffledIds, "Updated by thread " + threadIndex);
                            logger.debug("Thread {} updated {} records", threadIndex, updated);
                            return updated;
                        } catch (Exception e) {
                            logger.error("Update operation failed in thread {}", threadIndex, e);
                            throw new RuntimeException(e);
                        }
                    });
                    
                    successCount.incrementAndGet();
                    logger.info("Thread {} completed batch update successfully", threadIndex);
                    
                } catch (DeadlockLoserDataAccessException e) {
                    deadlockCount.incrementAndGet();
                    logger.warn("Thread {} encountered deadlock: {}", threadIndex, e.getMessage());
                } catch (Exception e) {
                    otherErrorCount.incrementAndGet();
                    logger.error("Thread {} failed with exception", threadIndex, e);
                }
            }, executorService);
        }
        
        // Wait for completion
        CompletableFuture.allOf(futures).get(60, TimeUnit.SECONDS);
        
        // Then
        logger.info("Batch update deadlock prevention test completed:");
        logger.info("- Thread count: {}", threadCount);
        logger.info("- Success count: {}", successCount.get());
        logger.info("- Deadlock count: {}", deadlockCount.get());
        logger.info("- Other error count: {}", otherErrorCount.get());
        logger.info("- Success rate: {:.2f}%", (successCount.get() * 100.0) / threadCount);
        
        // 使用死锁防范机制后，死锁应该大大减少
        assertTrue(successCount.get() >= threadCount * 0.8, "成功率应该超过80%");
        assertTrue(deadlockCount.get() <= threadCount * 0.1, "死锁率应该低于10%");
        
        executorService.shutdown();
    }
    
    /**
     * 测试原子操作防死锁
     * 确保操作的原子性和一致的锁定顺序
     */
    @Test
    void testAtomicOperationDeadlockPrevention() throws Exception {
        // Given
        int threadCount = 20;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger deadlockCount = new AtomicInteger(0);
        
        // When
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            final Long refId = (long) (threadIndex % 5 + 1); // 使用5个不同的refId
            
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    // 使用原子更新操作（保证固定的锁定顺序）
                    int updated = deadlockPreventionService.executeWithDeadlockRetry(() -> 
                        deadlockTestMapper.atomicUpdateBothTables(
                            refId, 
                            "Atomic Value1 - Thread " + threadIndex,
                            "Atomic Value2 - Thread " + threadIndex)
                    );
                    
                    successCount.incrementAndGet();
                    logger.info("Thread {} completed atomic update successfully, updated: {}", threadIndex, updated);
                    
                } catch (DeadlockLoserDataAccessException e) {
                    deadlockCount.incrementAndGet();
                    logger.warn("Thread {} encountered deadlock: {}", threadIndex, e.getMessage());
                } catch (Exception e) {
                    logger.error("Thread {} failed with exception", threadIndex, e);
                }
            }, executorService);
        }
        
        // Wait for completion
        CompletableFuture.allOf(futures).get(60, TimeUnit.SECONDS);
        
        // Then
        logger.info("Atomic operation deadlock prevention test completed:");
        logger.info("- Thread count: {}", threadCount);
        logger.info("- Success count: {}", successCount.get());
        logger.info("- Deadlock count: {}", deadlockCount.get());
        logger.info("- Success rate: {:.2f}%", (successCount.get() * 100.0) / threadCount);
        
        // 原子操作应该显著减少死锁
        assertTrue(successCount.get() >= threadCount * 0.9, "原子操作成功率应该超过90%");
        assertTrue(deadlockCount.get() <= threadCount * 0.05, "死锁率应该低于5%");
        
        executorService.shutdown();
    }
    
    /**
     * 测试死锁重试机制
     * 验证死锁发生时的自动重试功能
     */
    @Test
    void testDeadlockRetryMechanism() throws Exception {
        // Given
        int threadCount = 6;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger totalAttempts = new AtomicInteger(0);
        AtomicInteger finalSuccessCount = new AtomicInteger(0);
        AtomicInteger finalFailureCount = new AtomicInteger(0);
        
        // When
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    // 使用死锁重试机制
                    deadlockPreventionService.executeWithDeadlockRetry(() -> {
                        totalAttempts.incrementAndGet();
                        
                        // 故意创造死锁条件
                        if (threadIndex % 2 == 0) {
                            deadlockTestMapper.simulateDeadlockScenario1(
                                (long) (threadIndex % 3 + 1), 
                                "Retry Test Value1", 
                                "Retry Test Value2");
                        } else {
                            deadlockTestMapper.simulateDeadlockScenario2(
                                (long) (threadIndex % 3 + 1), 
                                "Retry Test Value1", 
                                "Retry Test Value2");
                        }
                        
                        return null;
                    });
                    
                    finalSuccessCount.incrementAndGet();
                    logger.info("Thread {} finally succeeded with retry mechanism", threadIndex);
                    
                } catch (Exception e) {
                    finalFailureCount.incrementAndGet();
                    logger.warn("Thread {} finally failed even with retry: {}", threadIndex, e.getMessage());
                }
            }, executorService);
        }
        
        // Wait for completion
        CompletableFuture.allOf(futures).get(120, TimeUnit.SECONDS);
        
        // Then
        logger.info("Deadlock retry mechanism test completed:");
        logger.info("- Thread count: {}", threadCount);
        logger.info("- Total attempts: {}", totalAttempts.get());
        logger.info("- Final success count: {}", finalSuccessCount.get());
        logger.info("- Final failure count: {}", finalFailureCount.get());
        logger.info("- Average attempts per thread: {:.2f}", (double) totalAttempts.get() / threadCount);
        logger.info("- Final success rate: {:.2f}%", (finalSuccessCount.get() * 100.0) / threadCount);
        
        // 重试机制应该提高最终成功率
        assertTrue(finalSuccessCount.get() > 0, "重试机制应该帮助至少部分操作成功");
        assertTrue(totalAttempts.get() > threadCount, "应该发生重试");
        
        executorService.shutdown();
    }
    
    /**
     * 测试长时间运行的死锁检测
     * 模拟长时间运行的系统中的死锁检测和处理
     */
    @Test
    void testLongRunningDeadlockDetection() throws Exception {
        // Given
        int duration = 30; // 运行30秒
        int threadCount = 8;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger operationCount = new AtomicInteger(0);
        AtomicInteger deadlockCount = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        long endTime = startTime + duration * 1000;
        
        // When
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                while (System.currentTimeMillis() < endTime) {
                    try {
                        operationCount.incrementAndGet();
                        
                        // 随机选择操作类型
                        Long refId = (long) (threadIndex % 3 + 1);
                        
                        if (threadIndex % 3 == 0) {
                            // 计数器递增
                            deadlockTestMapper.incrementCounterTable1(refId);
                            deadlockTestMapper.incrementCounterTable2(refId);
                        } else if (threadIndex % 3 == 1) {
                            // 批量更新
                            deadlockTestMapper.safeBatchUpdateTable1(
                                Arrays.asList(refId), "Long running test " + operationCount.get());
                        } else {
                            // 原子更新
                            deadlockTestMapper.atomicUpdateBothTables(
                                refId, "Atomic " + operationCount.get(), "Atomic " + operationCount.get());
                        }
                        
                        successCount.incrementAndGet();
                        
                        // 短暂休眠避免过度消耗资源
                        Thread.sleep(50 + (int) (Math.random() * 100));
                        
                    } catch (DeadlockLoserDataAccessException e) {
                        deadlockCount.incrementAndGet();
                        logger.debug("Thread {} encountered deadlock, will continue", threadIndex);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        logger.warn("Thread {} encountered error: {}", threadIndex, e.getMessage());
                    }
                }
                
                logger.info("Thread {} completed long running test", threadIndex);
            }, executorService);
        }
        
        // Wait for completion
        CompletableFuture.allOf(futures).get(duration + 10, TimeUnit.SECONDS);
        
        long actualDuration = System.currentTimeMillis() - startTime;
        
        // Then
        logger.info("Long running deadlock detection test completed:");
        logger.info("- Duration: {}ms", actualDuration);
        logger.info("- Thread count: {}", threadCount);
        logger.info("- Total operations: {}", operationCount.get());
        logger.info("- Success count: {}", successCount.get());
        logger.info("- Deadlock count: {}", deadlockCount.get());
        logger.info("- Operations per second: {:.2f}", (operationCount.get() * 1000.0) / actualDuration);
        logger.info("- Deadlock rate: {:.2f}%", (deadlockCount.get() * 100.0) / operationCount.get());
        logger.info("- Success rate: {:.2f}%", (successCount.get() * 100.0) / operationCount.get());
        
        // 验证系统在长时间运行下的稳定性
        assertTrue(operationCount.get() > 0, "应该执行了一些操作");
        assertTrue(successCount.get() > operationCount.get() * 0.7, "成功率应该超过70%");
        assertTrue(deadlockCount.get() < operationCount.get() * 0.1, "死锁率应该低于10%");
        
        executorService.shutdown();
    }
}