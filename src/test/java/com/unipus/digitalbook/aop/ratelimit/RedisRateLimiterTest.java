package com.unipus.digitalbook.aop.ratelimit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Redis限流器测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class RedisRateLimiterTest {
    
    @Mock
    private RedissonClient redissonClient;
    
    @Mock
    private RScript script;
    
    private RedisRateLimiter rateLimiter;
    
    @BeforeEach
    void setUp() {
        rateLimiter = new RedisRateLimiter();
        // 使用反射设置 redissonClient
        try {
            java.lang.reflect.Field field = RedisRateLimiter.class.getDeclaredField("redissonClient");
            field.setAccessible(true);
            field.set(rateLimiter, redissonClient);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    @Test
    void testIsAllowed_WhenWithinLimit_ShouldReturnTrue() {
        // Given
        String key = "test_key";
        int maxRequests = 10;
        int timeWindow = 60;
        
        when(redissonClient.getScript()).thenReturn(script);
        when(script.eval(
            eq(RScript.Mode.READ_WRITE),
            anyString(),
            eq(RScript.ReturnType.MULTI),
            eq(Collections.singletonList(key)),
            eq(timeWindow), eq(maxRequests), anyLong()
        )).thenReturn(new Object[]{1L, 5L}); // allowed=1, remaining=5
        
        // When
        boolean result = rateLimiter.isAllowed(key, maxRequests, timeWindow);
        
        // Then
        assertTrue(result);
        verify(redissonClient).getScript();
        verify(script).eval(
            eq(RScript.Mode.READ_WRITE),
            anyString(),
            eq(RScript.ReturnType.MULTI),
            eq(Collections.singletonList(key)),
            eq(timeWindow), eq(maxRequests), anyLong()
        );
    }
    
    @Test
    void testIsAllowed_WhenExceedsLimit_ShouldReturnFalse() {
        // Given
        String key = "test_key";
        int maxRequests = 10;
        int timeWindow = 60;
        
        when(redissonClient.getScript()).thenReturn(script);
        when(script.eval(
            eq(RScript.Mode.READ_WRITE),
            anyString(),
            eq(RScript.ReturnType.MULTI),
            eq(Collections.singletonList(key)),
            eq(timeWindow), eq(maxRequests), anyLong()
        )).thenReturn(new Object[]{0L, 0L}); // allowed=0, remaining=0
        
        // When
        boolean result = rateLimiter.isAllowed(key, maxRequests, timeWindow);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testIsAllowed_WhenExceptionOccurs_ShouldReturnTrue() {
        // Given
        String key = "test_key";
        int maxRequests = 10;
        int timeWindow = 60;
        
        when(redissonClient.getScript()).thenThrow(new RuntimeException("Redis connection failed"));
        
        // When
        boolean result = rateLimiter.isAllowed(key, maxRequests, timeWindow);
        
        // Then
        assertTrue(result); // 异常时默认允许通过
    }
    
    @Test
    void testIsAllowed_WithDifferentKeys_ShouldBeTreatedSeparately() {
        // Given
        String key1 = "test_key_1";
        String key2 = "test_key_2";
        int maxRequests = 5;
        int timeWindow = 60;
        
        when(redissonClient.getScript()).thenReturn(script);
        
        // key1 allowed
        when(script.eval(
            eq(RScript.Mode.READ_WRITE),
            anyString(),
            eq(RScript.ReturnType.MULTI),
            eq(Collections.singletonList(key1)),
            eq(timeWindow), eq(maxRequests), anyLong()
        )).thenReturn(new Object[]{1L, 4L});
        
        // key2 not allowed
        when(script.eval(
            eq(RScript.Mode.READ_WRITE),
            anyString(),
            eq(RScript.ReturnType.MULTI),
            eq(Collections.singletonList(key2)),
            eq(timeWindow), eq(maxRequests), anyLong()
        )).thenReturn(new Object[]{0L, 0L});
        
        // When & Then
        assertTrue(rateLimiter.isAllowed(key1, maxRequests, timeWindow));
        assertFalse(rateLimiter.isAllowed(key2, maxRequests, timeWindow));
    }
    
    @Test
    void testIsAllowed_WithZeroMaxRequests_ShouldReturnFalse() {
        // Given
        String key = "test_key";
        int maxRequests = 0;
        int timeWindow = 60;
        
        when(redissonClient.getScript()).thenReturn(script);
        when(script.eval(any(), anyString(), any(), anyList(), any()))
            .thenReturn(new Object[]{0L, 0L});
        
        // When
        boolean result = rateLimiter.isAllowed(key, maxRequests, timeWindow);
        
        // Then
        assertFalse(result);
    }
}