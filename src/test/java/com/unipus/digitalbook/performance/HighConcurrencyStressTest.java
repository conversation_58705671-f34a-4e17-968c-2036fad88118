package com.unipus.digitalbook.performance;

import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationRequest;
import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationResponse;
import com.unipus.digitalbook.service.highconcurrency.BatchOperationService;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 高并发压力测试
 * 
 * 注意：这些测试需要完整的Spring上下文和数据库连接
 * 在实际环境中运行时请取消@Disabled注解
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Disabled("需要完整环境才能运行，请在实际测试时启用")
class HighConcurrencyStressTest {
    
    private static final Logger logger = LoggerFactory.getLogger(HighConcurrencyStressTest.class);
    
    @Autowired
    private BatchOperationService batchOperationService;
    
    /**
     * 并发插入测试
     * 模拟多个线程同时执行批量插入操作
     */
    @Test
    void testConcurrentBatchInsert() throws Exception {
        // Given
        int threadCount = 10;
        int batchSizePerThread = 100;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicLong totalTime = new AtomicLong(0);
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        // When
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    BatchOperationRequest request = createBatchInsertRequest(threadIndex, batchSizePerThread);
                    
                    long threadStartTime = System.currentTimeMillis();
                    BatchOperationResponse response = batchOperationService.processBatchOperation(request);
                    long threadEndTime = System.currentTimeMillis();
                    
                    totalTime.addAndGet(threadEndTime - threadStartTime);
                    
                    if ("SUCCESS".equals(response.getStatus())) {
                        successCount.incrementAndGet();
                        logger.info("Thread {} completed successfully in {}ms", 
                            threadIndex, threadEndTime - threadStartTime);
                    } else {
                        failureCount.incrementAndGet();
                        logger.error("Thread {} failed: {}", threadIndex, response.getMessage());
                    }
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    logger.error("Thread {} encountered exception", threadIndex, e);
                }
            }, executorService);
            
            futures.add(future);
        }
        
        // Wait for all threads to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(60, TimeUnit.SECONDS);
        
        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;
        
        // Then
        logger.info("Concurrent batch insert test completed:");
        logger.info("- Total threads: {}", threadCount);
        logger.info("- Batch size per thread: {}", batchSizePerThread);
        logger.info("- Success count: {}", successCount.get());
        logger.info("- Failure count: {}", failureCount.get());
        logger.info("- Total duration: {}ms", totalDuration);
        logger.info("- Average thread duration: {}ms", totalTime.get() / threadCount);
        logger.info("- Throughput: {} operations/second", 
            (threadCount * batchSizePerThread * 1000.0) / totalDuration);
        
        // 验证结果
        assertTrue(successCount.get() > 0, "至少应有部分操作成功");
        assertTrue(totalDuration < 30000, "总执行时间不应超过30秒");
        
        executorService.shutdown();
    }
    
    /**
     * 并发更新测试
     * 测试对相同数据的并发更新操作
     */
    @Test
    void testConcurrentBatchUpdate() throws Exception {
        // Given
        int threadCount = 20;
        int recordsPerThread = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        
        // 先插入一些测试数据
        setupTestData(threadCount * recordsPerThread);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicLong totalTime = new AtomicLong(0);
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        // When
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    BatchOperationRequest request = createBatchUpdateRequest(threadIndex, recordsPerThread);
                    
                    long threadStartTime = System.currentTimeMillis();
                    BatchOperationResponse response = batchOperationService.processBatchOperation(request);
                    long threadEndTime = System.currentTimeMillis();
                    
                    totalTime.addAndGet(threadEndTime - threadStartTime);
                    
                    if ("SUCCESS".equals(response.getStatus()) || "PARTIAL_SUCCESS".equals(response.getStatus())) {
                        successCount.incrementAndGet();
                        logger.info("Thread {} update completed: success={}, failed={}, time={}ms", 
                            threadIndex, response.getSuccessCount(), response.getFailedCount(),
                            threadEndTime - threadStartTime);
                    } else {
                        failureCount.incrementAndGet();
                        logger.error("Thread {} update failed: {}", threadIndex, response.getMessage());
                    }
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    logger.error("Thread {} update encountered exception", threadIndex, e);
                }
            }, executorService);
            
            futures.add(future);
        }
        
        // Wait for completion
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(60, TimeUnit.SECONDS);
        
        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;
        
        // Then
        logger.info("Concurrent batch update test completed:");
        logger.info("- Total threads: {}", threadCount);
        logger.info("- Records per thread: {}", recordsPerThread);
        logger.info("- Success count: {}", successCount.get());
        logger.info("- Failure count: {}", failureCount.get());
        logger.info("- Total duration: {}ms", totalDuration);
        logger.info("- Average thread duration: {}ms", totalTime.get() / threadCount);
        
        // 验证没有死锁发生
        assertTrue(successCount.get() > 0, "应有部分更新操作成功");
        assertTrue(totalDuration < 45000, "总执行时间不应超过45秒，可能存在死锁");
        
        executorService.shutdown();
    }
    
    /**
     * 混合操作压力测试
     * 同时执行插入、更新、删除操作
     */
    @Test
    void testMixedOperationStressTest() throws Exception {
        // Given
        int insertThreads = 5;
        int updateThreads = 5;
        int deleteThreads = 3;
        int operationsPerThread = 50;
        
        ExecutorService executorService = Executors.newFixedThreadPool(insertThreads + updateThreads + deleteThreads);
        
        AtomicInteger totalSuccess = new AtomicInteger(0);
        AtomicInteger totalFailure = new AtomicInteger(0);
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        // 准备测试数据
        setupTestData(updateThreads * operationsPerThread + deleteThreads * operationsPerThread);
        
        // When
        long startTime = System.currentTimeMillis();
        
        // Insert threads
        for (int i = 0; i < insertThreads; i++) {
            final int threadIndex = i;
            futures.add(CompletableFuture.runAsync(() -> {
                executeOperationSafely("INSERT", threadIndex, operationsPerThread, totalSuccess, totalFailure);
            }, executorService));
        }
        
        // Update threads
        for (int i = 0; i < updateThreads; i++) {
            final int threadIndex = insertThreads + i;
            futures.add(CompletableFuture.runAsync(() -> {
                executeOperationSafely("UPDATE", threadIndex, operationsPerThread, totalSuccess, totalFailure);
            }, executorService));
        }
        
        // Delete threads
        for (int i = 0; i < deleteThreads; i++) {
            final int threadIndex = insertThreads + updateThreads + i;
            futures.add(CompletableFuture.runAsync(() -> {
                executeOperationSafely("DELETE", threadIndex, operationsPerThread, totalSuccess, totalFailure);
            }, executorService));
        }
        
        // Wait for completion
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(120, TimeUnit.SECONDS);
        
        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;
        
        // Then
        int totalOperations = (insertThreads + updateThreads + deleteThreads) * operationsPerThread;
        
        logger.info("Mixed operation stress test completed:");
        logger.info("- Insert threads: {}, Update threads: {}, Delete threads: {}", 
            insertThreads, updateThreads, deleteThreads);
        logger.info("- Operations per thread: {}", operationsPerThread);
        logger.info("- Total operations: {}", totalOperations);
        logger.info("- Total success: {}", totalSuccess.get());
        logger.info("- Total failure: {}", totalFailure.get());
        logger.info("- Success rate: {:.2f}%", (totalSuccess.get() * 100.0) / totalOperations);
        logger.info("- Total duration: {}ms", totalDuration);
        logger.info("- Throughput: {:.2f} operations/second", (totalOperations * 1000.0) / totalDuration);
        
        // 验证
        assertTrue(totalSuccess.get() > totalOperations * 0.8, "成功率应超过80%");
        assertTrue(totalDuration < 120000, "总执行时间不应超过2分钟");
        
        executorService.shutdown();
    }
    
    // 辅助方法
    private BatchOperationRequest createBatchInsertRequest(int threadIndex, int batchSize) {
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType("INSERT");
        request.setDataType("STRESS_TEST");
        request.setBatchNo("STRESS_INSERT_" + threadIndex + "_" + System.currentTimeMillis());
        
        List<BatchOperationRequest.BatchDataItem> items = new ArrayList<>();
        for (int i = 0; i < batchSize; i++) {
            BatchOperationRequest.BatchDataItem item = new BatchOperationRequest.BatchDataItem();
            item.setBusinessData("Thread " + threadIndex + " - Item " + i + " - " + System.currentTimeMillis());
            item.setExtraData("{\"threadIndex\":" + threadIndex + ",\"itemIndex\":" + i + "}");
            items.add(item);
        }
        
        request.setDataItems(items);
        return request;
    }
    
    private BatchOperationRequest createBatchUpdateRequest(int threadIndex, int recordCount) {
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType("UPDATE");
        request.setDataType("STRESS_TEST");
        request.setBatchNo("STRESS_UPDATE_" + threadIndex + "_" + System.currentTimeMillis());
        
        List<BatchOperationRequest.BatchDataItem> items = new ArrayList<>();
        for (int i = 0; i < recordCount; i++) {
            BatchOperationRequest.BatchDataItem item = new BatchOperationRequest.BatchDataItem();
            // 模拟更新已存在的记录
            item.setId((long) (threadIndex * recordCount + i + 1));
            item.setBusinessData("Updated by thread " + threadIndex + " - " + System.currentTimeMillis());
            item.setVersion(1);
            items.add(item);
        }
        
        request.setDataItems(items);
        return request;
    }
    
    private void setupTestData(int recordCount) {
        // 这里应该插入测试数据到数据库
        // 由于是示例代码，这里只做日志记录
        logger.info("Setting up {} test records", recordCount);
    }
    
    private void executeOperationSafely(String operationType, int threadIndex, int operationsPerThread,
                                      AtomicInteger totalSuccess, AtomicInteger totalFailure) {
        try {
            BatchOperationRequest request;
            switch (operationType) {
                case "INSERT":
                    request = createBatchInsertRequest(threadIndex, operationsPerThread);
                    break;
                case "UPDATE":
                    request = createBatchUpdateRequest(threadIndex, operationsPerThread);
                    break;
                case "DELETE":
                    request = createBatchDeleteRequest(threadIndex, operationsPerThread);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported operation: " + operationType);
            }
            
            BatchOperationResponse response = batchOperationService.processBatchOperation(request);
            
            if ("SUCCESS".equals(response.getStatus()) || "PARTIAL_SUCCESS".equals(response.getStatus())) {
                totalSuccess.incrementAndGet();
            } else {
                totalFailure.incrementAndGet();
            }
            
        } catch (Exception e) {
            totalFailure.incrementAndGet();
            logger.error("Operation {} in thread {} failed", operationType, threadIndex, e);
        }
    }
    
    private BatchOperationRequest createBatchDeleteRequest(int threadIndex, int recordCount) {
        BatchOperationRequest request = new BatchOperationRequest();
        request.setOperationType("DELETE");
        request.setDataType("STRESS_TEST");
        request.setBatchNo("STRESS_DELETE_" + threadIndex + "_" + System.currentTimeMillis());
        
        List<BatchOperationRequest.BatchDataItem> items = new ArrayList<>();
        for (int i = 0; i < recordCount; i++) {
            BatchOperationRequest.BatchDataItem item = new BatchOperationRequest.BatchDataItem();
            // 模拟删除已存在的记录
            item.setId((long) (threadIndex * recordCount + i + 1000));
            items.add(item);
        }
        
        request.setDataItems(items);
        return request;
    }
}