-- 高并发批量操作数据表
-- 用于演示高并发操作和死锁防范

CREATE TABLE IF NOT EXISTS `batch_operation_data` (
    `id` BIGINT NOT NULL PRIMARY KEY COMMENT '主键ID',
    `business_data` TEXT COMMENT '业务数据',
    `status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '数据状态：PENDING, PROCESSING, COMPLETED, FAILED',
    `data_type` VARCHAR(50) NOT NULL COMMENT '数据类型',
    `batch_no` VARCHAR(100) NOT NULL COMMENT '批次号',
    `priority` INT NOT NULL DEFAULT 1 COMMENT '处理优先级，数字越大越优先',
    `extra_data` JSON COMMENT '扩展数据，JSON格式',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` BIGINT COMMENT '创建用户ID',
    `update_user` BIGINT COMMENT '更新用户ID',
    `version` INT NOT NULL DEFAULT 1 COMMENT '版本号，用于乐观锁'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量操作数据表';

-- 创建索引，优化查询性能并减少死锁概率
-- 主键索引已自动创建：PRIMARY KEY (`id`)

-- 批次号索引，用于批次查询
CREATE INDEX `idx_batch_no` ON `batch_operation_data` (`batch_no`);

-- 状态和类型复合索引，用于状态查询
CREATE INDEX `idx_status_type` ON `batch_operation_data` (`status`, `data_type`);

-- 优先级和创建时间复合索引，用于排序查询
CREATE INDEX `idx_priority_create_time` ON `batch_operation_data` (`priority` DESC, `create_time` ASC);

-- 创建时间索引，用于时间范围查询
CREATE INDEX `idx_create_time` ON `batch_operation_data` (`create_time`);

-- 更新时间索引，用于同步和清理
CREATE INDEX `idx_update_time` ON `batch_operation_data` (`update_time`);

-- 用户相关索引
CREATE INDEX `idx_create_user` ON `batch_operation_data` (`create_user`);

-- 创建演示用的测试数据表
CREATE TABLE IF NOT EXISTS `high_concurrency_test` (
    `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `test_name` VARCHAR(100) NOT NULL COMMENT '测试名称',
    `test_value` VARCHAR(500) COMMENT '测试值',
    `counter` INT NOT NULL DEFAULT 0 COMMENT '计数器，用于并发测试',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` INT NOT NULL DEFAULT 1 COMMENT '版本号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='高并发测试表';

-- 测试表索引
CREATE INDEX `idx_test_name` ON `high_concurrency_test` (`test_name`);
CREATE INDEX `idx_status` ON `high_concurrency_test` (`status`);
CREATE INDEX `idx_create_time_test` ON `high_concurrency_test` (`create_time`);

-- 创建死锁测试表1
CREATE TABLE IF NOT EXISTS `deadlock_test_table1` (
    `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `ref_id` BIGINT NOT NULL COMMENT '关联ID',
    `data_value` VARCHAR(255) COMMENT '数据值',
    `update_count` INT NOT NULL DEFAULT 0 COMMENT '更新次数',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='死锁测试表1';

-- 创建死锁测试表2
CREATE TABLE IF NOT EXISTS `deadlock_test_table2` (
    `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `ref_id` BIGINT NOT NULL COMMENT '关联ID',
    `data_value` VARCHAR(255) COMMENT '数据值',
    `update_count` INT NOT NULL DEFAULT 0 COMMENT '更新次数',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='死锁测试表2';

-- 添加外键约束（可能产生死锁的场景）
CREATE INDEX `idx_ref_id_1` ON `deadlock_test_table1` (`ref_id`);
CREATE INDEX `idx_ref_id_2` ON `deadlock_test_table2` (`ref_id`);

-- 插入一些初始测试数据
INSERT INTO `high_concurrency_test` (`test_name`, `test_value`, `counter`) VALUES
('concurrency_test_1', 'Initial Value 1', 0),
('concurrency_test_2', 'Initial Value 2', 0),
('concurrency_test_3', 'Initial Value 3', 0),
('concurrency_test_4', 'Initial Value 4', 0),
('concurrency_test_5', 'Initial Value 5', 0);

-- 插入死锁测试数据
INSERT INTO `deadlock_test_table1` (`ref_id`, `data_value`) VALUES
(1, 'Table1 - Ref 1'),
(2, 'Table1 - Ref 2'),
(3, 'Table1 - Ref 3'),
(4, 'Table1 - Ref 4'),
(5, 'Table1 - Ref 5');

INSERT INTO `deadlock_test_table2` (`ref_id`, `data_value`) VALUES
(1, 'Table2 - Ref 1'),
(2, 'Table2 - Ref 2'),
(3, 'Table2 - Ref 3'),
(4, 'Table2 - Ref 4'),
(5, 'Table2 - Ref 5');

-- 设置MySQL参数优化（需要DBA权限执行）
-- 这些设置有助于减少死锁的发生

-- 设置锁等待超时时间（默认50秒，建议设置为10秒）
-- SET GLOBAL innodb_lock_wait_timeout = 10;

-- 设置死锁检测（建议开启）
-- SET GLOBAL innodb_deadlock_detect = ON;

-- 设置事务隔离级别为READ-COMMITTED（可以减少间隙锁）
-- SET GLOBAL transaction_isolation = 'READ-COMMITTED';

-- 优化InnoDB缓冲池大小（建议设置为总内存的70-80%）
-- SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB

-- 设置日志文件大小
-- SET GLOBAL innodb_log_file_size = 268435456; -- 256MB

-- 查询死锁信息的SQL（用于监控）
-- SELECT * FROM INFORMATION_SCHEMA.INNODB_TRX;
-- SELECT * FROM INFORMATION_SCHEMA.INNODB_LOCKS;
-- SELECT * FROM INFORMATION_SCHEMA.INNODB_LOCK_WAITS;
-- SHOW ENGINE INNODB STATUS;