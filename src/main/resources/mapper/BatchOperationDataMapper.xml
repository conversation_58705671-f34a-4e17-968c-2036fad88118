<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BatchOperationDataMapper">

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO batch_operation_data (
            id, business_data, status, data_type, batch_no, priority, 
            extra_data, create_time, update_time, create_user, update_user, version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.businessData}, #{item.status}, #{item.dataType}, 
                #{item.batchNo}, #{item.priority}, #{item.extraData}, 
                #{item.createTime}, #{item.updateTime}, #{item.createUser}, 
                #{item.updateUser}, #{item.version}
            )
        </foreach>
    </insert>

    <!-- 按ID列表批量更新状态，按ID排序避免死锁 -->
    <update id="batchUpdateStatusByIds">
        UPDATE batch_operation_data 
        SET status = #{status}, 
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id IN 
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY id
    </update>

    <!-- 按ID列表批量更新业务数据 -->
    <update id="batchUpdateByIds">
        <foreach collection="list" item="item" separator=";">
            UPDATE batch_operation_data 
            SET business_data = #{item.businessData},
                extra_data = #{item.extraData},
                update_time = NOW(),
                update_user = #{item.updateUser},
                version = version + 1
            WHERE id = #{item.id} 
            AND version = #{item.version}
        </foreach>
    </update>

    <!-- 按ID列表批量删除，按ID排序避免死锁 -->
    <delete id="batchDeleteByIds">
        DELETE FROM batch_operation_data 
        WHERE id IN 
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY id
    </delete>

    <!-- 按批次号查询数据 -->
    <select id="selectByBatchNo" resultType="com.unipus.digitalbook.model.entity.highconcurrency.BatchOperationData">
        SELECT * FROM batch_operation_data 
        WHERE batch_no = #{batchNo}
        ORDER BY id
    </select>

    <!-- 按状态和类型查询数据（分页） -->
    <select id="selectByStatusAndType" resultType="com.unipus.digitalbook.model.entity.highconcurrency.BatchOperationData">
        SELECT * FROM batch_operation_data 
        WHERE 1=1
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type = #{dataType}
        </if>
        ORDER BY priority DESC, create_time ASC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计各状态的记录数 -->
    <select id="countByStatus" resultType="com.unipus.digitalbook.dao.BatchOperationDataMapper$StatusCount">
        SELECT status, COUNT(*) as count
        FROM batch_operation_data 
        <where>
            <if test="batchNo != null and batchNo != ''">
                batch_no = #{batchNo}
            </if>
        </where>
        GROUP BY status
    </select>

</mapper>