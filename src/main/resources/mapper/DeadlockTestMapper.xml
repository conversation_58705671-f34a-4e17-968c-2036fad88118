<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.DeadlockTestMapper">

    <!-- 模拟死锁场景1：先更新table1再更新table2 -->
    <update id="simulateDeadlockScenario1">
        <!-- 先锁table1 -->
        UPDATE deadlock_test_table1 
        SET data_value = #{value1}, 
            update_count = update_count + 1,
            update_time = NOW()
        WHERE ref_id = #{refId};
        
        <!-- 模拟一些处理时间 -->
        SELECT SLEEP(0.01);
        
        <!-- 再锁table2 -->
        UPDATE deadlock_test_table2 
        SET data_value = #{value2}, 
            update_count = update_count + 1,
            update_time = NOW()
        WHERE ref_id = #{refId};
    </update>

    <!-- 模拟死锁场景2：先更新table2再更新table1 -->
    <update id="simulateDeadlockScenario2">
        <!-- 先锁table2 -->
        UPDATE deadlock_test_table2 
        SET data_value = #{value2}, 
            update_count = update_count + 1,
            update_time = NOW()
        WHERE ref_id = #{refId};
        
        <!-- 模拟一些处理时间 -->
        SELECT SLEEP(0.01);
        
        <!-- 再锁table1 -->
        UPDATE deadlock_test_table1 
        SET data_value = #{value1}, 
            update_count = update_count + 1,
            update_time = NOW()
        WHERE ref_id = #{refId};
    </update>

    <!-- 安全的批量更新表1（按ID排序避免死锁） -->
    <update id="safeBatchUpdateTable1">
        UPDATE deadlock_test_table1 
        SET data_value = #{dataValue}, 
            update_count = update_count + 1,
            update_time = NOW()
        WHERE id IN 
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY id
    </update>

    <!-- 安全的批量更新表2（按ID排序避免死锁） -->
    <update id="safeBatchUpdateTable2">
        UPDATE deadlock_test_table2 
        SET data_value = #{dataValue}, 
            update_count = update_count + 1,
            update_time = NOW()
        WHERE id IN 
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ORDER BY id
    </update>

    <!-- 原子性地更新两个表（按固定顺序避免死锁） -->
    <update id="atomicUpdateBothTables">
        <!-- 始终先更新table1，再更新table2，保证顺序一致 -->
        UPDATE deadlock_test_table1 
        SET data_value = #{value1}, 
            update_count = update_count + 1,
            update_time = NOW()
        WHERE ref_id = #{refId};
        
        UPDATE deadlock_test_table2 
        SET data_value = #{value2}, 
            update_count = update_count + 1,
            update_time = NOW()
        WHERE ref_id = #{refId};
    </update>

    <!-- 获取表1数据 -->
    <select id="getTable1ByRefId" resultType="com.unipus.digitalbook.model.entity.highconcurrency.DeadlockTestTable1">
        SELECT * FROM deadlock_test_table1 WHERE ref_id = #{refId}
    </select>

    <!-- 获取表2数据 -->
    <select id="getTable2ByRefId" resultType="com.unipus.digitalbook.model.entity.highconcurrency.DeadlockTestTable2">
        SELECT * FROM deadlock_test_table2 WHERE ref_id = #{refId}
    </select>

</mapper>