package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.common.utils.JwtUtil;
import com.unipus.digitalbook.conf.security.JwtProperties;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.constants.WebConstant;
import com.unipus.digitalbook.model.dto.user.BackendUserDTO;
import com.unipus.digitalbook.model.dto.user.LoginUserDTO;
import com.unipus.digitalbook.model.entity.BackendUserInfo;
import com.unipus.digitalbook.model.entity.CurrentUserInfo;
import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import com.unipus.digitalbook.model.enums.TenantExistFlagEnum;
import com.unipus.digitalbook.model.params.DoAuthParam;
import com.unipus.digitalbook.service.*;
import com.unipus.digitalbook.service.remote.restful.sso.CasResponseParser;
import com.unipus.digitalbook.service.remote.restful.sso.SsoService;
import com.unipus.digitalbook.service.remote.restful.sso.response.SsoResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/10/16 下午5:09
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Value("${remote.sso.serviceName}")
    String serviceName;

    @Resource
    CasResponseParser casResponseParser;
    @Resource
    SsoService ssoService;
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private UserService userService;
    @Resource
    StringRedisTemplate stringRedisTemplate;
    @Resource
    private BookService bookService;

    @Resource
    private TenantService tenantService;

    @Resource
    private ThirdPartyUserService thirdPartyUserService;
    @Resource
    private JwtProperties jwtProperties;


    /**
     * 进行身份验证并获取服务票据（Service Ticket）授权。
     *
     * @param serviceTicket        服务票据，通常由客户端在访问服务时提供，用于验证用户身份和授权信息。
     * @param ticketGrantingTicket 票据授权票据，通常由CAS服务器在用户首次登录时生成，并用于后续的服务票据生成和验证。
     * @return Response 返回一个Response对象，其中包含了身份验证和授权的结果。
     * <p>
     * - 如果身份验证和授权成功，Response对象的状态码（code）将为成功状态（如200），响应消息（message）将为成功消息，响应数据（data）可能包含授权后的用户信息或其他相关数据。
     * - 如果身份验证或授权失败，Response对象的状态码将为失败状态（如401或403），响应消息将为失败原因，响应数据可能为null或包含错误详细信息。
     */
    @Override
    public Response<LoginUserDTO> auth(String serviceTicket, String ticketGrantingTicket) {
        String  result = ssoService.validateServiceTicket( serviceName,serviceTicket);
        log.debug(result);
        if (!StringUtils.hasText(result)){
            return Response.fail("sso服务返回异常");
        }
        SsoResponse response = casResponseParser.processResponse(result);
        if (Boolean.FALSE.equals(response.getStatus())){
            return Response.fail(response.getMsg());
        }
        if (response.getRs()==null||response.getRs().getAttributes()==null){
            return Response.fail("sso服务返回数据异常");
        }
        String ssoId = response.getRs().getAttributes().getUserid();
        if (!StringUtils.hasText(ssoId)) {
            return Response.fail("sso服务返回数据异常,无ssoId");
        }
        String mobile = response.getRs().getAttributes().getMobile();
        if (!StringUtils.hasText(mobile)) {
            return Response.fail("sso服务返回数据异常,无mobileNumber");
        }

        // 取得用户信息（用户/组织/角色信息）
        Response<CurrentUserInfo> resp = userService.getLoginUserInfo(ssoId, mobile);
        if(Boolean.FALSE.equals(resp.isSuccess())){
            return Response.fail(resp.getMessage());
        }
        CurrentUserInfo currentUserInfo = resp.getData();
        // 取得用户ID
        Long userId = currentUserInfo.getUserInfo().getId();

        // 首次登录激活用户
        if(Boolean.FALSE.equals(currentUserInfo.getActive())) {
            Boolean success = userService.activateUser(userId, ssoId);
            if (Boolean.FALSE.equals(success)) {
                return Response.fail("绑定失败，请联系系统管理员。");
            }
        }

        // 生成服务票据 存到redis里
        stringRedisTemplate.opsForValue().set(CacheConstant.REDIS_AUTH_PREFIX+userId,
                serviceTicket, jwtProperties.getExpiration(), TimeUnit.DAYS);

        // 缓存用户信息 到redis中
        stringRedisTemplate.opsForValue().set(CacheConstant.REDIS_USR_PREFIX+userId,
                JsonUtil.toJsonString(currentUserInfo), jwtProperties.getExpiration(), TimeUnit.DAYS);

        // 开始生成jwt 构建jwt内的信息
        Map<String,Object> claims=new HashMap<>();
        claims.put(WebConstant.JWT_SERVICE_TICKET,serviceTicket);
        claims.put(WebConstant.JWT_USER_ID,userId);

        // 生成JWT token
        String token = jwtUtil.generateToken(
                claims,
                mobile
        );

        // 返回用户信息
        return Response.success(LoginUserDTO.build(currentUserInfo, token));
    }


    /**
     * 验证登录参数的有效性
     *
     * @param param 登录参数对象，包含服务票据和授权票据
     * @return 响应对象，包含状态码、响应消息和响应数据
     */
    @Override
    public Response<LoginUserDTO> validLogin(DoAuthParam param) {
        // 检查服务票据和授权票据是否为空或空白字符串
        if (!StringUtils.hasText(param.getServiceTicket()) || !StringUtils.hasText(param.getTicketGrantingTicket())) {
            // 如果任一票据为空或空白，返回失败的响应对象，状态码和响应消息由fail方法设置
            return Response.fail("参数校验未通过");
        }
        Response<LoginUserDTO> response = auth(param.getServiceTicket(), param.getTicketGrantingTicket());
        if (Boolean.TRUE.equals(response.isSuccess())) {
            return response;
        }
        return Response.fail(response.getCode(), response.getMessage());
    }

    @Override
    public Response<String> generateToken4CoEdit(Long userId, String bookId) {
        if (bookId == null) {
            throw new IllegalArgumentException("bookId is null");
        }
        if  (userId == null) {
            return Response.fail("协同认证，未获取到用户信息。");
        }
        UserInfo userInfo =  userService.getUserInfo(userId);
        if (userInfo == null) {
            return Response.fail("未获取到用户信息。");
        }
        List<String> chapterIds = bookService.getHasEditChapterIdsByBookId(userId, bookId);
        if (chapterIds.isEmpty()) {
            chapterIds = new ArrayList<>();
        }
        chapterIds.add("catalog");
        chapterIds.add("paper");
        chapterIds.add("examination");
        Map<String,Object> claims = new HashMap<>();
        claims.put(WebConstant.JWT_BOOK_ID, bookId);
        claims.put(WebConstant.JWT_CHAPTER_ID, chapterIds);
        claims.put(WebConstant.JWT_USER_ID, userId);
        claims.put(WebConstant.JWT_USER_NAME, userInfo.getName());
        String token = jwtUtil.generateCoToken(claims, bookId);
        return Response.success(token);
    }

    /**
     * 获取后端用户token
     *
     * @param param 内部认证参数类
     * @return 后端用户token
     */
    @Override
    public Response<BackendUserDTO> getBackendAccessToken(BackendUserInfo param) {
        // 校验租户 ID 的合法性
        TenantExistFlagEnum tenantExist = tenantService.checkTenantExist(param.getAppId());
        if (!TenantExistFlagEnum.EXIST.equals(tenantExist)) {
            return Response.fail("租户不存在");
        }

        // 教师身份则获取第三方用户信息（不存在则创建）
        if (ReaderTypeEnum.TEACHER.equals(param.getReaderType())) {
            ThirdPartyUserInfo userInfo = thirdPartyUserService.getUserInfoByOpenId(param.getAppId(), param.getOpenId());
            if (Objects.isNull(userInfo)) {
                return Response.fail("用户信息获取失败");
            }
        }

        // 生成访问ID
        String accessId = IdentifierUtil.getShortUUID();
        // 缓存用户信息
        stringRedisTemplate.opsForValue().set(CacheConstant.REDIS_PLATFORM_USR_PREFIX+accessId,
                JsonUtil.toJsonString(param), jwtProperties.getExpiration(), TimeUnit.DAYS);
        // 生成访问令牌
        Map<String,Object> claims = new HashMap<>();
        claims.put(WebConstant.JWT_BACKEND_AID, param.getAppId());
        claims.put(WebConstant.JWT_BACKEND_SID, accessId);
        claims.put(WebConstant.JWT_BACKEND_OID, param.getOpenId());
        claims.put(WebConstant.JWT_BACKEND_RID, param.getReaderType().getCode());
        claims.put(WebConstant.JWT_ENV_PID, param.getEnvPartition());
        String token = jwtUtil.generateToken(claims, param.getOpenId());
        // 返回用户信息
        return Response.success(new BackendUserDTO(token));
    }

    /**
     * 解析后端用户 token
     *
     * @param token 后端用户 token
     * @return 后端用户信息
     */
    @Override
    public Response<BackendUserInfo> parseBackendAccessToken(String token) {
        if (!StringUtils.hasText(token)) {
            return Response.fail("token 不能为空");
        }

        // 去除自定义前缀
        if (token.startsWith(WebConstant.HEADER_TOKEN_TYPE)) {
            token = token.substring(WebConstant.HEADER_TOKEN_TYPE.length()).trim();
        }

        // 解析 token，提取访问 ID
        Jwt jwt = jwtUtil.parseToken(token);
        String accessId = Optional.ofNullable(jwt)
                .map(Jwt::getClaims)
                .map(claims -> claims.get(WebConstant.JWT_BACKEND_SID))
                .map(Object::toString)
                .orElse(null);
        if (!StringUtils.hasText(accessId)) {
            return Response.fail("token 解析失败");
        }

        // 从 Redis 获取用户信息
        String userInfoStr = stringRedisTemplate.opsForValue().get(CacheConstant.REDIS_PLATFORM_USR_PREFIX + accessId);
        if (!StringUtils.hasText(userInfoStr)) {
            return Response.fail("token 已失效");
        }

        BackendUserInfo userInfo = JsonUtil.parseObject(userInfoStr, BackendUserInfo.class);
        return Response.success(userInfo);
    }

}
