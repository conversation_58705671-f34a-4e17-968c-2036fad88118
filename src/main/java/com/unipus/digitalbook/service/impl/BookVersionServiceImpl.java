package com.unipus.digitalbook.service.impl;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.util.StringUtils;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.BookVersionPOMapper;
import com.unipus.digitalbook.model.constants.CommonConstant;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.BookID;
import com.unipus.digitalbook.model.entity.publish.BookPublishedVersion;
import com.unipus.digitalbook.model.entity.publish.BookPublishedVersionList;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.params.publish.BookPublishedVersionParam;
import com.unipus.digitalbook.model.po.publish.BookVersionPO;
import com.unipus.digitalbook.service.BookVersionService;
import com.unipus.digitalbook.service.UserService;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 教材版本服务实现类
 */
@Service
@Slf4j
public class BookVersionServiceImpl implements BookVersionService {

    @Resource
    private BookVersionPOMapper bookVersionPOMapper;
    @Resource
    private UserService userService;
    @Resource(name = "bookVersionCache")
    private LoadingCache<BookID, BookVersion> bookVersionCache;

    /**
     * 根据教材ID 获取最新上架的版本信息
     *
     * @param bookId 教材ID
     * @return 最新上架的版本信息
     */
    @Override
    public BookVersion getBookLastPublishedVersion(String bookId) {
        BookVersionPO bookVersionPO = bookVersionPOMapper.selectLatestPublishedBookVersionByBookId(bookId);
        if (bookVersionPO == null){
            logBookVersionEmpty(bookId);
            return null;
        }
        return bookVersionPO.toEntity();
    }

    /**
     * 根据教材ID和版本号 获取上架版本信息
     *
     * @param bookId     教材ID
     * @param versionNum 版本号
     * @return 上架版本信息
     */
    @Override
    public @Nullable BookVersion getBookVersionByBookIdAndVersion(String bookId, String versionNum) {
        return bookVersionCache.get(new BookID(bookId, versionNum));
    }

    @Override
    public BookVersion getBookVersionById(Long id) {
        BookVersionPO bookVersionPO = bookVersionPOMapper.selectById(id);
        if (bookVersionPO != null){
            return bookVersionPO.toEntity();
        }
        return null;
    }

    @Override
    public List<BookVersion> getAllBookVersions() {
        List<BookVersionPO> bookVersionPOS = bookVersionPOMapper.selectAll();
        if(CollectionUtils.isEmpty(bookVersionPOS)){
            return List.of();
        }
        return bookVersionPOS.stream().map(BookVersionPO::toEntity).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addBookVersion(BookVersion bookVersion) {
        BookVersionPO bookVersionPO = new BookVersionPO(bookVersion);
        // 设置显示版本号和排序
        if (bookVersionPO.getVersionNum() != null && bookVersionPO.getShowVersionNumber() == null) {
            BookVersionPO lastPublishedVersionPO = bookVersionPOMapper.selectLatestPublishedBookVersionByBookId(bookVersionPO.getBookId());
            Integer sortOrder = lastPublishedVersionPO != null ? lastPublishedVersionPO.getSortOrder() + 1 : 1;
            long versionNumberTimestamp = IdentifierUtil.parseVersion(bookVersionPO.getVersionNum());
            String showVersionNumber = MessageFormat.format("V{0}-{1}", new SimpleDateFormat("yyyyMMdd")
                    .format(new Date(versionNumberTimestamp)), sortOrder);
            bookVersionPO.setShowVersionNumber(showVersionNumber);
            bookVersionPO.setSortOrder(sortOrder);
        }
        int insert = bookVersionPOMapper.insert(bookVersionPO);
        if (insert > 0) {
            return bookVersionPO.getId();
        } else {
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBookVersion(BookVersion bookVersion) {
        int result = bookVersionPOMapper.updateById(new BookVersionPO(bookVersion));
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBookVersionById(Long id) {
        int result = bookVersionPOMapper.deleteById(id);
        return result > 0;
    }

    /**
     * 根据条件查询教材所有已上架版版本信息
     *
     * @param param 查询条件
     * @return 教材版本信息
     */
    @Override
    public BookPublishedVersionList getBookPublishedVersion(BookPublishedVersionParam param) {
        String bookId = param.getBookId();
        Date publishStartTime = param.getPublishStartTime() != null ? new Date(param.getPublishStartTime()) : null;
        Date publishEndTime = param.getPublishEndTime() != null ? new Date(param.getPublishEndTime()) : null;
        // 取得教材提交上架信息列表（按照提交时间倒序排列）
        Integer count = bookVersionPOMapper.selectBookPublishedVersionCount(bookId, publishStartTime, publishEndTime);
        if (count <= 0) {
            return new BookPublishedVersionList(List.of(), 0);
        }
        List<BookVersionPO> bookVersionPOs = bookVersionPOMapper.selectBookPublishedVersion(bookId, publishStartTime, publishEndTime, param.getPageParams());
        List<Long> creatorIdList = bookVersionPOs.stream().map(BookVersionPO::getCreateBy).distinct().toList();
        Map<Long, UserInfo> userMap = userService.getUserMap(creatorIdList);
        List<BookPublishedVersion> versionList = bookVersionPOs.stream().map(bookVersionPO -> {
            UserInfo userInfo = userMap.get(bookVersionPO.getCreateBy());
            return new BookPublishedVersion(bookVersionPO.toEntity(), userInfo != null ? userInfo.getName() : null);
        }).toList();
        return new BookPublishedVersionList(versionList, count);
    }

    /**
     * 根据 教材ID 查询教材所有已上架版版本信息
     *
     * @param bookId 教材ID
     * @return 教材版本信息
     */
    @Override
    public List<BookVersion> getBookPublishedVersion(String bookId) {
        // 取得教材提交上架信息列表（按照提交时间倒序排列）
        List<BookVersionPO> bookVersionPOs = bookVersionPOMapper.selectBookPublishedVersionByBookId(bookId);
        if (CollectionUtils.isEmpty(bookVersionPOs)) {
            logBookVersionEmpty(bookId);
            return Collections.emptyList();
        }
        return bookVersionPOs.stream().map(BookVersionPO::toEntity).toList();
    }

    /**
     * 根据教材ID，获取教材已上架版本的次数
     *
     * @param bookId 教材ID
     * @return 教材已上架版本的次数
     */
    @Override
    public Integer getBookPublishedVersionCount(String bookId) {
        return bookVersionPOMapper.selectBookPublishedVersionCountByBookId(bookId);
    }

    /**
     * 根据教材ID列表，获取教材是否有已上架版本
     *
     * @param bookIds 教材ID列表
     * @return 教材是否有已上架版本
     */
    @Override
    public Map<String, Boolean> isBookPublishedVersion(List<String> bookIds) {
        if (bookIds == null || bookIds.isEmpty()) {
            throw new IllegalArgumentException("bookIds cannot be empty");
        }
        List<Map<String, Object>> results = bookVersionPOMapper.selectIsBookPublishedVersionByBookId(bookIds);
        return results.stream().collect(Collectors.toMap(
                result -> (String) result.get(CommonConstant.BOOK_ID_FIELD),
                result -> (Boolean) result.get("isPublished")
        ));
    }

    @Override
    public Map<String, Long> bookMaxPublishedTime(List<String> bookIds) {
        if (bookIds == null || bookIds.isEmpty()) {
            throw new IllegalArgumentException("bookIds cannot be empty");
        }

        List<Map<String, String>> results = bookVersionPOMapper.selectBookMaxPublishedTimeByBookId(bookIds);
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyMap();
        }

        Map<String, Long> bookPublishMap = new HashMap<>();
        for (Map<String, String> result : results) {
            String maxCreateTime = result.get("maxCreateTime");
            if (StringUtils.isBlank(maxCreateTime)) {
                continue;
            }

            try {
                long time = DateUtils.parseDate(result.get("maxCreateTime"), "yyyy-MM-dd HH:mm:ss").getTime();
                bookPublishMap.put(result.get(CommonConstant.BOOK_ID_FIELD), time);
            } catch (ParseException e) {
                log.warn("教材版本时间解析失败，教材ID:{}，错误信息:{}", result.get(CommonConstant.BOOK_ID_FIELD), maxCreateTime);
            }
        }

        return bookPublishMap;
    }

    private static final String BOOK_VERSION_EMPTY_LOG = "教材版本信息为空，教材ID:{}";

    private void logBookVersionEmpty(String bookId) {
        log.info(BOOK_VERSION_EMPTY_LOG, bookId);
    }

    /**
     * 根据版本id查询上一个版本
     *
     * @param id 上架版本 ID
     * @return 教材版本信息
     */
    @Override
    public BookVersion getBookPreviousVersionById(Long id) {
        BookVersionPO bookVersionPO = bookVersionPOMapper.selectPreviousVersionById(id);
        if (bookVersionPO != null) {
            return bookVersionPO.toEntity();
        }
        return null;
    }

    /**
     * 根据版本id，获取教材上架版本顺序
     *
     * @param id 上架版本 ID
     * @return 教材上架版本顺序
     */
    @Override
    public Integer getBookPublishedVersionSortOrder(Long id) {
        return bookVersionPOMapper.selectBookPublishedVersionSortOrderById(id);
    }

    @Override
    public BookVersion getBookVersionByChapterVersionId(Long chapterVersionId) {
        if  (chapterVersionId == null) {
            return null;
        }
        BookVersionPO bookVersionPO = bookVersionPOMapper.getBookVersionByChapterVersionId(chapterVersionId);
        if (bookVersionPO == null) {
            return null;
        }
        return bookVersionPO.toEntity();
    }
}
