package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.common.utils.RedisUtil;
import com.unipus.digitalbook.dao.*;
import com.unipus.digitalbook.model.constants.CommonConstant;
import com.unipus.digitalbook.model.entity.question.*;
import com.unipus.digitalbook.model.entity.tag.TagResource;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.enums.ResultMessage;
import com.unipus.digitalbook.model.enums.TagResourceTypeEnum;
import com.unipus.digitalbook.model.params.question.QuestionParseParam;
import com.unipus.digitalbook.model.po.question.*;
import com.unipus.digitalbook.publisher.standalone.QuestionEventPublisher;
import com.unipus.digitalbook.service.QuestionService;
import com.unipus.digitalbook.service.TagService;
import com.unipus.digitalbook.service.processor.QuestionProcessorFactory;
import com.unipus.digitalbook.service.remote.restful.aigc.LlmService;
import com.unipus.digitalbook.service.remote.restful.aigc.model.AigcApiResponse;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 题型服务实现类
 */
@Slf4j
@Service
public class QuestionServiceImpl implements QuestionService {

    private final Map<String, String> templateCache = new HashMap<>();

    @Resource
    private LlmService llmService;

    @Resource
    private QuestionProcessorFactory questionProcessorFactory;

    @Resource
    private QuestionGroupPOMapper questionGroupPOMapper;

    @Resource
    private QuestionGroupSettingPOMapper questionGroupSettingPOMapper;

    @Resource
    private QuestionPOMapper questionPOMapper;

    @Resource
    private ChoiceQuestionOptionPOMapper choiceQuestionOptionPOMapper;
    @Resource
    private QuestionAnswerPOMapper questionAnswerPOMapper;

    @Resource
    private QuestionEventPublisher questionEventPublisher;

    @Resource
    private TagService tagService;

    @Resource
    private RedisUtil redisUtil;

    @PostConstruct
    public void initLoadTemplates() {
        Path dirPath = Optional.ofNullable(new ApplicationHome(getClass()).getSource())
                .map(File::getParentFile)
                .map(File::toPath)
                .orElse(Paths.get(System.getProperty("user.dir")));
        try (Stream<Path> paths = Files.walk(dirPath)) {
            paths.filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(CommonConstant.MD_FILE_SUFFIX))
                    .forEach(path -> {
                        try {
                            String fileName = path.getFileName().toString();
                            String templateContent = Files.readString(path);
                            templateCache.put(fileName, templateContent);
                        } catch (IOException e) {
                            log.error("模板文件读取失败, path:{}", path, e);
                        }
                    });
        } catch (IOException e) {
            log.error("目录遍历失败, path:{}", dirPath, e);
        }
    }

    /**
     * 根据题型解析图片与文本内容
     *
     * @param param 参数
     * @return 解析后的内容，如果没有有效的内容则返回null
     */
    @Override
    public String questionParseContent(QuestionParseParam param) {
        String fileName = param.getQuestionType() + CommonConstant.MD_FILE_SUFFIX;
        String promptTemplate = templateCache.get(fileName);
        if (promptTemplate == null) {
            log.error("题型模板文件未找到, param:{}", JsonUtil.toJsonString(param));
            throw new BizException(ResultMessage.BIZ_READ_TEMPLATE_FILE_FAIL, "题型");
        }
        String contentTemplate = "";
        if (Optional.ofNullable(param.getImageUrl()).isPresent()) {
            contentTemplate = contentTemplate + "- 理解图片中的内容 \n";
        }
        if (Optional.ofNullable(param.getText()).isPresent()) {
            contentTemplate = contentTemplate + "- 理解文本中的内容，内容如下 \n" + param.getText() + "\n";
        }
        String prompt = promptTemplate.replace("{0}", contentTemplate);
        log.info("param:{},prompt:{}", JsonUtil.toJsonString(param), prompt);
        AigcApiResponse result = llmService.processImagePrompt(null, prompt, param.getImageUrl());
        log.info("param:{},processImage result:{}", JsonUtil.toJsonString(param), JsonUtil.toJsonString(result));
        return Optional.ofNullable(result.getChoices())
                .filter(choices -> !choices.isEmpty())
                .map(List::getFirst)
                .map(AigcApiResponse.Choice::getMessage)
                .map(AigcApiResponse.Message::getContent)
                .orElse(null);
    }

    /**
     * 删除组
     *
     * @param groupId 组的id
     * @param userId  用户ID
     * @return 题组ID
     */
    @Override
    public Long deleteGroup(Long groupId, Long userId) {
        log.info("删除组 groupId = {}", groupId);
        questionGroupPOMapper.deleteByIds(Collections.singletonList(groupId), userId);
        return groupId;
    }

    /**
     * 保存大题
     *
     * @param bigQuestions 大题
     * @return 大题ID
     */
    @Override
    public List<Long> batchSaveBigQuestions(List<BigQuestionGroup> bigQuestions) {
        if (CollectionUtils.isEmpty(bigQuestions)) {
            return Collections.emptyList();
        }
        // todo 待优化 批量多线程处理
        return bigQuestions.stream().map(this::saveBigQuestion).toList();
    }


    /**
     * 获取大题
     *
     * @param bizBigQuestionId 业务大题ID
     * @param versionNumber    版本号， 若无版本则查询最新版本的题目
     * @return 大题
     */
    @Override
    public BigQuestionGroup getBigQuestion(String bizBigQuestionId, String versionNumber) {
        QuestionGroupPO bigQuestionPO = StringUtils.hasText(versionNumber)
                ? questionGroupPOMapper.selectByBizGroupIdAndVersionNumber(bizBigQuestionId, versionNumber)
                : questionGroupPOMapper.selectLatestVersionByBizGroupId(bizBigQuestionId);
        if (bigQuestionPO == null) {
            return null;
        }
        return toCacheBigQuestion(bigQuestionPO);
    }

    @Override
    public BigQuestionGroup getBigQuestion(Long questionId) {
        QuestionGroupPO questionGroupPO = questionGroupPOMapper.selectByPrimaryKey(questionId);
        return toCacheBigQuestion(questionGroupPO);
    }

    @Override
    public List<BigQuestionGroup> batchGetBigQuestions(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        List<QuestionGroupPO> questionGroupPOS = questionGroupPOMapper.selectByIds(groupIds);
        if (CollectionUtils.isEmpty(questionGroupPOS)) {
            return Collections.emptyList();
        }
        return questionGroupPOS.stream().map(this::toCacheBigQuestion).sorted(Comparator.comparing(BigQuestionGroup::getSortOrder)).collect(Collectors.toList());
    }

    @Override
    public Long getIdByBizParentIdAndVersion(String bizParentId, String bizParentVersion, String questionId) {
        return questionGroupPOMapper.selectIdByBizParentIdAndVersionNumberAndBizGroupId(bizParentId, bizParentVersion, questionId);
    }

    /**
     * @param bigQuestionPO
     * @return
     */
    private BigQuestionGroup toBigQuestion(QuestionGroupPO bigQuestionPO) {
        Long bigQuestionId = bigQuestionPO.getId();
        BigQuestionGroup bigQuestionGroup = bigQuestionPO.toBigQuestion();
        // 获取大题的setting
        QuestionGroupSettingPO questionGroupSettingPO = questionGroupSettingPOMapper.selectByGroupId(bigQuestionId);
        if (questionGroupSettingPO != null) {
            QuestionSetting setting = questionGroupSettingPO.toEntity();
            bigQuestionGroup.setSetting(setting);
        }
        // 小题列表
        List<Question> questions = questionProcessorFactory.getSmallQuestions(bigQuestionId);
        bigQuestionGroup.setQuestions(questions);
        return bigQuestionGroup;
    }


    private BigQuestionGroup toCacheBigQuestion(QuestionGroupPO bigQuestionPO) {
        if (bigQuestionPO.getVersionNumber().equals(IdentifierUtil.DEFAULT_VERSION_NUMBER)) {
            return toBigQuestion(bigQuestionPO);
        }
        Long bigQuestionId = bigQuestionPO.getId();
        String questionCatchKey = getQuestionCatchKey(bigQuestionId);
        BigQuestionGroup questionGroup = redisUtil.readFromBucket(questionCatchKey, BigQuestionGroup.class);
        if (questionGroup != null) {
            return questionGroup;
        }
        log.debug("get question from db: {}", questionCatchKey);
        BigQuestionGroup bigQuestion = toBigQuestion(bigQuestionPO);
        redisUtil.writeToBucket(questionCatchKey, bigQuestion, TimeUnit.DAYS.toSeconds(7));
        return bigQuestion;
    }
    private String getQuestionCatchKey(Long questionId) {
        return String.format("QUESTION:%s", questionId);
    }
    @Override
    @Transactional
    public Long saveBigQuestion(BigQuestionGroup bigQuestion) {
        List<Question> smallQuestions = bigQuestion.getQuestions();
        if (CollectionUtils.isEmpty(smallQuestions)) {
            throw new IllegalArgumentException("题目不能为空");
        }
        String bizBigQuestionId = bigQuestion.getBizGroupId();
        // 题版本
        String versionNumber = bigQuestion.getVersionNumber();
        BigQuestionGroup originBigQuestion = getBigQuestion(bizBigQuestionId, versionNumber);
        // 判断大题是否有变更 todo 待优化 对比逻辑，后续每个题存储下hashcode，不然对比太消耗性能如果题的内容很多
        if (Objects.equals(bigQuestion, originBigQuestion)) {
            // 如果没有变更，则返回原大题id
            return originBigQuestion.getId();
        }
        String newVersionNumber = Optional.ofNullable(versionNumber).orElse(IdentifierUtil.generateVersion());
        bigQuestion.setVersionNumber(newVersionNumber);
        // 是否更新版本数据，目前只支持更新默认版本数据
        boolean isUpdate = originBigQuestion != null && newVersionNumber.equals(IdentifierUtil.DEFAULT_VERSION_NUMBER);
        QuestionGroupPO bigQuestionPO = new QuestionGroupPO(bigQuestion);
        // 保存大题
        questionGroupPOMapper.insertOrUpdateSelective(bigQuestionPO);
        Long bigQuestionId = bigQuestionPO.getId();
        bigQuestion.setId(bigQuestionId);
        QuestionSetting setting = bigQuestion.getSetting();
        QuestionGroupSettingPO questionGroupSettingPO = new QuestionGroupSettingPO(setting, bigQuestionId);
        Long opsUserId = bigQuestion.getCreateBy();
        // 保存大题设置
        questionGroupSettingPOMapper.insertOrUpdateSelective(questionGroupSettingPO);
        if (isUpdate) {
            updateQuestionWithVersion(bigQuestion.getQuestions(), originBigQuestion.getQuestions(), bigQuestionId, newVersionNumber, opsUserId);
            // 发布新增消息
            questionEventPublisher.questionEventPublisher(bigQuestion, EventTypeEnum.EDIT, opsUserId);
        } else {
            insertQuestionWithNewVersion(bigQuestion.getQuestions(), bigQuestionId, newVersionNumber);
            // 发布新增版本消息
            questionEventPublisher.questionEventPublisher(bigQuestion, EventTypeEnum.ADD_VERSION, opsUserId);
        }
        return bigQuestionId;
    }

    @Override
    public boolean batchDeleteBigQuestions(List<Long> ids, Long opsUserId) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        questionGroupPOMapper.deleteByIds(ids, opsUserId);
        // 发布删除消息
        ids.forEach(id -> {
            BigQuestionGroup questionGroup = new BigQuestionGroup();
            questionGroup.setId(id);
            questionEventPublisher.questionEventPublisher(questionGroup, EventTypeEnum.DELETE, opsUserId);
        });
        return true;
    }

    /**
     * 批量保存大题
     *
     * @param bigQuestions           大题列表
     * @param originBigQuestionIdMap 获取原来已有的的大题id列表，用于对比是否需要删除
     *                               key：大题的业务id，value：大题id
     * @param userId                用户ID
     * @return 大题id列表
     */
    @Override
    public List<Long> batchSaveBigQuestionsWithDelete(List<BigQuestionGroup> bigQuestions, Map<String, Long> originBigQuestionIdMap, Long userId) {
        if (CollectionUtils.isEmpty(originBigQuestionIdMap)) {
            return batchSaveBigQuestions(bigQuestions);
        }
        List<Long> deleteBigQuestionIds = new ArrayList<>();
        Map<String, BigQuestionGroup> bigQuestionMap = bigQuestions.stream().collect(Collectors.toMap(BigQuestionGroup::getBizGroupId, Function.identity()));
        // 处理需要更新和删除的题目
        originBigQuestionIdMap.forEach((bizQid, qId) -> {
            if (!bigQuestionMap.containsKey(bizQid)) {
                deleteBigQuestionIds.add(qId);
            }
        });
        // 删除不需要的题目
        batchDeleteBigQuestions(deleteBigQuestionIds, userId);
        // 新增题目
        return batchSaveBigQuestions(bigQuestions);
    }

    private void updateQuestionWithVersion(List<Question> smallQuestions, List<Question> originSmallQuestions, Long parentId, String versionNumber, Long opsUserId) {
        if (smallQuestions == null || smallQuestions.isEmpty()) {
            throw new IllegalArgumentException("小题不能为空");
        }
        Map<String, Question> smallQuestionMap = smallQuestions.stream()
                .collect(Collectors.toMap(Question::getBizQuestionId, q -> q));
        // 需要删除的数据
        Set<Long> deletedQuestionIds = originSmallQuestions.stream().filter(q -> !smallQuestionMap.containsKey(q.getBizQuestionId()))
                .map(Question::getId).collect(Collectors.toSet());
        if (!deletedQuestionIds.isEmpty()) {
            questionGroupPOMapper.deleteByIds(deletedQuestionIds, opsUserId);
        }
        Map<String, Question> originSmallQuestionMap = originSmallQuestions.stream().collect(Collectors.toMap(Question::getBizQuestionId, q -> q));
        // 需要更新的数据
        smallQuestions.forEach(question -> {
            Question originSmallQuestion = originSmallQuestionMap.get(question.getBizQuestionId());
            question.setVersionNumber(versionNumber);
            // 新增的数据
            if ( originSmallQuestion== null) {
                insertSmallQuestionWithNewVersion(question, parentId);
            } else {
                updateSmallQuestionWithVersion(question, originSmallQuestion, parentId, opsUserId);
            }
        });
    }

    private void updateSmallQuestionWithVersion(Question smallQuestion, Question originSmallQuestion, Long parentId, Long opsUserId) {
        QuestionGroupPO smallQuestionGroupPO = new QuestionGroupPO(smallQuestion, parentId);
        // 保存小题
        questionGroupPOMapper.insertOrUpdateSelective(smallQuestionGroupPO);
        // 保存标签
        saveTags(smallQuestion);
        String versionNumber = smallQuestionGroupPO.getVersionNumber();
        Long smallGroupId = smallQuestionGroupPO.getId();
        List<Question> questions = smallQuestion.getQuestions();
        List<Question> originQuestions = originSmallQuestion.getQuestions();
        Map<String, Question> questionMap = questions.stream()
                .collect(Collectors.toMap(Question::getBizQuestionId, q -> q));
        // 需要删除的数据, 需要区分题组和还是题
        Set<Long> deletedSmallQuestionIds = new HashSet<>();
        Set<Long> deletedQuestionIds = new HashSet<>();
        originQuestions.stream().filter(q -> !questionMap.containsKey(q.getBizQuestionId())).forEach(q -> {
            if (q instanceof IQuestion) {
                deletedQuestionIds.add(q.getId());
            } else {
                deletedSmallQuestionIds.add(q.getId());
            }
        });
        if (!deletedQuestionIds.isEmpty()) {
            questionPOMapper.deleteByIds(deletedQuestionIds, opsUserId);
        }
        if (!deletedSmallQuestionIds.isEmpty()) {
            questionGroupPOMapper.deleteByIds(deletedSmallQuestionIds, opsUserId);
        }
        Map<String, Question> originQuestionMap = originQuestions.stream().collect(Collectors.toMap(Question::getBizQuestionId, Function.identity()));
        questions.forEach(q -> {
            q.setVersionNumber(versionNumber);
            Question originQuestion = originQuestionMap.get(q.getBizQuestionId());
            upsertQuestionWithVersion(q, originQuestion, smallGroupId, opsUserId);
        });
    }

    private void upsertQuestionWithVersion(Question q, Question originQuestion, Long smallGroupId, Long opsUserId) {
        if (q instanceof IQuestion) {
            // 保存题目
            if (originQuestion == null) {
                insertQuestionWithNewVersion(q, smallGroupId);
            } else {
                updateQuestionWithVersion(q, originQuestion, smallGroupId, opsUserId);
            }
        } else {
            // 保存小题(题组)
            if (originQuestion == null) {
                insertSmallQuestionWithNewVersion(q, smallGroupId);
            } else {
                updateSmallQuestionWithVersion(q, originQuestion, smallGroupId, opsUserId);
            }
        }
    }

    private void updateQuestionWithVersion(Question question, Question originQuestion, Long groupId, Long opsUserId) {
        QuestionPO questionPO = new QuestionPO(question, groupId);
        questionPOMapper.insertOrUpdateSelective(questionPO);
        Long questionId = questionPO.getId();
        updateQuestionOptionsWithVersion(question.getOptions(), originQuestion.getOptions(), questionId, opsUserId);
        updateQuestionAnswersWithVersion(question.getAnswers(), originQuestion.getAnswers(), questionId, opsUserId);
    }

    private void updateQuestionOptionsWithVersion(List<ChoiceQuestionOption> options, List<ChoiceQuestionOption> originOptions, Long questionId, Long opsUserId) {
        if (options == null || options.isEmpty()) {
            return;
        }
        Map<String, ChoiceQuestionOption> optionMap = options.stream().collect(Collectors.toMap(ChoiceQuestionOption::getOptionId, Function.identity()));
        Set<Long> deletedIds = originOptions.stream().filter(o -> !optionMap.containsKey(o.getOptionId())).map(ChoiceQuestionOption::getId).collect(Collectors.toSet());
        if (!deletedIds.isEmpty()) {
            choiceQuestionOptionPOMapper.deleteByIds(deletedIds, opsUserId);
        }
        List<ChoiceQuestionOptionPO> choiceQuestionOptionPOList = options.stream().map(choiceQuestionOption -> new ChoiceQuestionOptionPO(choiceQuestionOption, questionId))
                .toList();
        choiceQuestionOptionPOMapper.batchInsertOrUpdate(choiceQuestionOptionPOList);
    }

    private void updateQuestionAnswersWithVersion(List<QuestionAnswer> answers, List<QuestionAnswer> originAnswers, Long questionId, Long opsUserId) {
        if (answers == null || answers.isEmpty()) {
            return;
        }
        Map<String, QuestionAnswer> answerMap = answers.stream().collect(Collectors.toMap(QuestionAnswer::getCorrectAnswerId, q -> q));
        Set<Long> deletedIds = originAnswers.stream().filter(q -> !answerMap.containsKey(q.getCorrectAnswerId())).map(QuestionAnswer::getId).collect(Collectors.toSet());
        if (!deletedIds.isEmpty()) {
            questionAnswerPOMapper.deleteByIds(deletedIds, opsUserId);
        }
        List<QuestionAnswerPO> questionAnswerPOList = answers.stream().map(questionAnswer -> new QuestionAnswerPO(questionAnswer, questionId))
                .toList();
        questionAnswerPOMapper.batchInsertOrUpdate(questionAnswerPOList);
    }

    private void insertQuestionWithNewVersion(List<Question> smallQuestions, Long parentId, String versionNumber) {
        if (smallQuestions == null || smallQuestions.isEmpty()) {
            throw new IllegalArgumentException("小题不能为空");
        }
        smallQuestions.forEach(question -> {
            question.setVersionNumber(versionNumber);
            insertSmallQuestionWithNewVersion(question, parentId);
        });
    }

    private void insertSmallQuestionWithNewVersion(Question smallQuestion, Long parentId) {
        QuestionGroupPO smallQuestionGroupPO = new QuestionGroupPO(smallQuestion, parentId);
        // 保存小题
        questionGroupPOMapper.insertOrUpdateSelective(smallQuestionGroupPO);
        // 保存标签
        saveTags(smallQuestion);
        String versionNumber = smallQuestionGroupPO.getVersionNumber();
        Long smallGroupId = smallQuestionGroupPO.getId();
        List<Question> questions = smallQuestion.getQuestions();
        questions.forEach(q -> {
            q.setVersionNumber(versionNumber);
            if (q instanceof SmallQuestion) {
                // 保存小题
                insertSmallQuestionWithNewVersion(q, smallGroupId);
            } else if (q instanceof IQuestion) {
                // 保存题目
                insertQuestionWithNewVersion(q, smallGroupId);
            }
        });
    }

    private void insertQuestionWithNewVersion(Question question, Long groupId) {
        QuestionPO questionPO = new QuestionPO(question, groupId);
        questionPOMapper.insertOrUpdateSelective(questionPO);
        Long questionId = questionPO.getId();
        insertQuestionOptionsWithNewVersion(question.getOptions(), questionId);
        insertQuestionAnswersWithNewVersion(question.getAnswers(), questionId);
    }

    private void insertQuestionOptionsWithNewVersion(List<ChoiceQuestionOption> options, Long questionId) {
        if (options == null || options.isEmpty()) {
            return;
        }
        List<ChoiceQuestionOptionPO> choiceQuestionOptionPOList = options.stream().map(choiceQuestionOption -> new ChoiceQuestionOptionPO(choiceQuestionOption, questionId))
                .toList();
        choiceQuestionOptionPOMapper.batchInsertOrUpdate(choiceQuestionOptionPOList);
    }

    private void insertQuestionAnswersWithNewVersion(List<QuestionAnswer> answers, Long questionId) {
        if (answers == null || answers.isEmpty()) {
            return;
        }
        List<QuestionAnswerPO> questionAnswerPOList = answers.stream().map(questionAnswer -> new QuestionAnswerPO(questionAnswer, questionId))
                .toList();
        questionAnswerPOMapper.batchInsertOrUpdate(questionAnswerPOList);
    }

    private void saveTags(Question question) {
        if (CollectionUtils.isEmpty(question.getTags())) {
            return;
        }
        TagResource tagResource = new TagResource();
        tagResource.setResourceId(question.getBizQuestionId());
        tagResource.setResourceVersion(question.getVersionNumber());
        tagResource.setResourceType(TagResourceTypeEnum.QUESTION.getCode());
        tagService.saveTagListToResource(QuestionTag.toTagList(question.getTags()), tagResource, question.getCreateBy());
    }
}