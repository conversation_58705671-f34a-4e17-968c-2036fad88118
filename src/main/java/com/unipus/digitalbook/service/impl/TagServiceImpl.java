package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.db.InsertFailedException;
import com.unipus.digitalbook.dao.TagPOMapper;
import com.unipus.digitalbook.dao.TagReferencePOMapper;
import com.unipus.digitalbook.model.entity.paper.QuestionTag;
import com.unipus.digitalbook.model.entity.tag.Tag;
import com.unipus.digitalbook.model.entity.tag.TagResource;
import com.unipus.digitalbook.model.enums.TagResourceTypeEnum;
import com.unipus.digitalbook.model.enums.TagTypeEnum;
import com.unipus.digitalbook.model.po.tag.TagPO;
import com.unipus.digitalbook.model.po.tag.TagReferencePO;
import com.unipus.digitalbook.service.TagService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 标签服务实现类
 */
@Service
@Slf4j
public class TagServiceImpl implements TagService {

    @Resource
    private TagPOMapper tagPOMapper;

    @Resource
    private TagReferencePOMapper tagReferencePOMapper;

    /**
     * 保存题目标签列表及标签关系
     *
     * @param questionTags 标签对象列表
     * @param userId       用户ID
     * @return 标签ID
     */
    @Transactional
    @Override
    public Boolean saveQuestionTagList(List<QuestionTag> questionTags, Long userId) {
        if (CollectionUtils.isEmpty(questionTags)) {
            log.warn("Question tags list is empty, nothing to save.");
            return Boolean.FALSE;
        }
        questionTags.forEach(qt->saveQuestionTag(qt, userId));
        return Boolean.TRUE;
    }

    /**
     * 递归保存题目标签
     *
     * @param questionTag  标签对象
     * @param userId       用户ID
     */
    private void saveQuestionTag(QuestionTag questionTag, Long userId) {
        // 取得标签关联的资源ID
        List<String> resourceIds = new ArrayList<>();
        if(questionTag.getBaseResourceId()!=null) {
            resourceIds.add(questionTag.getBaseResourceId());
        }
        if(questionTag.getTargetResourceId()!=null) {
            resourceIds.add(questionTag.getTargetResourceId());
        }
        if(CollectionUtils.isEmpty(questionTag.getTags())){
            return;
        }
        // 保存标签/保存标签关系
        questionTag.getTags().forEach(tag -> saveTagAndReference(tag, null, resourceIds, userId));
    }

    /**
     * 保存标签及标签关系
     * @param tag 标签对象
     * @param parentId 父标签ID
     * @param resourceIds 资源ID列表
     * @param userId 用户ID
     */
    private void saveTagAndReference(Tag tag, Long parentId, List<String> resourceIds, Long userId) {
        TagPO parentTag = tagPOMapper.selectTagById(parentId);
        // 保存标签
        TagPO tagPO = insertOrUpdateTag(new TagPO(tag, parentTag, userId));
        Long tagId = tagPO.getId();

        // 保存关系
        if(!CollectionUtils.isEmpty(resourceIds)) {
            resourceIds.forEach(resourceId -> tagReferencePOMapper
                    .insertOrUpdate(new TagReferencePO(resourceId, TagResourceTypeEnum.QUESTION.getCode() ,tagId, userId)));
        }

         // 处理下级列表
        if(!CollectionUtils.isEmpty(tag.getChildren())){
            tag.getChildren().forEach(child -> saveTagAndReference(child, tagId, resourceIds, userId));
        }
    }

    /**
     * 保存标签
     * @param tag  标签对象
     * @param parent 父标签ID
     * @param userId 用户ID
     * @return 标签ID
     */
    @Override
    public Long saveTag(Tag tag, Long parent, Long userId) {
        TagPO tagPO = insertOrUpdateTag(new TagPO(tag, parent, userId));
        if (tagPO.getId() != null) {
            String errorMessage = "Failed to insert tag: " + tag;
            log.error(errorMessage);
            throw new InsertFailedException(errorMessage);
        }
        return tagPO.getId();
    }

    @Override
    public void saveTagListToResource(List<Tag> tagList, TagResource resource, Long userId) {
        if (CollectionUtils.isEmpty(tagList)) {
            return;
        }
        // 根据名字获取对应的标签
        List<String> tagNames = tagList.stream().map(Tag::getTagName).toList();
        Map<String, List<TagPO>> tagNameMap = tagPOMapper.selectByNames(tagNames).stream().collect(Collectors.groupingBy(TagPO::getTagName));
        List<Long> parentIds = tagList.stream().map(Tag::getParentId).toList();
        Map<Long, TagPO> parentTagMap = tagPOMapper.selectByIds(parentIds).stream().collect(Collectors.toMap(TagPO::getId, tagPO -> tagPO));
        tagList.forEach(tag -> {
            List<TagPO> tagNameList = tagNameMap.get(tag.getTagName());
            if (CollectionUtils.isEmpty(tagNameList)) {
                TagPO parentTag = parentTagMap.get(tag.getParentId());
                TagPO tagPO = new TagPO(tag, parentTag, userId);
                tagPOMapper.insertOrUpdateTag(tagPO);
                tag.setTagId(tagPO.getId());
            } else {
                TagPO tagNamePO = tagNameList.stream().filter(tagName ->
                        Objects.equals(tagName.getParentId(), tag.getParentId()) && Objects.equals(tagName.getTagType(), tag.getTagType())).findAny().orElse(null);
                if (tagNamePO != null) {
                    tag.setTagId(tagNamePO.getId());
                } else {
                    TagPO parentTag = parentTagMap.get(tag.getParentId());
                    TagPO tagPO = new TagPO(tag, parentTag, userId);
                    tagPOMapper.insertOrUpdateTag(tagPO);
                    tag.setTagId(tagPO.getId());
                }
            }
        });
        Map<Long, Tag> tagMap = tagList.stream().collect(Collectors.toMap(Tag::getTagId, t -> t));
        List<Long> deletedRefIds = tagReferencePOMapper.selectByResourceIdAndVersion(Collections.singletonList(resource.getResourceId()),
                        resource.getResourceType(),
                        resource.getResourceVersion()).stream()
                .filter(ref -> !tagMap.containsKey(ref.getTagId()))
                .map(TagReferencePO::getId)
                .toList();
        if (!deletedRefIds.isEmpty()) {
            tagReferencePOMapper.deleteByIds(deletedRefIds);
        }
        // 插入资源引用关系
        List<TagReferencePO> list = IntStream.range(0, tagList.size())
                .mapToObj(i -> new TagReferencePO(resource, tagList.get(i).getTagId(), i, userId))
                .toList();
        tagReferencePOMapper.batchInsertOrUpdate(list);
    }

    /**
     * 插入或更新标签
     * @param tagPO 条件标签对象
     * @return 标签ID
     */
    private TagPO insertOrUpdateTag(TagPO tagPO){
        TagPO result = tagPOMapper.selectTagByCondition(tagPO);
        if(result != null){
            return result;
        }
        tagPOMapper.insertTag(tagPO);
        return tagPO;
    }

    /**
     * 根据标签类型获取顶级标签
     * @param tagType 标签类型
     * @return 标签对象
     */
    @Override
    public List<Tag> getTopLevelTagListByType(TagTypeEnum tagType) {
        List<TagPO> tagPOs = tagPOMapper.selectTagsByIds(null, tagType.getCode());
        if(CollectionUtils.isEmpty(tagPOs)){
            log.warn("getTopLevelTagListByType-根据标签类型获取顶级标签结果为空. TagTypeEnum:{}",tagType);
            return List.of();
        }
        return tagPOs.stream().filter(t->t.getParentId()==null).map(TagPO::toEntity).toList();
    }

    /**
     * 获取标签
     * @param tagIds 标签ID列表
     * @param tagType 标签类型
     * @return 标签对象
     */
    @Override
    public List<Tag> getTagList(List<Long> tagIds, TagTypeEnum tagType) {
        List<TagPO> tagPOs = tagPOMapper.selectTagsByIds(tagIds, tagType!=null?tagType.getCode():null);
        if(CollectionUtils.isEmpty(tagPOs)){
            log.warn("getTagList-标签列表查询结果为空.tagIds:{} \n TagTypeEnum:{}",tagIds,tagType);
            return List.of();
        }
        return tagPOs.stream().map(TagPO::toEntity).toList();
    }

    /**
     * 根据ID列表查询标签列表（包含所有层级子标签）
     * @param tagIds 标签ID列表
     * @param tagType 标签类型
     * @return 标签对象
     */
    @Override
    public List<Tag> recursionQueryTagList(List<Long> tagIds, TagTypeEnum tagType) {
        Integer tagTypeCode = tagType==null? null : tagType.getCode();
        List<TagPO> tagPOs = tagPOMapper.recursionQueryTagsByIds(tagIds, tagTypeCode);
        if(CollectionUtils.isEmpty(tagPOs)){
            log.warn("recursionQueryTagList-标签列表查询结果为空.tagIds:{} \n TagTypeEnum:{}",tagIds,tagType);
            return List.of();
        }
        return tagPOs.stream().map(TagPO::toEntity).toList();
    }

    @Override
    public Map<String, List<Tag>> getTagListByResourceIdsAndVersion(List<String> resourceIds, TagResourceTypeEnum resourceType, String version) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            return Collections.emptyMap();
        }
        List<TagReferencePO> tagReferencePOS = tagReferencePOMapper.selectByResourceIdAndVersion(resourceIds, resourceType.getCode(), version);
        if (CollectionUtils.isEmpty(tagReferencePOS)) {
            return Collections.emptyMap();
        }
        List<Long> tagIds = tagReferencePOS.stream().map(TagReferencePO::getTagId).toList();
        List<TagPO> tags = tagPOMapper.selectByIds(tagIds);
        if(CollectionUtils.isEmpty(tags)) {
            return Collections.emptyMap();
        }
        Map<Long, TagPO> tagMap = tags.stream().collect(Collectors.toMap(TagPO::getId, v -> v, (v1, v2) -> v1));
        return tagReferencePOS.stream()
                .filter(tagReferencePO -> tagMap.get(tagReferencePO.getTagId()) != null)
                .collect(Collectors.groupingBy(
                        TagReferencePO::getResourceId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(TagReferencePO::getSortOrder, Comparator.nullsLast(Integer::compareTo)))
                                        .map(ref -> {
                                            Tag tag = tagMap.get(ref.getTagId()).toEntity();
                                            tag.setResourceId(ref.getResourceId());
                                            return tag;
                                        })
                                        .toList()
                        )
                ));
    }


}
