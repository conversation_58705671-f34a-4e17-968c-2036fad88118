package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.BookUploadResourcesPOMapper;
import com.unipus.digitalbook.model.entity.book.BookFile;
import com.unipus.digitalbook.model.po.BookUploadResourcesPO;
import com.unipus.digitalbook.service.BookFileService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BookFileServiceImpl implements BookFileService {

    @Resource
    private BookUploadResourcesPOMapper bookUploadResourcesPOMapper;


    /**
     * 批量添加书文件记录
     *
     * @param bookFiles 书文件对象列表
     * @param creatorId 创建者ID
     * @return 操作是否成功
     */
    @Override
    public Boolean addBookFiles(List<BookFile> bookFiles, Long creatorId) {
        if  (bookFiles == null || bookFiles.isEmpty()) {
            return false;
        }
        bookFiles.forEach(bookFile -> bookFile.setCreateBy(creatorId));
        bookFiles.forEach(bookFile -> bookUploadResourcesPOMapper.insertSelective(new BookUploadResourcesPO(bookFile)));
        return true;
    }


    /**
     * 批量删除书文件记录
     *
     * @param ids 书文件ID列表
     * @return 操作是否成功
     */
    @Override
    public Boolean deleteBookFiles(List<Long> ids) {
       int res = bookUploadResourcesPOMapper.deleteBookFileListByIdList(ids);
        return res>0;
    }

    /**
     * 根据书ID查询书的所有上传的文件记录
     *
     * @param bookId 书ID
     * @return 书文件对象列表
     */
    @Override
    public List<BookFile> getAllBookFileListByBookId(String bookId) {
        List<BookUploadResourcesPO> bookUploadResourcesPOList = bookUploadResourcesPOMapper.selectByBookId(bookId);
        if (bookUploadResourcesPOList != null && !bookUploadResourcesPOList.isEmpty()) {
            return bookUploadResourcesPOList.stream().map(BookUploadResourcesPO::toEntity).toList();
        }
        return List.of();
    }
}
