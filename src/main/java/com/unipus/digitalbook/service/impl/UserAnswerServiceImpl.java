package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.unipus.digitalbook.common.exception.paper.UserPaperNotPassException;
import com.unipus.digitalbook.dao.UserAnswerPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.entity.question.*;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.po.question.UserAnswerPO;
import com.unipus.digitalbook.producer.TenantMessageProducer;
import com.unipus.digitalbook.publisher.standalone.UserAnswerEventPublisher;
import com.unipus.digitalbook.service.UserAnswerService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class UserAnswerServiceImpl implements UserAnswerService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserAnswerPOMapper userAnswerPOMapper;

    @Resource
    private UserAnswerEventPublisher userAnswerEventPublisher;

    @Resource
    private TenantMessageProducer tenantMessageProducer;

    @Resource(name = "questionForJudgeCache")
    private LoadingCache<QuestionId, BigQuestionGroup> questionForJudgeCache;

    @Override
    public List<JudgeTaskTicket> judgeStart(BigQuestionGroup question, UserAnswerList userAnswers) {
        if (question == null || userAnswers == null) {
            return Collections.emptyList();
        }
        return userAnswers.startJudgeTask(question.getQuestions());
    }

    @Override
    public UserAnswerList fetchJudgeResult(UserAnswerList userAnswerList) {
        if (userAnswerList == null) {
            return new UserAnswerList(Collections.emptyList());
        }
        List<UserAnswer> userAnswers = userAnswerList.fetchJudgeResult();
        return new UserAnswerList(userAnswers);
    }

    @Override
    public UserAnswerList judgeScore(BigQuestionGroup question, UserAnswerList userAnswers) {
        if (question == null || userAnswers == null) {
            return null;
        }
        return userAnswers.score(question.getQuestions());
    }

    @Override
    public UserAnswerList submitScore(BigQuestionGroup question, UserAnswerList userAnswers, SubmitAnswerContext context) {
        if (question == null || userAnswers == null) {
            return null;
        }
        UserAnswerList answerList = userAnswers.score(question.getQuestions());
        // 保存作答记录
        List<UserAnswer> userAnswersResult = addUserAnswers(userAnswers.getUserAnswers());
        // 推送用户作答记录同步推送第三方
        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                context.getTenantId(), MessageTopicEnum.PUSH_USER_ANSWER)).isPresent()) {
            UserAnswerNodeData answerNodeData = new UserAnswerNodeData(context, question, userAnswersResult);
            Response<String> syncResponse = tenantMessageProducer.produceSyncMessage(
                    context.getTenantId(),
                    MessageTopicEnum.PUSH_USER_ANSWER,
                    new TypeReference<>() {}, answerNodeData, new TypeReference<>() {});
            // kafka syncResponse is null ignore
            if (syncResponse != null && !syncResponse.isSuccess()) {
                throw new UserPaperNotPassException(syncResponse.getMessage());
            }
        } else {
            log.info("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.PUSH_USER_ANSWER.name(), context.getTenantId());
        }
        // 通过用户作答记录
        batchPassUserAnswer(userAnswersResult.stream().map(UserAnswer::getId).toList());
        // 发布用户作答事件
        userAnswerEventPublisher.userAnswerEventPublisher(EventTypeEnum.ADD, question, answerList.getUserAnswers(), context);
        return answerList;
    }


    /**
     * 新增用户作答列表
     *
     * @param userAnswers 用户作答列表
     * @return 是否保存成功
     */
    @Override
    public List<UserAnswer> addUserAnswers(List<UserAnswer> userAnswers) {
        if (CollectionUtils.isEmpty(userAnswers)) {
            return Collections.emptyList();
        }
        // 批次一致性检查
        List<UserAnswerPO> userAnswerPOList = UserAnswerPO.toPOList(userAnswers);
        userAnswerPOMapper.batchInsert(userAnswerPOList);
        return userAnswerPOList.stream()
                .map(UserAnswerPO::toEntity)
                .toList();
    }

    /**
     * 保存用户作答列表
     *
     * @param userAnswers 用户作答列表
     * @return 是否保存成功
     */
    @Override
    public void saveUserAnswers(List<UserAnswer> userAnswers) {
        if (CollectionUtils.isEmpty(userAnswers)) {
            return;
        }
        List<UserAnswerPO> userAnswerPOList = UserAnswerPO.toPOList(userAnswers);
        userAnswerPOMapper.batchInsertOrUpdate(userAnswerPOList);
    }

    /**
     * 查询最近一次同一批次下的作答记录
     *
     * @param bizQuestionIds        题目业务ID列表
     * @param questionVersionNumber 题目版本
     * @param openId                用户ssoID
     * @param tenantId              租户ID
     * @return 最近一次作答记录列表
     */
    @Override
    public List<UserAnswer> getLatestUserAnswers(Collection<String> bizQuestionIds, String questionVersionNumber, String openId, Long tenantId) {
        if (CollectionUtils.isEmpty(bizQuestionIds)) {
            return Collections.emptyList();
        }
        // 查询最近一次同一批次下的作答记录
        List<UserAnswerPO> userAnswerPOList = userAnswerPOMapper.selectLatest(bizQuestionIds, questionVersionNumber, openId, tenantId);
        if (CollectionUtils.isEmpty(userAnswerPOList)) {
            return Collections.emptyList();
        }
        // 将PO对象转换为实体对象
        return userAnswerPOList.stream().map(UserAnswerPO::toEntity).toList();
    }

    /**
     * 查询作答记录
     *
     * @param bizQuestionIds        题目业务ID列表
     * @param questionVersionNumber 题目版本
     * @param openId                用户ssoID
     * @param tenantId              租户ID
     * @return 作答记录列表
     */
    @Override
    public List<UserAnswer> getUserAnswers(Collection<String> bizQuestionIds, String questionVersionNumber, String openId, Long tenantId) {
        if (CollectionUtils.isEmpty(bizQuestionIds)) {
            return Collections.emptyList();
        }
        List<UserAnswerPO> userAnswerPOList = userAnswerPOMapper.selectList(bizQuestionIds, questionVersionNumber, openId, tenantId);
        if (CollectionUtils.isEmpty(userAnswerPOList)) {
            return Collections.emptyList();
        }
        // 将PO对象转换为实体对象
        return userAnswerPOList.stream().map(UserAnswerPO::toEntity).toList();
    }

    /**
     * 根据批次ID查询用户作答记录
     *
     * @param batchId 用户作答批次ID
     * @return 用户作答记录列表
     */
    @Override
    public List<UserAnswer> getUserAnswersByBatchId(String batchId) {
        if (!StringUtils.hasText(batchId)) {
            return Collections.emptyList();
        }
        List<UserAnswerPO> userAnswerPOList = userAnswerPOMapper.selectByBatchId(batchId);
        if (CollectionUtils.isEmpty(userAnswerPOList)) {
            return Collections.emptyList();
        }
        return userAnswerPOList.stream().map(UserAnswerPO::toEntity).toList();
    }

    /**
     * 设置用户异步作答回调记录
     *
     * @param bizAnswerId   作答业务ID
     * @param callbackValue 用户作答详情
     */
    @Override
    public void setAnswerCallback(String bizAnswerId, String callbackValue) {
        String callbackKey = CacheConstant.USER_ANSWER_CALLBACK_PREFIX + bizAnswerId;
        log.info("设置用户异步作答回调记录: {}", callbackValue);
        stringRedisTemplate.opsForValue().set(callbackKey, callbackValue, 1, TimeUnit.DAYS);
    }

    /**
     * 获取用户异步作答回调记录
     *
     * @param bizAnswerId 作答业务ID
     * @return 用户作答详情
     */
    @Override
    public JSONObject getAnswerCallback(String bizAnswerId) {
        String callbackKey = CacheConstant.USER_ANSWER_CALLBACK_PREFIX + bizAnswerId;
        String callbackValue = stringRedisTemplate.opsForValue().get(callbackKey);
        if (!StringUtils.hasText(callbackValue)) {
            return JSONObject.from(new HashMap<>());
        }
        return JSON.parseObject(callbackValue);
    }

    /**
     * 获取用户异步作答回调记录评测结果
     *
     * @param bizAnswerId 作答业务ID
     * @return 用户评测结果
     */
    @Override
    public String getAnswerCallbackEvaluation(String bizAnswerId) {
        JSONObject callbackBody = getAnswerCallback(bizAnswerId);
        if (callbackBody == null || callbackBody.isEmpty()) {
            return null;
        }
        return callbackBody.getString("evaluation");
    }

    /**
     * 设置用户异步作答回调记录评测结果
     *
     * @param bizAnswerId 作答业务ID
     * @param evaluation  评测结果
     * @return 是否更改成功
     */
    @Override
    public Boolean setAnswerCallbackEvaluation(String bizAnswerId, String evaluation) {
        JSONObject callbackBody = getAnswerCallback(bizAnswerId);
        if (callbackBody == null || callbackBody.isEmpty()) {
            return false;
        }
        callbackBody.put("evaluation", evaluation);
        setAnswerCallback(bizAnswerId, callbackBody.toJSONString());
        return true;
    }

    @Override
    public BigQuestionGroup getBigQuestionForJudge(String bizBigQuestionId, String versionNumber) {
        return questionForJudgeCache.get(new QuestionId(bizBigQuestionId, versionNumber));
    }

    /**
     * 批量通过用户作答记录
     *
     * @param ids 作答记录ID列表
     */
    public void batchPassUserAnswer(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        log.info("批量通过用户作答记录: {}", ids);
        userAnswerPOMapper.batchPassUserAnswer(ids);
    }
}
