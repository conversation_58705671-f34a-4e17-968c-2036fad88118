package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.conf.cache.CacheManagerConfig;
import com.unipus.digitalbook.dao.PaperBookRelationPOMapper;
import com.unipus.digitalbook.dao.PaperExtendPOMapper;
import com.unipus.digitalbook.dao.PaperQuestionRelationPOMapper;
import com.unipus.digitalbook.dao.QuestionGroupPOMapper;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.QuestionTag;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.model.params.paper.PaperQueryParam;
import com.unipus.digitalbook.model.params.paper.question.PaperQuestionListParam;
import com.unipus.digitalbook.model.po.paper.PaperBookRelationPO;
import com.unipus.digitalbook.model.po.paper.PaperExtendPO;
import com.unipus.digitalbook.model.po.paper.PaperPO;
import com.unipus.digitalbook.model.po.paper.PaperQuestionRelationPO;
import com.unipus.digitalbook.model.po.question.QuestionGroupPO;
import com.unipus.digitalbook.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 试卷服务实现类
 */
@Service
@Slf4j
public class PaperServiceImpl implements PaperService {

    @Resource
    private QuestionService questionService;
    @Resource
    private PaperBookRelationPOMapper paperBookRelationPOMapper;
    @Resource
    private PaperReferenceService paperReferenceService;
    @Resource
    private QuestionGroupPOMapper questionGroupPOMapper;
    @Resource
    private PaperQuestionRelationPOMapper paperQuestionRelationPOMapper;
    @Resource
    private UserService userService;
    @Resource
    private PaperOperationLogService paperOperationLogService;
    @Resource
    private PaperVersionService paperVersionService;
    @Resource
    private PaperExtendPOMapper paperExtendPOMapper;
    @Resource
    private CacheManagerConfig cacheManagerConfig;

    /**
     * 保存试卷
     * @param paper 试卷对象
     * @param userId       用户ID
     * @return 试卷ID
     */
    @Transactional
    @Override
    public String savePaper(Paper paper, Long userId) {
        // 查询旧的试卷信息
        Paper oldPaper = getPaperDetailNoException(paper.getPaperId(), paper.getVersionNumber());
        if(oldPaper != null && oldPaper.getId()!=null){
            paper.setId(oldPaper.getId());
        }

        // 保存试卷
        Long paperPrimaryId = savePaperDetail(paper, userId);
        log.debug("试卷主键ID:{}", paperPrimaryId);

        // 保存试卷教材关系
        PaperBookRelationPO paperBookRelationPO = new PaperBookRelationPO(paper.getPaperId(), paper.getBookId(), userId);
        paperBookRelationPOMapper.insertOrUpdate(paperBookRelationPO);
        log.debug("试卷在教材中的引用主键ID:{}", paperBookRelationPO.getId());

        // 输出试卷保存日志
        paperOperationLogService.addPaperSaveOperation(oldPaper, paper, userId);

        // 返回试卷ID
        return paper.getPaperId();
    }

    private Paper getPaperDetailNoException(String paperId, String versionNumber) {
        try {
            return this.getPaperDetail(paperId, versionNumber, true);
        }catch (IllegalArgumentException e){
            return null;
        }
    }

    /**
     * 保存试卷详情
     * @param paper 试卷对象
     * @param userId 用户ID
     * @return 试卷主键ID
     */
    private Long savePaperDetail(Paper paper, Long userId){
        BigQuestionGroup questionGroup = paper.buildQuestionGroup(userId);
        // 保存试卷道题组
        QuestionGroupPO questionGroupPO = new QuestionGroupPO(questionGroup);
        questionGroupPOMapper.insertOrUpdateSelective(questionGroupPO);

        // 保存试卷扩展信息
        PaperExtendPO paperExtendPO = new PaperExtendPO(paper, userId);
        if (questionGroupPO.getId() != null) {
            paperExtendPO.setPaperVersionId(questionGroupPO.getId());
        }
        paperExtendPOMapper.insertOrUpdate(paperExtendPO);

        // 返回题组主键ID
        return questionGroupPO.getId();
    }

    /**
      * 获取默认版本试卷/题库
      * @param bizGroupId 试卷ID/题库ID
      * @param versionNumber 版本号
      * @return 试卷/题库对象
     */
    private BigQuestionGroup getDefaultQuestionGroup(String bizGroupId, String versionNumber) {
        QuestionGroupPO questionGroupPO = questionGroupPOMapper.selectQuestionGroupByBizGroupId(bizGroupId, versionNumber);
        if(questionGroupPO == null){
            throw new BizException("试卷/题库不存在");
        }

        return questionGroupPO.toBigQuestion();
    }

    /**
     * 保存试卷题目列表及标签列表（挑战卷以外）
     * @param paperQuestionListParam 题组参数对象
     * @param userId 用户ID
     * @return 试卷ID/题库ID
     */
    @Transactional
    @Override
    public String saveQuestions(PaperQuestionListParam paperQuestionListParam, Long userId) {
        String defaultVersionNumber = IdentifierUtil.DEFAULT_VERSION_NUMBER;
        String parentId = paperQuestionListParam.getParentId();

        // 查询试卷/题库
        BigQuestionGroup parent = getDefaultQuestionGroup(parentId, defaultVersionNumber);
        Integer type = parent.getType();
        if(QuestionGroupTypeEnum.CHALLENGE_PAPER.match(type)){
            log.error("请选择正确的试卷,试卷类型:{},试卷ID/题库ID:{}", type, parent.getBizGroupId());
            throw new BizException("请选择正确的试卷(常规卷/诊断卷)");
        }

        // 保存试卷/题库题组列表
        boolean updateFlag = saveQuestionGroups(parent, paperQuestionListParam, defaultVersionNumber, userId);

        // 保存诊断卷题组标签以及诊断卷默认题与推荐题关系
        if(QuestionGroupTypeEnum.DIAGNOSTIC.match(type)) {
            saveQuestionRelation(paperQuestionListParam.toQuestionTagEntity(), parentId, defaultVersionNumber, userId);
        }
        // 输出试卷内容编辑日志
        if(updateFlag) {
            String paperId = QuestionGroupTypeEnum.QUESTION_BANK.match(type) ? getChallengePaperId(parent) : parentId;
            Paper paper = getPaperDetail(paperId, defaultVersionNumber, false);
            paperOperationLogService.addPaperContentOperation(paper, userId);
        }

        return paperQuestionListParam.getParentId();
    }

    /**
     * 保存诊断卷默认题与推荐题关系。
     * 该方法采用更高效的“协调”模式，直接在数据库层面处理差异，以获得更好的性能和数据一致性。
     *
     * @param questionTags  题组关系列表 (作为“唯一真实来源”)
     * @param paperId       试卷ID
     * @param versionNumber 版本号
     * @param userId        用户ID
     */
    private void saveQuestionRelation(List<QuestionTag> questionTags, String paperId, String versionNumber, Long userId) {
        // 1: 根据输入参数，准备好本次操作最终需要存在于数据库的关系列表。
        List<PaperQuestionRelationPO> relationsToUpsert = CollectionUtils.isEmpty(questionTags) ? Collections.emptyList()
                : questionTags.stream()
                .filter(qt -> qt.getBaseResourceId() != null)
                .map(questionTag -> new PaperQuestionRelationPO(questionTag, paperId, versionNumber, userId))
                .toList();

        // 2: 从需要保留的关系中，提取出所有基础题目ID (base_question_id)。
        Set<String> baseIdsToKeep = relationsToUpsert.stream()
                .map(PaperQuestionRelationPO::getBizQuestionIdBase)
                .collect(Collectors.toSet());

        // 3: 【核心优化点】直接在数据库中逻辑删除那些不再需要的关系。
        // 即：更新所有 paperId 和 versionNumber 匹配，但 biz_question_id_base 不在 'baseIdsToKeep' 集合中的记录。
        // 这个操作避免了将旧数据全部查询到应用内存中，性能开销极小。
        // 注意：如果 baseIdsToKeep 为空，则会逻辑删除该试卷版本下的所有关系，逻辑正确。
        paperQuestionRelationPOMapper.deleteRelationsNotInSet(paperId, versionNumber, userId, baseIdsToKeep);

        // 4: 如果有需要保存的关系，则执行批量插入或更新。
        if (!relationsToUpsert.isEmpty()) {
            paperQuestionRelationPOMapper.batchInsertOrUpdate(relationsToUpsert);
        }
    }

    /**
     * 查询题库所属挑战卷业务ID
     * @param questionBank 题库对应题组对象
     * @return 挑战卷业务ID
     */
    private String getChallengePaperId(BigQuestionGroup questionBank) {
        Long paperPrimaryId = questionBank.getParentId();
        QuestionGroupPO questionGroup = questionGroupPOMapper.selectByPrimaryKey(paperPrimaryId);
        if(questionGroup == null){
            log.error("未查询到题库所属试卷,题库ID:{},挑战卷主键ID:{}", questionBank.getBizGroupId(), paperPrimaryId);
            throw new BizException("请选择正确的题库");
        }
        // 返回挑战卷业务ID
        return questionGroup.getBizGroupId();
    }

    /**
     * 保存试卷题目列表
     * @param parentBigQuestionGroup 父级题组(试卷/题库对应的题组对象)
     * @param paperQuestionListParam 题目列表参数对象
     * @param defaultVersionNumber 默认版本号
     * @param userId 用户ID
     * @return true:有更新/false:无更新
     */
    private Boolean saveQuestionGroups(BigQuestionGroup parentBigQuestionGroup, PaperQuestionListParam paperQuestionListParam, String defaultVersionNumber, Long userId) {
        // 获取题组业务ID
        String parentBizId = parentBigQuestionGroup.getBizGroupId();
        // 获取题组主键ID
        Long parentPrimaryId = parentBigQuestionGroup.getId();
        // 构建新题组对象
        List<BigQuestionGroup> newBigQuestionGroups = paperQuestionListParam.toBigQuestionGroupEntity(userId, parentPrimaryId, defaultVersionNumber);
        // 查询已有的题组对象列表
        List<BigQuestionGroup> oldBigQuestionGroups = getQuestions(parentBizId, defaultVersionNumber);
        Map<String, Long> oldIdMap = oldBigQuestionGroups.stream().collect(Collectors.toMap(BigQuestionGroup::getBizGroupId, BigQuestionGroup::getId));
        // 保存题组
        questionService.batchSaveBigQuestionsWithDelete(newBigQuestionGroups, oldIdMap, userId);

        return !CollectionUtils.isEmpty(newBigQuestionGroups) || !CollectionUtils.isEmpty(oldBigQuestionGroups);
    }

    /**
     * 获取默认版本试卷列表
     * @param param 试卷查询参数
     * @return 试卷对象列表
     */
    @Override
    public List<Paper> getDefaultVersionPaperList(PaperQueryParam param) {
        // 获取试卷类型对应的题组类型
        Integer questionGroupTypeCode = PaperTypeEnum.getQuestionGroupTypeCodeByCode(param.getPaperType());

        // 查询教材下所有试卷列表
        List<PaperBookRelationPO> paperBookRelationPOS = paperBookRelationPOMapper.selectList(param.getBookId());
        if(CollectionUtils.isEmpty(paperBookRelationPOS)){
            return List.of();
        }
        List<String> paperIds = paperBookRelationPOS.stream().map(PaperBookRelationPO::getPaperId).toList();

        // 查询试卷列表
        List<PaperPO> paperPOs = paperExtendPOMapper.selectLatestPaperList(paperIds, param.getPaperName(), questionGroupTypeCode, null);
        if(CollectionUtils.isEmpty(paperPOs)){
            return List.of();
        }

        // 转换为Paper对象
        return convertToPaperList(paperPOs);
    }

    /**
     * 将PaperPO列表转换为Paper列表
     * @param paperPOs 试卷PO列表
     * @return 试卷对象列表
     *  - 设置试卷包含题目数量
     *  - 设置创建人名称
     *  - 设置试卷是否被引用的标识
     *  - 设置试卷是否发布的标识
     */
    private List<Paper> convertToPaperList(List<PaperPO> paperPOs) {
        if (CollectionUtils.isEmpty(paperPOs)) {
            return List.of();
        }

        // 提取试卷ID和用户ID列表
        List<String> paperIds = paperPOs.stream().map(PaperPO::getPaperId).toList();
        List<Long> userIds = paperPOs.stream().map(PaperPO::getCreateBy).distinct().toList();

        // 并行执行三个批量查询，避免N+1问题
        CompletableFuture<Set<String>> referenceFuture = CompletableFuture
                .supplyAsync(() -> paperReferenceService.checkPaperReferenceExist(paperIds));

        CompletableFuture<Map<Long, String>> userMapFuture = CompletableFuture
                .supplyAsync(() -> userService.getUserNames(userIds));

        CompletableFuture<Map<String, Boolean>> publishFlagFuture = CompletableFuture
                .supplyAsync(() -> paperVersionService.isPublishedPaper(paperIds));

        // 等待所有查询完成
        CompletableFuture.allOf(referenceFuture, userMapFuture, publishFlagFuture).join();

        // 获取结果
        Set<String> referencePaperIds = referenceFuture.join();
        Map<Long, String> userMap = userMapFuture.join();
        Map<String, Boolean> publishFlagMap = publishFlagFuture.join();

        // 转换为Paper对象
        return paperPOs.stream()
                .map(po -> po.toEntity(
                        userMap.get(po.getCreateBy()),
                        referencePaperIds.contains(po.getPaperId()),
                        publishFlagMap.getOrDefault(po.getPaperId(), false)))
                .collect(Collectors.toList());
    }

    /**
     * 获取试卷详情
     * @param paperId 试卷ID
     * @param versionNumber 版本号
     * @return 试卷对象
     */
    @Override
    public Paper getPaperDetail(String paperId, String versionNumber, Boolean withContent) {
        String localVersionNumber = versionNumber==null ? IdentifierUtil.DEFAULT_VERSION_NUMBER : versionNumber;
        String cacheKey =  IdentifierUtil.DEFAULT_VERSION_NUMBER.equals(localVersionNumber) ? null : paperId + ":" + localVersionNumber;

        // 获取非编辑态试卷详情缓存数据
        Paper paper = cacheManagerConfig.getLocalCacheData(CacheManagerConfig.CACHE_NAME_PAPER_DETAIL, cacheKey);
        if(paper!=null){
            return paper;
        }

        // 查询试卷列表
        paper = localGetPaperDetail(paperId, localVersionNumber, withContent);
        if(paper==null){
            log.debug("试卷不存在:{}", paperId);
            throw new IllegalArgumentException("试卷不存在");
        }

        // 缓存试卷详情
        cacheManagerConfig.addLocalCacheData(CacheManagerConfig.CACHE_NAME_PAPER_DETAIL, cacheKey, paper);

        return paper;
    }

    /**
     * 获取试卷详情
     * @param paperId 试卷ID
     * @param localVersionNumber 本地版本号
     * @param withContent 是否包含内容
     * @return 试卷对象
     */
    private Paper localGetPaperDetail(String paperId, String localVersionNumber, Boolean withContent) {
        // 查询试卷列表
        PaperPO paperPO = paperExtendPOMapper.selectPaperDetail(paperId, localVersionNumber, withContent);
        if(paperPO==null){
            return null;
        }
        Paper paper = paperPO.toEntity();

        // 查询关联教材ID
        paper.setBookId(paperBookRelationPOMapper.selectBookIdByPaperId(paper.getPaperId()));

        // 取得章节引用试卷列表
        Set<String> referencePaperIds = paperReferenceService.checkPaperReferenceExist(List.of(paperPO.getPaperId()));
        paper.setReferenceFlag(referencePaperIds.contains(paperPO.getPaperId()));

        return paper;
    }

    /**
     * 获取题目列表
     *
     * @param parentBizGroupId 父级业务ID(试卷业务Id/题库业务Id)
     * @param versionNumber 版本号
     * @return 题目列表
     */
    @Override
    public List<BigQuestionGroup> getQuestions(String parentBizGroupId, String versionNumber) {
        // 查询题组未指定版本时，查询默认版本
        String version  = versionNumber == null ? IdentifierUtil.DEFAULT_VERSION_NUMBER : versionNumber;
        // 查询试卷或者题库下的题组列表
        List<QuestionGroupPO> questionGroupPOs = questionGroupPOMapper.selectChildByParentBizGroupId(parentBizGroupId, version);
        if(CollectionUtils.isEmpty(questionGroupPOs)){
            return List.of();
        }
        List<Long> questionPrimaryIds = questionGroupPOs.stream().map(QuestionGroupPO::getId).toList();

        // 返回大题组列表
        return questionService.batchGetBigQuestions(questionPrimaryIds);
    }

    /**
     * 删除试卷
     * 删除前检查是否有被教材章节引用
     * @param paperId 试卷业务ID
     * @param userId 当前用户ID
     * @return 是否删除成功
     */
    @Transactional
    @Override
    public Boolean deletePaper(String paperId, Long userId) {
        // 检查试卷是否已被教材章节引用
        Set<String> referencePaperIds = paperReferenceService.checkPaperReferenceExist(List.of(paperId));
        if(!CollectionUtils.isEmpty(referencePaperIds)){
            log.error("该测试被教材章节使用，不可删除。{}", paperId);
            throw new BizException("该测试被教材章节使用，不可删除。");
        }
        // 检查试卷是否已上架
        Set<String>  publishedPaperIds = paperReferenceService.getVersionedPaperReference(List.of(paperId));
        if(!CollectionUtils.isEmpty(publishedPaperIds)){
            log.error("该测试被保存在章节的版本中，不可删除。{}", paperId);
            throw new BizException("该测试被保存在章节的版本中，不可删除。");
        }

        // 删除教材与试卷关系
        PaperBookRelationPO paperBookRelationPO = new PaperBookRelationPO();
        paperBookRelationPO.setPaperId(paperId);
        paperBookRelationPO.setEnable(Boolean.FALSE);
        paperBookRelationPO.setUpdateBy(userId);
        paperBookRelationPOMapper.update(paperBookRelationPO);

        // 删除试卷
        Paper existPaper = deleteQuestionGroup(paperId, userId);
        // 输出操作日志
        paperOperationLogService.addPaperDeleteOperation(existPaper, userId);
        return Boolean.TRUE;
    }

    /**
     * 从题组表中删除试卷/题库
     * @param questionGroupId 试卷/题库ID
     * @param userId 当前用户ID
     * @return 试卷/题库对象
     */
    private Paper deleteQuestionGroup(String questionGroupId, Long userId) {
        // 查询试卷
        PaperPO paperPO = paperExtendPOMapper.selectPaperDetail(questionGroupId, IdentifierUtil.DEFAULT_VERSION_NUMBER, false);
        if (paperPO == null) {
            log.error("试卷不存在:{}", questionGroupId);
            throw new IllegalArgumentException("试卷/题库不存在");
        }
        Paper paper = paperPO.toEntity();

        // 删除试卷扩展信息
        paperExtendPOMapper.deleteDefaultVersion(paper.getPaperId());

        // 删除试卷
        Long groupId = questionService.deleteGroup(paper.getId(), userId);
        log.debug("删除试卷:{}", groupId);
        return paper;
    }
}
