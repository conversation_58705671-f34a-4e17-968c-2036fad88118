package com.unipus.digitalbook.service.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DeadlockLoserDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Supplier;

/**
 * 死锁防范服务
 * 提供数据库死锁的预防和处理机制
 * 
 * <AUTHOR>
 */
@Service
public class DeadlockPreventionService {
    
    private static final Logger logger = LoggerFactory.getLogger(DeadlockPreventionService.class);
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_ATTEMPTS = 3;
    
    /**
     * 基础重试延迟（毫秒）
     */
    private static final long BASE_RETRY_DELAY = 100;
    
    /**
     * 有序执行操作，按ID排序避免死锁
     * 
     * @param ids 需要操作的ID列表
     * @param operation 具体操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeWithOrderedLocking(List<Long> ids, Supplier<T> operation) {
        if (ids == null || ids.isEmpty()) {
            return operation.get();
        }
        
        // 按ID排序，确保所有线程以相同顺序获取锁
        List<Long> sortedIds = sortIds(ids);
        
        logger.debug("Executing operation with ordered IDs: {}", sortedIds);
        
        return executeWithDeadlockRetry(operation);
    }
    
    /**
     * 执行操作，遇到死锁时自动重试
     * 
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    @Retryable(
        value = {DeadlockLoserDataAccessException.class, SQLException.class},
        maxAttempts = MAX_RETRY_ATTEMPTS,
        backoff = @Backoff(delay = BASE_RETRY_DELAY, multiplier = 2, random = true)
    )
    public <T> T executeWithDeadlockRetry(Supplier<T> operation) {
        try {
            return operation.get();
        } catch (Exception e) {
            if (isDeadlockException(e)) {
                logger.warn("Deadlock detected, will retry. Error: {}", e.getMessage());
                // 添加随机延迟，减少重试时再次冲突的概率
                randomDelay();
                throw e;
            } else {
                throw e;
            }
        }
    }
    
    /**
     * 事务性操作，确保原子性
     * 
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class, timeout = 30)
    public <T> T executeInTransaction(Supplier<T> operation) {
        return executeWithDeadlockRetry(operation);
    }
    
    /**
     * 批量操作，分批处理避免长时间锁定
     * 
     * @param items 要处理的项目列表
     * @param batchSize 批次大小
     * @param batchOperation 批处理操作
     * @param <T> 项目类型
     */
    public <T> void executeBatchOperation(List<T> items, int batchSize, 
                                        java.util.function.Consumer<List<T>> batchOperation) {
        if (items == null || items.isEmpty()) {
            return;
        }
        
        logger.debug("Executing batch operation for {} items with batch size {}", items.size(), batchSize);
        
        for (int i = 0; i < items.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, items.size());
            List<T> batch = items.subList(i, endIndex);
            
            try {
                executeWithDeadlockRetry(() -> {
                    batchOperation.accept(batch);
                    return null;
                });
                
                logger.debug("Successfully processed batch {}-{}", i, endIndex - 1);
            } catch (Exception e) {
                logger.error("Failed to process batch {}-{}", i, endIndex - 1, e);
                throw e;
            }
        }
    }
    
    /**
     * 安全的批量更新操作
     * 按ID排序并分批处理
     * 
     * @param ids ID列表
     * @param batchSize 批次大小
     * @param updateOperation 更新操作
     */
    public void safeBatchUpdate(List<Long> ids, int batchSize, 
                              java.util.function.Consumer<List<Long>> updateOperation) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        
        // 排序ID列表
        List<Long> sortedIds = sortIds(ids);
        
        // 分批处理
        executeBatchOperation(sortedIds, batchSize, batch -> {
            logger.debug("Processing batch update for IDs: {}", batch);
            updateOperation.accept(batch);
        });
    }
    
    /**
     * 对ID列表进行排序
     */
    private List<Long> sortIds(List<Long> ids) {
        if (ids == null || ids.size() <= 1) {
            return ids;
        }
        
        List<Long> sortedIds = ids.stream()
            .distinct()
            .sorted()
            .collect(java.util.stream.Collectors.toList());
        
        return sortedIds;
    }
    
    /**
     * 判断是否为死锁异常
     */
    private boolean isDeadlockException(Exception e) {
        if (e instanceof DeadlockLoserDataAccessException) {
            return true;
        }
        
        if (e instanceof SQLException) {
            SQLException sqlException = (SQLException) e;
            // MySQL死锁错误码：1213
            // InnoDB Lock wait timeout错误码：1205
            return sqlException.getErrorCode() == 1213 || sqlException.getErrorCode() == 1205;
        }
        
        String message = e.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            return lowerMessage.contains("deadlock") || 
                   lowerMessage.contains("lock wait timeout") ||
                   lowerMessage.contains("try restarting transaction");
        }
        
        return false;
    }
    
    /**
     * 随机延迟，减少重试时的冲突
     */
    private void randomDelay() {
        try {
            long delay = ThreadLocalRandom.current().nextLong(50, 200);
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 获取建议的批次大小
     * 根据数据量动态调整
     */
    public int getRecommendedBatchSize(int totalSize) {
        if (totalSize <= 50) {
            return totalSize;
        } else if (totalSize <= 500) {
            return 50;
        } else if (totalSize <= 2000) {
            return 100;
        } else {
            return 200;
        }
    }
    
    /**
     * 检查当前是否存在活跃的死锁
     * 可用于监控告警
     */
    public boolean hasActiveDeadlocks() {
        // 这里可以添加查询数据库死锁信息的逻辑
        // 例如查询 INFORMATION_SCHEMA.INNODB_TRX 表
        return false;
    }
}