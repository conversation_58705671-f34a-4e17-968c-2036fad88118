package com.unipus.digitalbook.service.common;

import com.unipus.digitalbook.producer.DigitalBookMessageProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 异步处理服务
 * 提供异步任务执行和消息队列处理能力
 * 
 * <AUTHOR>
 */
@Service
public class AsyncProcessorService {
    
    private static final Logger logger = LoggerFactory.getLogger(AsyncProcessorService.class);
    
    @Autowired
    @Qualifier("highConcurrencyExecutor")
    private ExecutorService highConcurrencyExecutor;
    
    @Autowired
    @Qualifier("batchProcessExecutor")
    private ExecutorService batchProcessExecutor;
    
    @Autowired
    private DigitalBookMessageProducer messageProducer;
    
    /**
     * 异步执行任务
     * 
     * @param task 要执行的任务
     * @param <T> 返回类型
     * @return CompletableFuture
     */
    public <T> CompletableFuture<T> processAsync(Supplier<T> task) {
        logger.debug("Submitting async task");
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                T result = task.get();
                logger.debug("Async task completed successfully");
                return result;
            } catch (Exception e) {
                logger.error("Async task failed", e);
                throw new RuntimeException("异步任务执行失败", e);
            }
        }, highConcurrencyExecutor);
    }
    
    /**
     * 异步执行任务（无返回值）
     * 
     * @param task 要执行的任务
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> processAsync(Runnable task) {
        logger.debug("Submitting async runnable task");
        
        return CompletableFuture.runAsync(() -> {
            try {
                task.run();
                logger.debug("Async runnable task completed successfully");
            } catch (Exception e) {
                logger.error("Async runnable task failed", e);
                throw new RuntimeException("异步任务执行失败", e);
            }
        }, highConcurrencyExecutor);
    }
    
    /**
     * 批量异步处理
     * 
     * @param tasks 任务列表
     * @param <T> 返回类型
     * @return CompletableFuture列表
     */
    public <T> CompletableFuture<Void> submitBatch(List<Supplier<T>> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }
        
        logger.info("Submitting batch async tasks, count: {}", tasks.size());
        
        List<CompletableFuture<T>> futures = tasks.stream()
            .map(task -> processAsync(task))
            .collect(java.util.stream.Collectors.toList());
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    logger.error("Batch async processing failed", throwable);
                } else {
                    logger.info("Batch async processing completed successfully");
                }
            });
    }
    
    /**
     * 批量异步处理（无返回值）
     * 
     * @param tasks 任务列表
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> submitBatchRunnable(List<Runnable> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }
        
        logger.info("Submitting batch async runnable tasks, count: {}", tasks.size());
        
        List<CompletableFuture<Void>> futures = tasks.stream()
            .map(this::processAsync)
            .collect(java.util.stream.Collectors.toList());
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    logger.error("Batch async runnable processing failed", throwable);
                } else {
                    logger.info("Batch async runnable processing completed successfully");
                }
            });
    }
    
    /**
     * 分批异步处理
     * 
     * @param items 要处理的项目列表
     * @param batchSize 批次大小
     * @param processor 处理器
     * @param <T> 项目类型
     * @return CompletableFuture<Void>
     */
    public <T> CompletableFuture<Void> processBatchAsync(List<T> items, int batchSize, 
                                                        Consumer<List<T>> processor) {
        if (items == null || items.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }
        
        logger.info("Processing {} items in batches of {}", items.size(), batchSize);
        
        List<CompletableFuture<Void>> batchFutures = new java.util.ArrayList<>();
        
        for (int i = 0; i < items.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, items.size());
            List<T> batch = items.subList(i, endIndex);
            
            CompletableFuture<Void> batchFuture = CompletableFuture.runAsync(() -> {
                try {
                    processor.accept(batch);
                    logger.debug("Processed batch {}-{}", i, endIndex - 1);
                } catch (Exception e) {
                    logger.error("Failed to process batch {}-{}", i, endIndex - 1, e);
                    throw new RuntimeException("批次处理失败", e);
                }
            }, batchProcessExecutor);
            
            batchFutures.add(batchFuture);
        }
        
        return CompletableFuture.allOf(batchFutures.toArray(new CompletableFuture[0]))
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    logger.error("Batch async processing failed", throwable);
                } else {
                    logger.info("Batch async processing completed successfully");
                }
            });
    }
    
    /**
     * 发送异步消息到Kafka
     * 
     * @param topic 主题
     * @param message 消息内容
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> sendAsyncMessage(String topic, Object message) {
        return CompletableFuture.runAsync(() -> {
            try {
                messageProducer.sendMessage(topic, message);
                logger.debug("Async message sent to topic: {}", topic);
            } catch (Exception e) {
                logger.error("Failed to send async message to topic: {}", topic, e);
                throw new RuntimeException("发送异步消息失败", e);
            }
        }, highConcurrencyExecutor);
    }
    
    /**
     * 批量发送异步消息
     * 
     * @param topic 主题
     * @param messages 消息列表
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> sendBatchAsyncMessages(String topic, List<Object> messages) {
        if (messages == null || messages.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }
        
        logger.info("Sending {} async messages to topic: {}", messages.size(), topic);
        
        List<CompletableFuture<Void>> futures = messages.stream()
            .map(message -> sendAsyncMessage(topic, message))
            .collect(java.util.stream.Collectors.toList());
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    logger.error("Batch async message sending failed", throwable);
                } else {
                    logger.info("Batch async messages sent successfully");
                }
            });
    }
    
    /**
     * 延迟异步执行
     * 
     * @param task 要执行的任务
     * @param delayMillis 延迟时间（毫秒）
     * @param <T> 返回类型
     * @return CompletableFuture<T>
     */
    public <T> CompletableFuture<T> processAsyncDelayed(Supplier<T> task, long delayMillis) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(delayMillis);
                return task.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("延迟任务被中断", e);
            } catch (Exception e) {
                logger.error("Delayed async task failed", e);
                throw new RuntimeException("延迟异步任务执行失败", e);
            }
        }, highConcurrencyExecutor);
    }
    
    /**
     * 获取异步处理器状态
     * 
     * @return 状态信息
     */
    public AsyncProcessorStatus getStatus() {
        return new AsyncProcessorStatus()
            .setHighConcurrencyExecutorActive(highConcurrencyExecutor != null && !highConcurrencyExecutor.isShutdown())
            .setBatchProcessExecutorActive(batchProcessExecutor != null && !batchProcessExecutor.isShutdown())
            .setTimestamp(System.currentTimeMillis());
    }
    
    /**
     * 异步处理器状态
     */
    public static class AsyncProcessorStatus {
        private boolean highConcurrencyExecutorActive;
        private boolean batchProcessExecutorActive;
        private long timestamp;
        
        public boolean isHighConcurrencyExecutorActive() {
            return highConcurrencyExecutorActive;
        }
        
        public AsyncProcessorStatus setHighConcurrencyExecutorActive(boolean highConcurrencyExecutorActive) {
            this.highConcurrencyExecutorActive = highConcurrencyExecutorActive;
            return this;
        }
        
        public boolean isBatchProcessExecutorActive() {
            return batchProcessExecutorActive;
        }
        
        public AsyncProcessorStatus setBatchProcessExecutorActive(boolean batchProcessExecutorActive) {
            this.batchProcessExecutorActive = batchProcessExecutorActive;
            return this;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        public AsyncProcessorStatus setTimestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }
    }
}