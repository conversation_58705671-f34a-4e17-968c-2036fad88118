package com.unipus.digitalbook.service.highconcurrency;

import com.unipus.digitalbook.common.utils.DatabaseOperationUtils;
import com.unipus.digitalbook.dao.BatchOperationDataMapper;
import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationRequest;
import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationResponse;
import com.unipus.digitalbook.model.entity.highconcurrency.BatchOperationData;
import com.unipus.digitalbook.service.common.DeadlockPreventionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 批量操作服务
 * 实现高并发下的安全批量操作
 * 
 * <AUTHOR>
 */
@Service
public class BatchOperationService {
    
    private static final Logger logger = LoggerFactory.getLogger(BatchOperationService.class);
    
    @Autowired
    private BatchOperationDataMapper batchOperationDataMapper;
    
    @Autowired
    private DatabaseOperationUtils databaseOperationUtils;
    
    @Autowired
    private DeadlockPreventionService deadlockPreventionService;
    
    /**
     * 处理批量操作请求
     * 
     * @param request 批量操作请求
     * @return 操作结果
     */
    public BatchOperationResponse processBatchOperation(BatchOperationRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        String batchNo = generateBatchNo(request);
        
        logger.info("Starting batch operation: {}, type: {}, count: {}", 
            batchNo, request.getOperationType(), request.getDataItems().size());
        
        BatchOperationResponse response = new BatchOperationResponse()
            .setBatchNo(batchNo)
            .setStartTime(startTime)
            .setTotalCount(request.getDataItems().size());
        
        try {
            switch (request.getOperationType().toUpperCase()) {
                case "INSERT":
                    return processBatchInsert(request, response);
                case "UPDATE":
                    return processBatchUpdate(request, response);
                case "DELETE":
                    return processBatchDelete(request, response);
                default:
                    throw new IllegalArgumentException("不支持的操作类型: " + request.getOperationType());
            }
        } catch (Exception e) {
            logger.error("Batch operation failed: {}", batchNo, e);
            return response.setStatus("FAILED")
                .setSuccessCount(0)
                .setFailedCount(request.getDataItems().size())
                .setMessage("批量操作失败: " + e.getMessage())
                .setEndTime(LocalDateTime.now())
                .setProcessingTime(calculateProcessingTime(startTime));
        }
    }
    
    /**
     * 处理批量插入
     */
    private BatchOperationResponse processBatchInsert(BatchOperationRequest request, BatchOperationResponse response) {
        List<BatchOperationData> dataList = request.getDataItems().stream()
            .map(item -> convertToEntity(item, request))
            .collect(Collectors.toList());
        
        // 计算合适的批次大小
        int batchSize = databaseOperationUtils.calculateBatchSize(dataList.size(), 1);
        
        try {
            int insertedCount = databaseOperationUtils.safeBatchInsert(
                dataList, 
                batchSize,
                batchOperationDataMapper::batchInsert
            );
            
            List<Long> successIds = dataList.stream()
                .map(BatchOperationData::getId)
                .collect(Collectors.toList());
            
            return response.setStatus("SUCCESS")
                .setSuccessCount(insertedCount)
                .setFailedCount(0)
                .setSuccessIds(successIds)
                .setMessage("批量插入成功")
                .setEndTime(LocalDateTime.now())
                .setProcessingTime(calculateProcessingTime(response.getStartTime()));
                
        } catch (Exception e) {
            logger.error("Batch insert failed for batch: {}", response.getBatchNo(), e);
            throw e;
        }
    }
    
    /**
     * 处理批量更新
     */
    private BatchOperationResponse processBatchUpdate(BatchOperationRequest request, BatchOperationResponse response) {
        List<Long> ids = request.getDataItems().stream()
            .map(BatchOperationRequest.BatchDataItem::getId)
            .filter(id -> id != null)
            .collect(Collectors.toList());
        
        if (ids.isEmpty()) {
            throw new IllegalArgumentException("批量更新时ID不能为空");
        }
        
        // 计算合适的批次大小
        int batchSize = databaseOperationUtils.calculateBatchSize(ids.size(), 2);
        
        try {
            List<BatchOperationData> updateList = request.getDataItems().stream()
                .filter(item -> item.getId() != null)
                .map(item -> convertToUpdateEntity(item, request))
                .collect(Collectors.toList());
            
            int updatedCount = databaseOperationUtils.safeBatchUpdate(
                ids,
                batchSize,
                batchIds -> {
                    List<BatchOperationData> batchUpdateList = updateList.stream()
                        .filter(data -> batchIds.contains(data.getId()))
                        .collect(Collectors.toList());
                    return batchOperationDataMapper.batchUpdateByIds(batchUpdateList);
                }
            );
            
            return response.setStatus("SUCCESS")
                .setSuccessCount(updatedCount)
                .setFailedCount(request.getDataItems().size() - updatedCount)
                .setSuccessIds(ids)
                .setMessage("批量更新成功")
                .setEndTime(LocalDateTime.now())
                .setProcessingTime(calculateProcessingTime(response.getStartTime()));
                
        } catch (Exception e) {
            logger.error("Batch update failed for batch: {}", response.getBatchNo(), e);
            throw e;
        }
    }
    
    /**
     * 处理批量删除
     */
    private BatchOperationResponse processBatchDelete(BatchOperationRequest request, BatchOperationResponse response) {
        List<Long> ids = request.getDataItems().stream()
            .map(BatchOperationRequest.BatchDataItem::getId)
            .filter(id -> id != null)
            .collect(Collectors.toList());
        
        if (ids.isEmpty()) {
            throw new IllegalArgumentException("批量删除时ID不能为空");
        }
        
        // 计算合适的批次大小
        int batchSize = databaseOperationUtils.calculateBatchSize(ids.size(), 1);
        
        try {
            int deletedCount = databaseOperationUtils.safeBatchDelete(
                ids,
                batchSize,
                batchOperationDataMapper::batchDeleteByIds
            );
            
            return response.setStatus("SUCCESS")
                .setSuccessCount(deletedCount)
                .setFailedCount(ids.size() - deletedCount)
                .setSuccessIds(ids)
                .setMessage("批量删除成功")
                .setEndTime(LocalDateTime.now())
                .setProcessingTime(calculateProcessingTime(response.getStartTime()));
                
        } catch (Exception e) {
            logger.error("Batch delete failed for batch: {}", response.getBatchNo(), e);
            throw e;
        }
    }
    
    /**
     * 查询批次处理状态
     * 
     * @param batchNo 批次号
     * @return 处理状态
     */
    public BatchOperationResponse getBatchStatus(String batchNo) {
        List<BatchOperationDataMapper.StatusCount> statusCounts = 
            batchOperationDataMapper.countByStatus(batchNo);
        
        if (statusCounts.isEmpty()) {
            throw new IllegalArgumentException("批次不存在: " + batchNo);
        }
        
        int totalCount = statusCounts.stream()
            .mapToInt(BatchOperationDataMapper.StatusCount::getCount)
            .sum();
        
        int successCount = statusCounts.stream()
            .filter(sc -> "COMPLETED".equals(sc.getStatus()))
            .mapToInt(BatchOperationDataMapper.StatusCount::getCount)
            .sum();
        
        int failedCount = statusCounts.stream()
            .filter(sc -> "FAILED".equals(sc.getStatus()))
            .mapToInt(BatchOperationDataMapper.StatusCount::getCount)
            .sum();
        
        String status = determineOverallStatus(statusCounts);
        
        return new BatchOperationResponse()
            .setBatchNo(batchNo)
            .setStatus(status)
            .setTotalCount(totalCount)
            .setSuccessCount(successCount)
            .setFailedCount(failedCount);
    }
    
    /**
     * 转换为实体对象
     */
    private BatchOperationData convertToEntity(BatchOperationRequest.BatchDataItem item, BatchOperationRequest request) {
        return new BatchOperationData()
            .setId(item.getId())
            .setBusinessData(item.getBusinessData())
            .setStatus("PENDING")
            .setDataType(request.getDataType())
            .setBatchNo(request.getBatchNo())
            .setPriority(request.getPriority())
            .setExtraData(item.getExtraData())
            .setCreateTime(LocalDateTime.now())
            .setUpdateTime(LocalDateTime.now())
            .setVersion(1);
    }
    
    /**
     * 转换为更新实体对象
     */
    private BatchOperationData convertToUpdateEntity(BatchOperationRequest.BatchDataItem item, BatchOperationRequest request) {
        return new BatchOperationData()
            .setId(item.getId())
            .setBusinessData(item.getBusinessData())
            .setExtraData(item.getExtraData())
            .setUpdateTime(LocalDateTime.now())
            .setVersion(item.getVersion());
    }
    
    /**
     * 生成批次号
     */
    private String generateBatchNo(BatchOperationRequest request) {
        if (request.getBatchNo() != null && !request.getBatchNo().isEmpty()) {
            return request.getBatchNo();
        }
        return "BATCH_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * 计算处理时间
     */
    private Long calculateProcessingTime(LocalDateTime startTime) {
        return java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
    }
    
    /**
     * 确定整体状态
     */
    private String determineOverallStatus(List<BatchOperationDataMapper.StatusCount> statusCounts) {
        boolean hasProcessing = statusCounts.stream()
            .anyMatch(sc -> "PROCESSING".equals(sc.getStatus()) || "PENDING".equals(sc.getStatus()));
        
        if (hasProcessing) {
            return "PROCESSING";
        }
        
        boolean hasFailed = statusCounts.stream()
            .anyMatch(sc -> "FAILED".equals(sc.getStatus()));
        
        boolean hasSuccess = statusCounts.stream()
            .anyMatch(sc -> "COMPLETED".equals(sc.getStatus()));
        
        if (hasFailed && hasSuccess) {
            return "PARTIAL_SUCCESS";
        } else if (hasFailed) {
            return "FAILED";
        } else {
            return "SUCCESS";
        }
    }
}