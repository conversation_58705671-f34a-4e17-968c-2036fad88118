package com.unipus.digitalbook.service.judge;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

@Component
public class JudgeStrategyFactory  {

    private final Map<QuestionTypeEnum, JudgeStrategy<? extends Question>> strategies = new EnumMap<>(QuestionTypeEnum.class);

    @Autowired
    public JudgeStrategyFactory(ApplicationContext applicationContext) {
        Map<String, JudgeStrategy> strategyBeans =
                applicationContext.getBeansOfType(JudgeStrategy.class);
        for (Map.Entry<String, JudgeStrategy> entry : strategyBeans.entrySet()) {
            JudgeStrategy<?> strategy = entry.getValue();
            strategy.supportQuestionTypes().forEach(type -> strategies.put(type, strategy));
        }
    }

    public <T extends Question> JudgeStrategy<T> getStrategy(QuestionTypeEnum questionType) {
        return (JudgeStrategy<T>) strategies.get(questionType);
    }
}
