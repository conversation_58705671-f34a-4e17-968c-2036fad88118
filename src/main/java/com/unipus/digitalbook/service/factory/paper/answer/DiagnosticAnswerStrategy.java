package com.unipus.digitalbook.service.factory.paper.answer;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.DataOperateUtil;
import com.unipus.digitalbook.dao.PaperInstanceRelationPOMapper;
import com.unipus.digitalbook.dao.PaperQuestionInstancePOMapper;
import com.unipus.digitalbook.dao.PaperScoreBatchPOMapper;
import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.*;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.tag.Tag;
import com.unipus.digitalbook.model.enums.*;
import com.unipus.digitalbook.model.po.paper.PaperInstanceRelationPO;
import com.unipus.digitalbook.model.po.paper.PaperQuestionInstancePO;
import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 诊断卷答案策略
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DiagnosticAnswerStrategy extends AbstractPaperAnswerStrategy{
    private final TagProcessor tagProcessor;
    private final PaperInstanceRelationPOMapper paperInstanceRelationPOMapper;
    private final PaperQuestionInstancePOMapper paperQuestionInstancePOMapper;
    private final PaperScoreBatchPOMapper paperScoreBatchPOMapper;
    private final DataOperateUtil dataOperateUtil;

    /**
     * 获取试卷类型
     */
    @Override
    public PaperTypeEnum getPaperType() {
        return PaperTypeEnum.DIAGNOSTIC;
    }

    /**
     * 提交用户作答，并返回用户试卷信息
     * @param paperInstance 试卷实例
     * @param userAnswerMap 用户作答信息
     * @param userAccessInfo 用户访问基本信息
     * @return UserPaperInfo 用户试卷信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public UserPaperInfo submit(PaperInstance paperInstance, Map<String, List<UserAnswer>> userAnswerMap, UserAccessInfo userAccessInfo) {

        // 1.构建用户试卷成绩提交扩展信息
        UserPaperSubmitExtInfo ext = super.buildUserPaperSubmitExtInfo(paperInstance, userAnswerMap, userAccessInfo);

        // 2.提交用户作答，取得作答成绩
        List<UserQuestionScore> userQuestionScores = super.submit(paperInstance.getBigQuestionGroupList(), userAnswerMap);

        // 3.构建用户作答成绩信息实体
        UserPaperScore score = new UserPaperScore(paperInstance.getScoreBatchId(), paperInstance.getInstanceId(),
                PaperTypeEnum.DIAGNOSTIC, userQuestionScores, null, PaperSubmitStatusEnum.SUBMITTED);

        // 4.保存用户挑战卷成绩信息
        this.saveUserPaperRecord(paperInstance, score);

        // 5.构建用户试卷信息
        UserPaperInfo userPaperInfo = UserPaperInfo.build(paperInstance, score.getUserScore(), PaperSubmitStatusEnum.SUBMITTED);

        // 6.自动提交成绩到UAI
        if(UnitTestModeEnum.DIAGNOSIS.match(paperInstance.getTestMode())) {
            // 诊断模式同步成绩到UAI，并更新成绩批次状态为已提交
            super.syncUserPaperScore(userPaperInfo, ext);
        }else{
            // 推荐模式仅更新成绩批次状态为已提交
            super.updateUserPaperScoreBatch(userPaperInfo);
        }

        return userPaperInfo;
    }

    @Override
    public void saveUserPaperRecord(PaperInstance paperInstance, UserPaperScore userPaperScore) {
        // 保存用户试卷记录
        super.saveUserPaperRecord(paperInstance, userPaperScore);

        // 添加诊断卷实例关系
        saveDiagnosticInstanceRelation(paperInstance);
    }

    /**
     * 保存诊断卷实例关系
     * @param paperInstance 试卷实例
     */
    private void saveDiagnosticInstanceRelation(PaperInstance paperInstance) {
        if (UnitTestModeEnum.DIAGNOSIS.match(paperInstance.getTestMode())) {
            // 诊断模式，创建实例关系基础数据
            dataOperateUtil.saveOrUpdateWithLock(new PaperInstanceRelationPO(paperInstance),
                    p -> "diagnostic:instance:relation:" + paperInstance.getInstanceId(),
                    paperInstanceRelationPOMapper::selectCount,
                    paperInstanceRelationPOMapper::insert,null);
        } else {
            // 推荐模式，更新关联的试卷实例ID
            String baseInstanceId = paperInstance.getRelatedInstanceId();
            PaperInstanceRelationPO relationPO = new PaperInstanceRelationPO(baseInstanceId, paperInstance);
            int count = paperInstanceRelationPOMapper.updateTargetInstanceId(relationPO);
            if(count==0){
                log.error("更新推荐卷实例关系失败，baseInstanceId: {}, targetInstanceId: {}",
                        paperInstance.getRelatedInstanceId(), paperInstance.getInstanceId());
                throw new BizException("更新推荐卷实例关系失败");
            }
        }
    }

    /**
     * 获取用户最后的试卷成绩批次记录
     * 根据测试模式（诊断模式/测试模式）获取不同的实例
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 测试模式
     * @return 用户作答记录
     */
    @Override
    protected PaperScoreBatchPO getLatestPaperScoreBatch(String paperId, String versionNumber, String openId,
                                                         Long tenantId, UnitTestModeEnum testMode){
        if(testMode==null){
            log.debug("未指定测试模式: {}, {}, {}, {}", paperId, versionNumber, openId, tenantId);
            throw new IllegalArgumentException("诊断卷未指定测试模式");
        }

        // 获取最新的试卷实例关系
        PaperInstanceRelationPO relation = getLatestInstanceRelation(paperId, versionNumber, openId, tenantId);
        if(relation==null){
            log.debug("不存在实例关系: {}, {}, {}, {}, {}", paperId, versionNumber, openId, tenantId, testMode);
            return null;
        }
        // 根据测试模式，获取试卷实例ID
        String instanceId = UnitTestModeEnum.DIAGNOSIS.match(testMode) ? relation.getBaseInstanceId() : relation.getTargetInstanceId();
        if(!StringUtils.hasText(instanceId)){
            log.debug("不存在推荐卷实例ID: {}, {}, {}, {}, {}", paperId, versionNumber, openId, tenantId, testMode);
            return null;
        }
        return paperScoreBatchPOMapper.gePaperScoreBatchByInstanceId(instanceId, null);
    }

    /**
     * 获取用户试卷成绩
     * 查询试卷的用户成绩，可用于判断试卷是否已完成
     * @param userAccessInfo 用户访问信息
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @param testMode 试卷测试模式
     * @return UserPaperInfo 用户试卷得分信息
     */
    @Override
    public UserPaperScore getUserPaperScore(String paperId, String versionNumber, UserAccessInfo userAccessInfo, UnitTestModeEnum testMode){
        // 获取用户试卷成绩
        UserPaperScore userPaperScore = super.getUserPaperScore(paperId, versionNumber, userAccessInfo, testMode);
        if(userPaperScore==null){
            log.debug("用户试卷成绩不存在，paperId:{}, versionNumber:{}, userAccessInfo:{}", paperId, versionNumber, userAccessInfo);
            return null;
        }

        // 通过试卷小题ID列表获取知识点
        userPaperScore.setTags(tagProcessor.processKnowledgePoints(paperId, userPaperScore.getSmallQuestionIds(), versionNumber));

        // 取得诊断卷关联的推荐卷作答记录
        if(UnitTestModeEnum.DIAGNOSIS.match(testMode)) {
            RecommendedPaperInstanceInfo info = getRecommendUserPaperInfo(paperId, versionNumber, userAccessInfo);
            if(info != null){
                userPaperScore.setRecommendInstanceId(info.instanceId);
                userPaperScore.setIncorrectRecommendTags(info.tags);
                userPaperScore.setRecommendSubmitStatus(info.status);
            }
        }

        // 返回用户试卷成绩信息
        return userPaperScore;
    }

    private record RecommendedPaperInstanceInfo(String instanceId, List<Tag> tags, PaperSubmitStatusEnum status) {}

    /**
     * 获取推荐试卷信息 (仅包含客观题)
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @param userAccessInfo 用户访问信息
     * @return 推荐试卷信息
     */
    private RecommendedPaperInstanceInfo getRecommendUserPaperInfo(String paperId, String versionNumber, UserAccessInfo userAccessInfo) {
        String openId = userAccessInfo.getOpenId();
        Long tenantId = userAccessInfo.getTenantId();

        PaperInstanceRelationPO relation = getLatestInstanceRelation(paperId, null, openId, tenantId);
        if(relation==null || !StringUtils.hasText(relation.getTargetInstanceId())){
            log.debug("无推荐实例，paperId:{}, versionNumber:{}, openId:{}", paperId, versionNumber, openId);
            return null;
        }
        String targetInstanceId = relation.getTargetInstanceId();
        String latestPaperVersionNumber = relation.getPaperVersionNumber();

        // 根据推荐卷实例ID查询成绩批次信息
        PaperScoreBatchPO targetScoreBatchPO = paperScoreBatchPOMapper.gePaperScoreBatchByInstanceId(targetInstanceId, null);
        if(targetScoreBatchPO == null){
            log.debug("未找到推荐卷的成绩批次信息，instanceId: {}", targetInstanceId);
            return null;
        }
        if(!PaperSubmitStatusEnum.SUBMITTED.match(targetScoreBatchPO.getStatus())){
            log.debug("推荐卷尚未提交，无法获取错题分析, instanceId: {}, status: {}", targetInstanceId, targetScoreBatchPO.getStatus());
            return new RecommendedPaperInstanceInfo(targetInstanceId, null, PaperSubmitStatusEnum.UNSUBMITTED);
        }

        // 仅查询该推荐实例下作答错误的小题（最小字段集合）
        List<PaperQuestionInstancePO> incorrectInstances = paperQuestionInstancePOMapper.selectIncorrectByRoundId(targetInstanceId);

        // 仅保留客观题的小题ID
        List<String> recommendedSmallQuestionIds = incorrectInstances.stream()
                .filter(q -> QuestionGroupTypeEnum.isObjective(q.getQuestionGroupType()))
                .map(PaperQuestionInstancePO::getQuestionBizId)
                .toList();

        // 获取错题知识点标签（题目为空时会返回空列表，避免不必要的数据库访问）
        List<Tag> incorrectTags = tagProcessor.processKnowledgePoints(paperId, recommendedSmallQuestionIds, latestPaperVersionNumber);
        if(!CollectionUtils.isEmpty(incorrectTags)){
            // 仅返回二级标签
            incorrectTags = incorrectTags.stream().filter(tag -> TagLevelEnum.SECOND.match(tag.getLevel())).toList();
        }
        return new RecommendedPaperInstanceInfo(targetInstanceId, incorrectTags, PaperSubmitStatusEnum.SUBMITTED);
    }

    /**
     * 获取诊断卷最新实例关系
      *
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @return 最新实例关系
     */
    private PaperInstanceRelationPO getLatestInstanceRelation(String paperId, String versionNumber, String openId, Long tenantId) {
        // 查询试卷最新推荐关系
        PaperInstanceRelationPO paperInstanceRelationPO = paperInstanceRelationPOMapper.selectLatestInstanceRelation(
                paperId, versionNumber, PaperInstanceRelationTypeEnum.RECOMMEND.getCode(), tenantId, openId);
        if (paperInstanceRelationPO == null) {
            log.debug("未找到推荐卷实例关系，paperId:{}, versionNumber:{}, openId:{}", paperId, versionNumber, openId);
            return null;
        }
        return paperInstanceRelationPO;
    }
}
