package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.aop.lock.DistributedLock;
import com.unipus.digitalbook.aop.ratelimit.RateLimit;
import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationRequest;
import com.unipus.digitalbook.model.dto.highconcurrency.BatchOperationResponse;
import com.unipus.digitalbook.service.highconcurrency.BatchOperationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 高并发控制器
 * 提供支持高并发访问的接口，同时避免数据库死锁
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/high-concurrency")
@Tag(name = "高并发接口", description = "支持高并发访问的批量操作接口")
@Validated
public class HighConcurrencyController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(HighConcurrencyController.class);
    
    @Autowired
    private BatchOperationService batchOperationService;
    
    /**
     * 批量数据操作接口
     * 支持插入、更新、删除操作，具备限流和分布式锁保护
     * 
     * @param request 批量操作请求
     * @return 操作结果
     */
    @PostMapping("/batch-operation")
    @Operation(summary = "批量数据操作", description = "支持批量插入、更新、删除操作，具备死锁防范机制")
    @RateLimit(maxRequests = 50, timeWindow = 60, limitType = RateLimit.LimitType.USER, 
               message = "批量操作过于频繁，请稍后再试")
    @DistributedLock(key = "'batch_operation:' + #request.operationType + ':' + #request.dataType", 
                    waitTime = 3000, leaseTime = 60000,
                    failMessage = "系统正在处理相同类型的批量操作，请稍后再试")
    public ResponseEntity<BatchOperationResponse> batchOperation(
            @Valid @RequestBody BatchOperationRequest request) {
        
        logger.info("Received batch operation request: type={}, dataType={}, count={}", 
            request.getOperationType(), request.getDataType(), request.getDataItems().size());
        
        try {
            BatchOperationResponse response = batchOperationService.processBatchOperation(request);
            
            logger.info("Batch operation completed: batchNo={}, status={}, success={}, failed={}", 
                response.getBatchNo(), response.getStatus(), 
                response.getSuccessCount(), response.getFailedCount());
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid batch operation request: {}", e.getMessage());
            return ResponseEntity.badRequest().body(
                new BatchOperationResponse()
                    .setStatus("FAILED")
                    .setMessage(e.getMessage())
            );
        } catch (Exception e) {
            logger.error("Batch operation failed", e);
            return ResponseEntity.internalServerError().body(
                new BatchOperationResponse()
                    .setStatus("FAILED")
                    .setMessage("批量操作执行失败，请稍后重试")
            );
        }
    }
    
    /**
     * 查询批次处理状态
     * 
     * @param batchNo 批次号
     * @return 处理状态
     */
    @GetMapping("/batch-status/{batchNo}")
    @Operation(summary = "查询批次状态", description = "根据批次号查询批量操作的处理状态")
    @RateLimit(maxRequests = 200, timeWindow = 60, limitType = RateLimit.LimitType.USER)
    public ResponseEntity<BatchOperationResponse> getBatchStatus(
            @Parameter(description = "批次号") 
            @PathVariable @NotBlank(message = "批次号不能为空") String batchNo) {
        
        logger.debug("Querying batch status: {}", batchNo);
        
        try {
            BatchOperationResponse response = batchOperationService.getBatchStatus(batchNo);
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Batch not found: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.error("Failed to query batch status: {}", batchNo, e);
            return ResponseEntity.internalServerError().body(
                new BatchOperationResponse()
                    .setStatus("ERROR")
                    .setMessage("查询批次状态失败")
            );
        }
    }
    
    /**
     * 高并发查询接口
     * 演示缓存和数据库查询的结合使用
     * 
     * @param id 数据ID
     * @return 查询结果
     */
    @GetMapping("/query/{id}")
    @Operation(summary = "高并发查询", description = "支持高并发的数据查询接口，具备缓存机制")
    @RateLimit(maxRequests = 1000, timeWindow = 60, limitType = RateLimit.LimitType.USER)
    public ResponseEntity<Object> queryData(
            @Parameter(description = "数据ID") 
            @PathVariable Long id) {
        
        logger.debug("Querying data: {}", id);
        
        try {
            // 这里可以添加缓存查询逻辑
            // Object result = cacheService.get("data:" + id);
            // if (result == null) {
            //     result = dataService.queryById(id);
            //     cacheService.set("data:" + id, result);
            // }
            
            // 暂时返回模拟数据
            return ResponseEntity.ok(java.util.Map.of(
                "id", id,
                "status", "SUCCESS",
                "message", "查询成功",
                "timestamp", System.currentTimeMillis()
            ));
            
        } catch (Exception e) {
            logger.error("Query failed for id: {}", id, e);
            return ResponseEntity.internalServerError().body(
                java.util.Map.of(
                    "status", "ERROR",
                    "message", "查询失败"
                )
            );
        }
    }
    
    /**
     * 健康检查接口
     * 
     * @return 系统状态
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查高并发模块的运行状态")
    public ResponseEntity<Object> healthCheck() {
        return ResponseEntity.ok(java.util.Map.of(
            "status", "UP",
            "module", "high-concurrency",
            "timestamp", System.currentTimeMillis(),
            "message", "高并发模块运行正常"
        ));
    }
    
    /**
     * 获取系统负载信息
     * 
     * @return 负载信息
     */
    @GetMapping("/metrics")
    @Operation(summary = "系统指标", description = "获取高并发模块的运行指标")
    @RateLimit(maxRequests = 10, timeWindow = 60, limitType = RateLimit.LimitType.IP)
    public ResponseEntity<Object> getMetrics() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            return ResponseEntity.ok(java.util.Map.of(
                "jvm", java.util.Map.of(
                    "totalMemory", totalMemory,
                    "freeMemory", freeMemory,
                    "usedMemory", usedMemory,
                    "memoryUsagePercent", (double) usedMemory / totalMemory * 100
                ),
                "system", java.util.Map.of(
                    "availableProcessors", runtime.availableProcessors(),
                    "timestamp", System.currentTimeMillis()
                )
            ));
        } catch (Exception e) {
            logger.error("Failed to get metrics", e);
            return ResponseEntity.internalServerError().body(
                java.util.Map.of("error", "获取系统指标失败")
            );
        }
    }
}