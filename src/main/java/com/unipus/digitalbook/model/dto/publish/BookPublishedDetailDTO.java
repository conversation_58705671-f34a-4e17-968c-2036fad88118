package com.unipus.digitalbook.model.dto.publish;

import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.BookChangeItemEntity;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.enums.PublishContentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Schema(description = "教材上架版本详情实体类")
public class BookPublishedDetailDTO implements Serializable {

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "教材名称")
    private String bookName;

    @Schema(description = "上架次数")
    private Integer publishedCount;

    @Schema(description = "当前版本的上架版本")
    private String currentVersionNumber;

    @Schema(description = "当前版本的上架格式版本")
    private String currentFormatVersionNumber;

    @Schema(description = "当前版本的上架时间")
    private Long currentVersionTime;

    @Schema(description = "前一版本的上架版本")
    private String previousVersionNumber;

    @Schema(description = "前一版本的上架格式版本")
    private String previousFormatVersionNumber;

    @Schema(description = "前一版本的的上架时间")
    private Long previousVersionTime;

    @Schema(description = "资源发布列表")
    private List<BookPublishedDetailResourceDTO> publishedResources;

    public BookPublishedDetailDTO(Book currentBook, Book previousBook, List<BookChangeItemEntity> currentPublishedItemList) {
        this.bookId = currentBook.getId();
        this.bookName = currentBook.getChineseName();
        // 设置版本信息
        if (currentBook.getBookVersion() != null) {
            BookVersion currentBookVersion = currentBook.getBookVersion();
            this.publishedCount = currentBookVersion.getSortOrder();
            this.currentVersionNumber = currentBookVersion.getVersionNum();
            this.currentVersionTime = currentBookVersion.getCreateTime() != null ? currentBookVersion.getCreateTime().getTime() : null;
            this.currentFormatVersionNumber = currentBookVersion.getShowVersionNumber();
        }
        boolean hasPreviousBook = previousBook != null;
        if (hasPreviousBook && previousBook.getBookVersion() != null) {
            BookVersion previousBookVersion = previousBook.getBookVersion();
            this.previousVersionNumber = previousBookVersion.getVersionNum();
            this.previousVersionTime = previousBookVersion.getCreateTime() != null ? previousBookVersion.getCreateTime().getTime() : null;
            this.previousFormatVersionNumber = previousBookVersion.getShowVersionNumber();
        }
        // 处理资源信息
        List<BookPublishedDetailResourceDTO> tempPublishedResources = new ArrayList<>();
        if (currentPublishedItemList == null) {
            currentPublishedItemList = new ArrayList<>();
        }
        // 处理基础资源信息
        processMetadataResources(tempPublishedResources, currentBook, previousBook, hasPreviousBook, currentPublishedItemList);
        // 处理配套资源
        processComplementResources(tempPublishedResources, currentBook, previousBook, hasPreviousBook, currentPublishedItemList);
        // 处理章节信息
        processChapterResources(tempPublishedResources, currentBook, previousBook, hasPreviousBook, currentPublishedItemList);

        this.publishedResources = tempPublishedResources;
    }

    /**
     * 设置资源变更标识，并考虑当前上架包列表
     */
    private void setResourceChangeFlag(BookPublishedDetailResourceDTO resource, List<Long> currentPublishedVersionIdList) {
        // 默认无变更
        int changeFlag = 0;
        if (resource.getCurrentVersionId() == null) {
            // 情况1: 删除 - 当前版本不存在
            changeFlag = 3;
        } else if (resource.getPreviousVersionId() == null) {
            // 情况2: 新增 - 前一版本不存在
            changeFlag = 1;
        } else if (!Objects.equals(resource.getCurrentVersionId(), resource.getPreviousVersionId())) {
            // 情况3: 更新 - 版本ID不同
            changeFlag = 2;
        } else if (currentPublishedVersionIdList != null && currentPublishedVersionIdList.contains(resource.getCurrentVersionId())) {
            // 情况4: 特殊更新 - 版本ID相同但在当前上架包中
            changeFlag = 2;
        }
        resource.setChangeFlag(changeFlag);
    }

    /**
     * 处理基础资源信息（基本信息、教材简介、版权信息）
     */
    private void processMetadataResources(List<BookPublishedDetailResourceDTO> publishedResources,
                                          Book currentBook, Book previousBook, boolean hasPreviousBook,
                                          List<BookChangeItemEntity> currentPublishedItemList) {
        // 处理基本信息
        if (currentBook.getBookBasic() != null || (hasPreviousBook && previousBook.getBookBasic() != null)) {
            BookPublishedDetailResourceDTO basicResource = new BookPublishedDetailResourceDTO(currentBook.getId(), PublishContentTypeEnum.BASIC_INFO,
                    currentBook.getBookBasic(), hasPreviousBook ? previousBook.getBookBasic() : null);
            // 设置变更标识，并考虑当前上架包列表
            List<Long> basicInfoVersionIdList = currentPublishedItemList.stream()
                    .filter(item -> PublishContentTypeEnum.BASIC_INFO.match(item.getTypeCode()))
                    .map(BookChangeItemEntity::getVersionId).toList();
            setResourceChangeFlag(basicResource, basicInfoVersionIdList);
            if (basicResource.getChangeFlag() != 0) {
                publishedResources.add(basicResource);
            }
        }
        // 处理教材简介
        if (currentBook.getBookIntro() != null || (hasPreviousBook && previousBook.getBookIntro() != null)) {
            BookPublishedDetailResourceDTO introResource = new BookPublishedDetailResourceDTO(currentBook.getId(), PublishContentTypeEnum.BOOK_INTRO,
                    currentBook.getBookIntro(), hasPreviousBook ? previousBook.getBookIntro() : null);
            // 设置变更标识，并考虑当前上架包列表
            List<Long> bookIntroVersionIdList = currentPublishedItemList.stream()
                    .filter(item -> PublishContentTypeEnum.BOOK_INTRO.match(item.getTypeCode()))
                    .map(BookChangeItemEntity::getVersionId).toList();
            setResourceChangeFlag(introResource, bookIntroVersionIdList);
            if (introResource.getChangeFlag() != 0) {
                publishedResources.add(introResource);
            }
        }
        // 处理版权信息
        if (currentBook.getBookCopyright() != null || (hasPreviousBook && previousBook.getBookCopyright() != null)) {
            BookPublishedDetailResourceDTO copyrightResource = new BookPublishedDetailResourceDTO(currentBook.getId(), PublishContentTypeEnum.COPYRIGHT_INFO,
                    currentBook.getBookCopyright(), hasPreviousBook ? previousBook.getBookCopyright() : null);
            // 设置变更标识，并考虑当前上架包列表
            List<Long> copyrightInfoVersionIdList = currentPublishedItemList.stream()
                    .filter(item -> PublishContentTypeEnum.COPYRIGHT_INFO.match(item.getTypeCode()))
                    .map(BookChangeItemEntity::getVersionId).toList();
            setResourceChangeFlag(copyrightResource, copyrightInfoVersionIdList);
            if (copyrightResource.getChangeFlag() != 0) {
                publishedResources.add(copyrightResource);
            }
        }
    }

    /**
     * 处理配套资源信息
     */
    private void processComplementResources(List<BookPublishedDetailResourceDTO> publishedResources,
                                            Book currentBook, Book previousBook, boolean hasPreviousBook,
                                            List<BookChangeItemEntity> currentPublishedItemList) {
        // 构建当前版本配套资源映射
        Map<String, ComplementResource> currentComplementResourceMap = new HashMap<>();
        if (currentBook.getComplementResourceList() != null
                && !CollectionUtils.isEmpty(currentBook.getComplementResourceList().getComplementResourceList())) {
            currentComplementResourceMap = currentBook.getComplementResourceList().getComplementResourceList().stream()
                    .collect(Collectors.toMap(ComplementResource::getResourceId, resource -> resource));
        }
        // 构建前一版本配套资源映射
        Map<String, ComplementResource> previousComplementResourceMap = new HashMap<>();
        if (hasPreviousBook && previousBook.getComplementResourceList() != null
                && !CollectionUtils.isEmpty(previousBook.getComplementResourceList().getComplementResourceList())) {
            previousComplementResourceMap = previousBook.getComplementResourceList().getComplementResourceList().stream()
                    .collect(Collectors.toMap(ComplementResource::getResourceId, resource -> resource));
        }
        // 添加当前版本配套资源
        if (currentBook.getComplementResourceList() != null
                && !CollectionUtils.isEmpty(currentBook.getComplementResourceList().getComplementResourceList())) {
            for (ComplementResource currentComplementResource : currentBook.getComplementResourceList().getComplementResourceList()) {
                BookPublishedDetailResourceDTO resource = new BookPublishedDetailResourceDTO(currentBook.getId(), PublishContentTypeEnum.COMPLEMENT_RESOURCE,
                        currentComplementResource, previousComplementResourceMap.get(currentComplementResource.getResourceId()));
                // 设置变更标识，并考虑当前上架包列表
                List<Long> currentPublishedVersionIdList = currentPublishedItemList.stream()
                        .filter(item -> PublishContentTypeEnum.COMPLEMENT_RESOURCE.match(item.getTypeCode()))
                        .map(BookChangeItemEntity::getVersionId).toList();
                setResourceChangeFlag(resource, currentPublishedVersionIdList);
                if (resource.getChangeFlag() != 0) {
                    publishedResources.add(resource);
                }
            }
        }
        // 处理已删除的配套资源
        if (hasPreviousBook && previousBook.getComplementResourceList() != null
                && !CollectionUtils.isEmpty(previousBook.getComplementResourceList().getComplementResourceList())) {
            for (ComplementResource previousComplementResource : previousBook.getComplementResourceList().getComplementResourceList()) {
                if (!currentComplementResourceMap.containsKey(previousComplementResource.getResourceId())) {
                    BookPublishedDetailResourceDTO resource = new BookPublishedDetailResourceDTO(currentBook.getId(), PublishContentTypeEnum.COMPLEMENT_RESOURCE,
                            null, previousComplementResource);
                    // 设置变更标识，删除的资源不需要考虑当前上架包列表
                    setResourceChangeFlag(resource, null);
                    if (resource.getChangeFlag() != 0) {
                        publishedResources.add(resource);
                    }
                }
            }
        }
    }

    /**
     * 处理章节资源信息
     */
    private void processChapterResources(List<BookPublishedDetailResourceDTO> publishedResources,
                                         Book currentBook, Book previousBook, boolean hasPreviousBook,
                                         List<BookChangeItemEntity> currentPublishedItemList) {
        // 处理章节信息
        Map<String, Chapter> currentChapterMap = new HashMap<>();
        if (currentBook.getChapterList() != null) {
            currentChapterMap = currentBook.getChapterList().stream()
                    .filter(chapter -> chapter != null && chapter.getId() != null)
                    .collect(Collectors.toMap(Chapter::getId, chapter -> chapter));
        }
        Map<String, Chapter> previousChapterMap = new HashMap<>();
        if (hasPreviousBook && previousBook.getChapterList() != null) {
            previousChapterMap = previousBook.getChapterList().stream()
                    .filter(chapter -> chapter != null && chapter.getId() != null)
                    .collect(Collectors.toMap(Chapter::getId, chapter -> chapter));
        }
        // 添加当前版本章节
        if (currentBook.getChapterList() != null) {
            for (Chapter currentChapter : currentBook.getChapterList()) {
                if (currentChapter != null && currentChapter.getId() != null) {
                    Map<String, Object> property = new HashMap<>();
                    property.put("currentChapterNumber", currentChapter.getChapterNumber());

                    Chapter previousChapter = previousChapterMap.get(currentChapter.getId());
                    boolean chapterNameUpdated = false;
                    if (previousChapter != null) {
                        property.put("previousChapterNumber", previousChapter.getChapterNumber());
                        chapterNameUpdated = !currentChapter.getName().equals(previousChapter.getName());
                    }
                    property.put("chapterNameUpdated", chapterNameUpdated ? 1 : 0);

                    // 处理试卷信息
                    List<Paper> currentChapterPaperList = currentChapter.getChapterVersion() != null ? currentChapter.getChapterVersion().getPaperList() : null;
                    List<Paper> previousChapterPaperList = previousChapter != null && previousChapter.getChapterVersion() != null ? previousChapter.getChapterVersion().getPaperList() : null;
                    List<BookPublishedDetailResourceDTO> paperResourceList = processPaperResources(
                            currentBook.getId(), currentChapterPaperList, previousChapterPaperList, currentPublishedItemList);
                    property.put("chapterPaperList", paperResourceList);

                    BookPublishedDetailResourceDTO currentResource = new BookPublishedDetailResourceDTO(currentBook.getId(), PublishContentTypeEnum.CHAPTER_CONTENT,
                            currentChapter, previousChapter);
                    currentResource.setProperty(property);
                    // 设置变更标识，并考虑当前上架包列表
                    List<Long> currentPublishedVersionIdList = currentPublishedItemList.stream()
                            .filter(item -> PublishContentTypeEnum.CHAPTER_CONTENT.match(item.getTypeCode()))
                            .map(BookChangeItemEntity::getVersionId).toList();
                    setResourceChangeFlag(currentResource, currentPublishedVersionIdList);
                    if (currentResource.getChangeFlag() != 0) {
                        publishedResources.add(currentResource);
                    }
                }
            }
        }
        // 添加已删除的章节
        if (hasPreviousBook && previousBook.getChapterList() != null) {
            for (Chapter previousChapter : previousBook.getChapterList()) {
                if (previousChapter != null && previousChapter.getId() != null && !currentChapterMap.containsKey(previousChapter.getId())) {
                    Map<String, Object> property = new HashMap<>();
                    property.put("previousChapterNumber", previousChapter.getChapterNumber());
                    // 处理试卷信息
                    List<BookPublishedDetailResourceDTO> paperResourceList = processPaperResources(
                            currentBook.getId(), null, previousChapter.getChapterVersion() != null ? previousChapter.getChapterVersion().getPaperList() : null, currentPublishedItemList);
                    property.put("chapterPaperList", paperResourceList);

                    BookPublishedDetailResourceDTO previousResource = new BookPublishedDetailResourceDTO(currentBook.getId(), PublishContentTypeEnum.CHAPTER_CONTENT,
                            null, previousChapter);
                    previousResource.setProperty(property);
                    // 设置变更标识，删除的资源不需要考虑当前上架包列表
                    setResourceChangeFlag(previousResource, null);
                    if (previousResource.getChangeFlag() != 0) {
                        publishedResources.add(previousResource);
                    }
                }
            }
        }
    }

    /**
     * 处理章节中的试卷信息
     * @param bookId 教材ID
     * @param currentChapterPaperList 当前版本章节试卷列表
     * @param previousChapterPaperList 前一版本章节试卷列表
     * @return 试卷资源列表
     */
    private List<BookPublishedDetailResourceDTO> processPaperResources(String bookId,
                                                                       List<Paper> currentChapterPaperList,
                                                                       List<Paper> previousChapterPaperList,
                                                                       List<BookChangeItemEntity> currentPublishedItemList) {
        Map<String, Paper> currentChapterPaperMap = new HashMap<>();
        if (currentChapterPaperList != null) {
            currentChapterPaperMap = currentChapterPaperList.stream()
                    .filter(paper -> paper != null && paper.getPaperId() != null)
                    .collect(Collectors.toMap(Paper::getPaperId, paper -> paper));
        }
        Map<String, Paper> previousChapterPaperMap = new HashMap<>();
        if (previousChapterPaperList != null) {
            previousChapterPaperMap = previousChapterPaperList.stream()
                    .filter(paper -> paper != null && paper.getPaperId() != null)
                    .collect(Collectors.toMap(Paper::getPaperId, paper -> paper));
        }

        List<BookPublishedDetailResourceDTO> paperResourceList = new ArrayList<>();
        if (currentChapterPaperList != null) {
            for (Paper paper : currentChapterPaperList) {
                BookPublishedDetailResourceDTO currentResource = new BookPublishedDetailResourceDTO(bookId, PublishContentTypeEnum.PAPER,
                        paper, previousChapterPaperMap.get(paper.getPaperId()));
                // 设置变更标识，并考虑当前上架包列表
                List<Long> paperVersionIdList = currentPublishedItemList.stream()
                        .filter(item -> PublishContentTypeEnum.PAPER.match(item.getTypeCode()))
                        .map(BookChangeItemEntity::getVersionId).toList();
                setResourceChangeFlag(currentResource, paperVersionIdList);
                // 章节下试卷不过滤
//                if (currentResource.getChangeFlag() != 0) {
//                    paperResourceList.add(currentResource);
//                }
                paperResourceList.add(currentResource);
            }
        }
        if (previousChapterPaperList != null) {
            for (Paper paper : previousChapterPaperList) {
                if (!currentChapterPaperMap.containsKey(paper.getPaperId())) {
                    BookPublishedDetailResourceDTO previousResource = new BookPublishedDetailResourceDTO(bookId, PublishContentTypeEnum.PAPER,
                            null, paper);
                    // 设置变更标识，删除的资源不需要考虑当前上架包列表
                    setResourceChangeFlag(previousResource, null);
//                    if (previousResource.getChangeFlag() != 0) {
//                        paperResourceList.add(previousResource);
//                    }
                    paperResourceList.add(previousResource);
                }
            }
        }
        return paperResourceList;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public Integer getPublishedCount() {
        return publishedCount;
    }

    public void setPublishedCount(Integer publishedCount) {
        this.publishedCount = publishedCount;
    }

    public String getCurrentVersionNumber() {
        return currentVersionNumber;
    }

    public void setCurrentVersionNumber(String currentVersionNumber) {
        this.currentVersionNumber = currentVersionNumber;
    }

    public Long getCurrentVersionTime() {
        return currentVersionTime;
    }

    public void setCurrentVersionTime(Long currentVersionTime) {
        this.currentVersionTime = currentVersionTime;
    }

    public String getPreviousVersionNumber() {
        return previousVersionNumber;
    }

    public void setPreviousVersionNumber(String previousVersionNumber) {
        this.previousVersionNumber = previousVersionNumber;
    }

    public Long getPreviousVersionTime() {
        return previousVersionTime;
    }

    public void setPreviousVersionTime(Long previousVersionTime) {
        this.previousVersionTime = previousVersionTime;
    }

    public List<BookPublishedDetailResourceDTO> getPublishedResources() {
        return publishedResources;
    }

    public void setPublishedResources(List<BookPublishedDetailResourceDTO> publishedResources) {
        this.publishedResources = publishedResources;
    }

    public String getCurrentFormatVersionNumber() {
        return currentFormatVersionNumber;
    }

    public void setCurrentFormatVersionNumber(String currentFormatVersionNumber) {
        this.currentFormatVersionNumber = currentFormatVersionNumber;
    }

    public String getPreviousFormatVersionNumber() {
        return previousFormatVersionNumber;
    }

    public void setPreviousFormatVersionNumber(String previousFormatVersionNumber) {
        this.previousFormatVersionNumber = previousFormatVersionNumber;
    }
}
