package com.unipus.digitalbook.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "id信息")
public class IdListDTO<T extends Serializable> implements Serializable {

    private List<IdDTO<T>> ids;

    public IdListDTO(List<T> ids) {
        this.ids = ids.stream().map(IdDTO::new).toList();
    }

    @Schema(description = "id列表")
    public List<IdDTO<T>> getIds() {
        return ids;
    }

    public void setIds(List<IdDTO<T>> ids) {
        this.ids = ids;
    }

}
