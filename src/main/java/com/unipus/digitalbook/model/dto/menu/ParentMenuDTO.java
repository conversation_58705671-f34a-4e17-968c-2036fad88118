package com.unipus.digitalbook.model.dto.menu;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "父菜单模型")
public class ParentMenuDTO implements Serializable {
    @Schema(description = "菜单id", example = "1")
    private Long id;
    @Schema(description = "菜单名字", example = "一级菜单")
    private String name;

    public ParentMenuDTO(){}

    public ParentMenuDTO(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
