package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.dto.book.BookNodeVersionDTO;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "教材章节版本列表")
public class VersionListDTO implements Serializable {
    @Schema(description = "教材下节点的版本信息")
    private List<BookNodeVersionDTO> infoVersionList;

    @Schema(description = "章节简单信息（含版本号）列表")
    private List<ChapterVersionSimpleInfoDTO> chapterVersionList;

    public VersionListDTO() {
        super();
        chapterVersionList=new ArrayList<>(0);
    }


    public VersionListDTO(List<ChapterVersion> chapterEntityVersionList) {
        if (chapterEntityVersionList!=null){
            chapterVersionList=new ArrayList<>(chapterEntityVersionList.size());
            for (ChapterVersion chapterVersion : chapterEntityVersionList) {
                ChapterVersionSimpleInfoDTO chapterVersionSimpleInfoDTO = new ChapterVersionSimpleInfoDTO();
                chapterVersionSimpleInfoDTO.fromEntity(chapterVersion);
                chapterVersionList.add(chapterVersionSimpleInfoDTO);
            }
        }
    }

    public List<BookNodeVersionDTO> getInfoVersionList() {
        return infoVersionList;
    }

    public void setInfoVersionList(List<BookNodeVersionDTO> infoVersionList) {
        this.infoVersionList = infoVersionList;
    }

    public List<ChapterVersionSimpleInfoDTO> getChapterVersionList() {
        return chapterVersionList;
    }

    public void setChapterVersionList(List<ChapterVersionSimpleInfoDTO> chapterVersionList) {
        this.chapterVersionList = chapterVersionList;
    }
}
