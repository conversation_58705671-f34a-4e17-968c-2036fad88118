package com.unipus.digitalbook.model.dto;


import com.unipus.digitalbook.model.entity.AuthPermission;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

public class ControllerPermissionListDTO implements Serializable {
    private List<ControllerPermissionDTO> controllerResourceList;

    public ControllerPermissionListDTO() {
        super();
    }

    public ControllerPermissionListDTO(Map<String,List<AuthPermission>> map) {
        if (map==null || map.isEmpty()){
            controllerResourceList = new ArrayList<>(0);
            return;
        }
        controllerResourceList = new ArrayList<>(map.size());
        map.forEach((controller, resourceList) -> {
            if (resourceList != null && !resourceList.isEmpty()) {
                ControllerPermissionDTO controllerResourceDto = new ControllerPermissionDTO();
                controllerResourceDto.setController(resourceList.getFirst().getController());
                controllerResourceDto.setControllerDesc(resourceList.getFirst().getControllerDesc());
                List<AuthPermissionDTO> resourceDtoList = new ArrayList<>(resourceList.size());
                resourceList.forEach(authResource ->
                        resourceDtoList.add(new AuthPermissionDTO(authResource)));
                controllerResourceDto.setResourceList(resourceDtoList);
                controllerResourceList.add(controllerResourceDto);
            }
        });
    }


    public List<ControllerPermissionDTO> getControllerResourceList() {
        return controllerResourceList;
    }

    public void setControllerResourceList(List<ControllerPermissionDTO> controllerResourceList) {
        this.controllerResourceList = controllerResourceList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ControllerPermissionListDTO.class.getSimpleName() + "[", "]")
                .add("controllerResourceList=" + controllerResourceList)
                .toString();
    }
}
