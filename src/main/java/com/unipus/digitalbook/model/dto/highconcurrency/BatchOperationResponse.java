package com.unipus.digitalbook.model.dto.highconcurrency;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量操作响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BatchOperationResponse {
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 处理状态：SUCCESS, PARTIAL_SUCCESS, FAILED, PROCESSING
     */
    private String status;
    
    /**
     * 总记录数
     */
    private Integer totalCount;
    
    /**
     * 成功记录数
     */
    private Integer successCount;
    
    /**
     * 失败记录数
     */
    private Integer failedCount;
    
    /**
     * 处理开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 处理结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 处理耗时（毫秒）
     */
    private Long processingTime;
    
    /**
     * 错误详情
     */
    private List<ErrorDetail> errors;
    
    /**
     * 成功处理的数据ID列表
     */
    private List<Long> successIds;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 错误详情
     */
    @Data
    @Accessors(chain = true)
    public static class ErrorDetail {
        
        /**
         * 数据ID
         */
        private Long dataId;
        
        /**
         * 错误代码
         */
        private String errorCode;
        
        /**
         * 错误消息
         */
        private String errorMessage;
        
        /**
         * 错误发生时间
         */
        private LocalDateTime errorTime;
    }
}