package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.common.utils.ScoreUtil;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Schema(description = "试卷卷成绩基类DTO")
public abstract class ScoreBaseDTO implements Serializable {

    /**
     * 计算正确率
     * @param correctCount 正确题目数
     * @param totalCount 题目总数
     * @return 正确率
     */
    protected BigDecimal calculateCorrectRate(Integer correctCount, Integer totalCount) {
        if (totalCount == null || totalCount <= 0 || correctCount == null || correctCount <= 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(String.valueOf(correctCount))
                .multiply(new BigDecimal("100"))
                .divide(new BigDecimal(String.valueOf(totalCount)), 1, RoundingMode.HALF_UP);
    }

    /**
     * 计算得分率
     * @param userScore 用户得分
     * @param standardScore 试卷标准分
     * @return 正确率
     */
    protected BigDecimal calculateScoringRate(BigDecimal userScore, BigDecimal standardScore){
        BigDecimal score = ScoreUtil.keepOneDecimalForPercent(standardScore, userScore);
        return score==null?BigDecimal.ZERO:score;
    }
}
