package com.unipus.digitalbook.model.dto.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.SmallQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Schema(description = "题目信息")
public class QuestionDTO implements Serializable {
    @Schema(description = "题目ID")
    private String id;
    @Schema(description = "子题目ID")
    private String childId;
    @Schema(description = "题型")
    private String questionType;
    @Schema(description = "小题解析")
    private String analysis;
    @Schema(description = "题干")
    private String quesText;
    @Schema(description = "文本题干")
    private String quesTextString;
    @Schema(description = "是否计分")
    private Boolean isScoring;
    @Schema(description = "是否自动判题")
    private Boolean isJudgment;
    @Schema(description = "题目")
    private BigDecimal score;
    @Schema(description = "难度")
    private Integer difficulty;
    @Schema(description = "角色")
    private String role;
    @Schema(description = "媒体")
    private String media;
    @Schema(description = "音标")
    private String phoneticSymbol;
    @Schema(description = "答题间隔时间")
    private Integer answerTime;
    @Schema(description = "语音准备时间")
    private Integer prepareTime;
    @Schema(description = "答题字数限制")
    private Integer answerWordLimit;
    @Schema(description = "评测文本")
    private String evaluationText;
    @Schema(description = "关键字")
    private List<KeywordDTO> keywords;
    @Schema(description = "关联参数")
    private List<QuestionRelevancyDTO> relevancyList;

    @Schema(description = "选项")
    private List<QuestionOptionDTO> options;
    @Schema(description = "答案")
    private List<QuestionAnswerDTO> answers;
    @Schema(description = "子题")
    private List<QuestionDTO> children;
    @Schema(description = "标签")
    private List<List<QuestionTagDTO>> tagList;


    public QuestionDTO(){}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    public String getQuesText() {
        return quesText;
    }

    public void setQuesText(String quesText) {
        this.quesText = quesText;
    }

    public String getQuesTextString() {
        return quesTextString;
    }

    public void setQuesTextString(String quesTextString) {
        this.quesTextString = quesTextString;
    }

    public Boolean getIsScoring() {
        return isScoring;
    }

    public void setIsScoring(Boolean scoring) {
        isScoring = scoring;
    }

    public Boolean getIsJudgment() {
        return isJudgment;
    }

    public void setIsJudgment(Boolean judgment) {
        isJudgment = judgment;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public String getChildId() {
        return childId;
    }

    public void setChildId(String childId) {
        this.childId = childId;
    }

    public List<KeywordDTO> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<KeywordDTO> keywords) {
        this.keywords = keywords;
    }

    public List<QuestionDTO> getChildren() {
        return children;
    }

    public void setChildren(List<QuestionDTO> children) {
        this.children = children;
    }

    public List<QuestionRelevancyDTO> getRelevancyList() {
        return relevancyList;
    }

    public void setRelevancyList(List<QuestionRelevancyDTO> relevancyList) {
        this.relevancyList = relevancyList;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public List<QuestionOptionDTO> getOptions() {
        return options;
    }

    public void setOptions(List<QuestionOptionDTO> options) {
        this.options = options;
    }

    public List<QuestionAnswerDTO> getAnswers() {
        return answers;
    }

    public void setAnswers(List<QuestionAnswerDTO> answers) {
        this.answers = answers;
    }

    public String getPhoneticSymbol() {
        return phoneticSymbol;
    }

    public void setPhoneticSymbol(String phoneticSymbol) {
        this.phoneticSymbol = phoneticSymbol;
    }

    public Integer getAnswerTime() {
        return answerTime;
    }

    public void setAnswerTime(Integer answerTime) {
        this.answerTime = answerTime;
    }

    public Integer getPrepareTime() {
        return prepareTime;
    }

    public void setPrepareTime(Integer prepareTime) {
        this.prepareTime = prepareTime;
    }

    public Integer getAnswerWordLimit() {
        return answerWordLimit;
    }

    public void setAnswerWordLimit(Integer answerWordLimit) {
        this.answerWordLimit = answerWordLimit;
    }

    public List<List<QuestionTagDTO>> getTagList() {
        return tagList;
    }

    public void setTagList(List<List<QuestionTagDTO>> tagList) {
        this.tagList = tagList;
    }

    public String getEvaluationText() {
        return evaluationText;
    }

    public void setEvaluationText(String evaluationText) {
        this.evaluationText = evaluationText;
    }

    public QuestionDTO(Question question, boolean isReturnAnswer) {
        setIdentifier(question);
        setBasicProperties(question);
        setQuestionTextProperties(question.getQuestionText());
        setQuestionOptions(question);
        if (isReturnAnswer || QuestionTypeEnum.isReturnAnswer(questionType)) {
            setAnswerProperties(question);
        }
        fillTagPaths(question);
        setChildQuestions(question, isReturnAnswer);
    }
    private void setIdentifier(Question question) {
        if (question instanceof SmallQuestion) {
            this.id = question.getBizQuestionId();
        } else {
            this.childId = question.getBizQuestionId();
        }
    }

    private void setBasicProperties(Question question) {
        this.difficulty = question.getDifficulty();
        this.isScoring = question.getIsScoring();
        this.isJudgment = question.getIsJudgment();
        this.score = question.getScore();
        this.questionType = QuestionTypeEnum.getNameByCode(question.getQuestionType());
    }

    private void setQuestionTextProperties(QuestionText questionText) {
        if (questionText == null) {
            return;
        }

        this.quesText = questionText.getText();
        this.quesTextString = questionText.getPlainText();

        setKeywordsWithEntity(questionText.getKeywords());
        setRelevancy(questionText.getRelevancy());

        this.media = questionText.getMedia();
        this.role = questionText.getRole();
        this.phoneticSymbol = questionText.getPhoneticSymbol();

        this.answerTime = questionText.getAnswerTime();
        this.prepareTime = questionText.getPrepareTime();
        this.answerWordLimit = questionText.getAnswerWordLimit();
        this.evaluationText = questionText.getEvaluationText();
        if (questionText.getOptions() != null) {
            this.options = questionText.getOptions().stream()
                    .map(QuestionOptionDTO::new)
                    .toList();
        }
    }

    private void setKeywordsWithEntity(List<QuestionText.Keyword> keywords) {
        if (keywords != null) {
            this.keywords = keywords.stream()
                    .map(KeywordDTO::new)
                    .toList();
        }
    }

    private void setRelevancy(List<QuestionText.Relevancy> relevancy) {
        if (relevancy != null) {
            this.relevancyList = relevancy.stream()
                    .map(QuestionRelevancyDTO::new)
                    .toList();
        }
    }

    private void setQuestionOptions(Question question) {
        if (question.getOptions() != null) {
            this.options = question.getOptions().stream()
                    .map(QuestionOptionDTO::new)
                    .toList();
        }
    }

    private void fillTagPaths(Question question) {
        if (question.getTags() != null) {
            this.tagList = QuestionTagDTO.toDTOList(question.getTags());
        }
    }

    private void setAnswerProperties(Question question) {
        if (question.getAnswers() != null) {
            this.answers = question.getAnswers().stream()
                    .map(QuestionAnswerDTO::new)
                    .toList();
        }
        this.analysis = question.getAnalysis();
    }

    private void setChildQuestions(Question question, boolean isReturnAnswer) {
        if (question.getQuestions() != null) {
            this.children = question.getQuestions().stream()
                    .map(q -> new QuestionDTO(q, isReturnAnswer))
                    .toList();
        }
    }

}
