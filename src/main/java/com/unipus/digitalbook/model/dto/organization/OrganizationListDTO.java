package com.unipus.digitalbook.model.dto.organization;

import com.unipus.digitalbook.model.entity.Organization;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

@Schema(description = "组织列表返回类型")
public class OrganizationListDTO implements Serializable {

    @Schema(description = "组织列表")
    private List<OrganizationListItemDTO> organizationList;

    public OrganizationListDTO() {}

    public OrganizationListDTO(List<Organization> organizationList) {
        this.organizationList = organizationList.stream().map(OrganizationListDTO::convertToTree).toList();
    }

    public List<OrganizationListItemDTO> getOrganizationList() {
        return organizationList;
    }

    public OrganizationListDTO setOrganizationList(List<OrganizationListItemDTO> organizationList) {
        this.organizationList = organizationList;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrganizationListDTO.class.getSimpleName() + "[", "]")
                .add("organizationList=" + organizationList)
                .toString();
    }

    // Entity转换DTO（单层列表，不包含树形结构）
    public static OrganizationListDTO fromEntity(List<Organization> entityList){
        OrganizationListDTO dto = new OrganizationListDTO();
        if(CollectionUtils.isEmpty(entityList)){
            return null;
        }
        List<OrganizationListItemDTO> items = new ArrayList<>();
        for (Organization entity : entityList) {
            OrganizationListItemDTO target = new OrganizationListItemDTO();
            target.setId(entity.getId());
            target.setName(entity.getOrgName());
            target.setOrgType(entity.getOrgType());
            target.setParentId(entity.getParentId());
            target.setParentName(entity.getParentName());
            target.setStatus(entity.getStatus());
            target.setLevel(entity.getLevel());
            items.add(target);
        }
        entityList.forEach(entity -> items.add(convertToTree(entity)));
        dto.setOrganizationList(items);
        return dto;
    }

    // Entity转换DTO-TREE
    public static OrganizationListItemDTO convertToTree(Organization entity) {
        if (entity == null) {
            return null;
        }

        OrganizationListItemDTO target = new OrganizationListItemDTO();
        // 根据映射设置属性值
        target.setId(entity.getId());
        target.setName(entity.getOrgName());
        target.setOrgType(entity.getOrgType());
        target.setParentId(entity.getParentId());
        target.setParentName(entity.getParentName());
        target.setStatus(entity.getStatus());
        target.setLevel(entity.getLevel());

        // 递归转换子节点
        List<Organization> subOrgEntityList = entity.getSubOrgList();
        if (!CollectionUtils.isEmpty(subOrgEntityList)) {
            target.setSubOrganizationList(subOrgEntityList.stream().map(OrganizationListDTO::convertToTree).toList());
        }
        return target;
    }
}
