package com.unipus.digitalbook.model.dto.organization;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

@Schema(description = "组织对象")
public class OrganizationListItemDTO implements Serializable {
    @Schema(description = "组织ID", example = "1234567890")
    private Long id;

    @Schema(description = "组织名称", example = "Example Organization")
    private String name;

    @Schema(description = "组织类型", example = "1")
    private Integer orgType;

    @Schema(description = "上级组织名称", example = "Parent Organization")
    private String parentName;

    @Schema(description = "上级组织ID", example = "1234567890")
    private Long parentId;

    @Schema(description = "组织状态", example = "1")
    private Integer status;

    @Schema(description = "组织层级:1~5", example = "1")
    private Integer level;

    @Schema(description = "子组织列表")
    private List<OrganizationListItemDTO> subOrganizationList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public OrganizationListItemDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public OrganizationListItemDTO setOrgType(Integer orgType) {
        this.orgType = orgType;
        return this;
    }

    public String getParentName() {
        return parentName;
    }

    public OrganizationListItemDTO setParentName(String parentName) {
        this.parentName = parentName;
        return this;
    }

    public Long getParentId() {
        return parentId;
    }

    public OrganizationListItemDTO setParentId(Long parentId) {
        this.parentId = parentId;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public OrganizationListItemDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getLevel() {
        return level;
    }

    public OrganizationListItemDTO setLevel(Integer level) {
        this.level = level;
        return this;
    }

    public List<OrganizationListItemDTO> getSubOrganizationList() {
        return subOrganizationList;
    }

    public OrganizationListItemDTO setSubOrganizationList(List<OrganizationListItemDTO> subOrganizationList) {
        this.subOrganizationList = subOrganizationList;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrganizationListItemDTO.class.getSimpleName() + "[", "]")
                .add("name='" + name + "'")
                .add("orgType=" + orgType)
                .add("status=" + status)
                .add("parentName=" + parentName)
                .add("parentId=" + parentId)
                .add("subOrganizationList=" + subOrganizationList)
                .add("level=" + level)
                .toString();
    }

}

