package com.unipus.digitalbook.model.dto.publish;

import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.enums.PublishContentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Schema(description = "教材发布检测数据返回对象")
public class BookPublishCheckDTO implements Serializable {
    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "管理者")
    private String manager;

    @Schema(description = "教材名称")
    private String bookName;

    @Schema(description = "教材系列")
    private String bookSeries;

    @Schema(description = "PC端封面图片地址")
    private String pcCoverUrl;

    @Schema(description = "APP横版封面图片地址")
    private String appHorizontalCoverUrl;

    @Schema(description = "APP竖版封面图片地址")
    private String appVerticalCoverUrl;

    @Schema(description = "发布次数")
    private Integer publishCount;

    @Schema(description = "最后提交时间")
    private Long lastSubmitTime;

    @Schema(description = "最后提交版本号")
    private String lastSubmitVersionNumber;

    @Schema(description = "最后提交格式版本号")
    private String lastSubmitFormatVersionNumber;

    @Schema(description = "资源发布列表")
    private List<PublishCheckResourceDTO> publishResources;

    public BookPublishCheckDTO(Book currentBook, Book publishedBook, String manager, Integer publishCount) {
        // 设置基本属性
        this.bookId = currentBook.getId();
        this.manager = manager;
        this.bookName = currentBook.getChineseName();
        this.bookSeries = currentBook.getSeries() != null ? currentBook.getSeries().getName() : null;
        this.pcCoverUrl = currentBook.getPcCoverUrl();
        this.appHorizontalCoverUrl = currentBook.getAppHorizontalCoverUrl();
        this.appVerticalCoverUrl = currentBook.getAppVerticalCoverUrl();
        this.publishCount = publishCount != null ? publishCount : 0;
        // 设置已发布版本信息
        boolean publishedFlag = publishedBook != null;
        if (publishedFlag && publishedBook.getBookVersion() != null) {
            BookVersion publishedBookVersion = publishedBook.getBookVersion();
            this.lastSubmitTime = publishedBookVersion.getCreateTime() != null ?
                    publishedBookVersion.getCreateTime().getTime() : null;
            this.lastSubmitVersionNumber = publishedBookVersion.getVersionNum();
            this.lastSubmitFormatVersionNumber = publishedBookVersion.getShowVersionNumber();
        }
        // 构建资源列表
        List<PublishCheckResourceDTO> tempPublishResources = new ArrayList<>();
        // 处理基础资源信息（基本信息、教材简介、版权信息）
        processMetadataResources(tempPublishResources, currentBook, publishedBook, publishedFlag);
        // 处理配套资源
        processComplementResources(tempPublishResources, currentBook, publishedBook, publishedFlag);
        // 处理章节信息
        processChapterResources(tempPublishResources, currentBook, publishedBook, publishedFlag);

        this.publishResources = tempPublishResources;
    }
    
    /**
     * 处理基础资源信息（基本信息、教材简介、版权信息）
     */
    private void processMetadataResources(List<PublishCheckResourceDTO> publishResources,
                                          Book currentBook, Book publishedBook, boolean publishedFlag) {
        // 添加基本信息资源
        String basicInfoVersion = publishedFlag && publishedBook.getBookBasic() != null ?
                publishedBook.getBookBasic().getVersionNumber() : null;
        publishResources.add(new PublishCheckResourceDTO(currentBook.getId(),
                PublishContentTypeEnum.BASIC_INFO,
                currentBook.getBookBasic(),
                basicInfoVersion));

        // 添加简介资源
        String introVersion = publishedFlag && publishedBook.getBookIntro() != null ?
                publishedBook.getBookIntro().getVersionNumber() : null;
        publishResources.add(new PublishCheckResourceDTO(currentBook.getId(),
                PublishContentTypeEnum.BOOK_INTRO,
                currentBook.getBookIntro(),
                introVersion));

        // 添加版权信息资源
        String copyrightVersion = publishedFlag && publishedBook.getBookCopyright() != null ?
                publishedBook.getBookCopyright().getVersionNumber() : null;
        publishResources.add(new PublishCheckResourceDTO(currentBook.getId(),
                PublishContentTypeEnum.COPYRIGHT_INFO,
                currentBook.getBookCopyright(),
                copyrightVersion));
    }

    /**
     * 处理配套资源信息
     */
    private void processComplementResources(List<PublishCheckResourceDTO> publishResources,
                                            Book currentBook, Book publishedBook, boolean publishedFlag) {
        Map<String, ComplementResource> publishedComplementResourceMap = new HashMap<>();
        if (publishedFlag && publishedBook.getComplementResourceList() != null
                && !CollectionUtils.isEmpty(publishedBook.getComplementResourceList().getComplementResourceList())) {
            publishedComplementResourceMap = publishedBook.getComplementResourceList().getComplementResourceList().stream()
                    .collect(Collectors.toMap(ComplementResource::getResourceId, resource -> resource));
        }
        if (currentBook.getComplementResourceList() != null
                && !CollectionUtils.isEmpty(currentBook.getComplementResourceList().getComplementResourceList())) {
            for (ComplementResource resource : currentBook.getComplementResourceList().getComplementResourceList()) {
                ComplementResource complementResource = publishedComplementResourceMap.get(resource.getResourceId());
                String complementVersion = complementResource != null ? complementResource.getVersionNumber() : null;
                publishResources.add(new PublishCheckResourceDTO(currentBook.getId(),
                        PublishContentTypeEnum.COMPLEMENT_RESOURCE,
                        resource,
                        complementVersion));
            }
        } else {
            publishResources.add(new PublishCheckResourceDTO(currentBook.getId(),
                    PublishContentTypeEnum.COMPLEMENT_RESOURCE,
                    null,
                    null));
        }
    }

    /**
     * 处理章节资源信息
     */
    private void processChapterResources(List<PublishCheckResourceDTO> publishResources,
                                         Book currentBook, Book publishedBook, boolean publishedFlag) {
        if (currentBook.getChapterList() == null) {
            return;
        }
        // 构建已发布章节映射
        Map<String, Chapter> publishedChapterMap = new HashMap<>();
        if (publishedFlag && publishedBook.getChapterList() != null) {
            publishedChapterMap = publishedBook.getChapterList().stream()
                    .filter(chapter -> chapter != null && chapter.getId() != null)
                    .collect(Collectors.toMap(Chapter::getId, chapter -> chapter));
        }
        // 添加章节资源
        for (Chapter currentChapter : currentBook.getChapterList()) {
            if (currentChapter == null || currentChapter.getId() == null) {
                continue;
            }

            Chapter publishedChapter = publishedChapterMap.get(currentChapter.getId());
            String chapterVersion = null;
            if (publishedChapter != null && publishedChapter.getChapterVersion() != null) {
                chapterVersion = publishedChapter.getChapterVersion().getVersionNumber();
            }

            // 判断章节版本是否更新
            boolean chapterVersionUpdated = false;
            if (currentChapter.getChapterVersion() != null && chapterVersion != null) {
                chapterVersionUpdated = !currentChapter.getChapterVersion().getVersionNumber().equals(chapterVersion);
            }

            // 创建章节资源DTO
            Map<String, Object> properties = new HashMap<>();
            properties.put("chapterNumber", currentChapter.getChapterNumber());
            properties.put("chapterVersionUpdated", chapterVersionUpdated);

            PublishCheckResourceDTO resource = new PublishCheckResourceDTO(currentBook.getId(),
                    PublishContentTypeEnum.CHAPTER_CONTENT,
                    currentChapter,
                    chapterVersion);
            resource.setProperty(properties);
            publishResources.add(resource);
        }
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public String getBookSeries() {
        return bookSeries;
    }

    public void setBookSeries(String bookSeries) {
        this.bookSeries = bookSeries;
    }

    public String getPcCoverUrl() {
        return pcCoverUrl;
    }

    public void setPcCoverUrl(String pcCoverUrl) {
        this.pcCoverUrl = pcCoverUrl;
    }

    public String getAppHorizontalCoverUrl() {
        return appHorizontalCoverUrl;
    }

    public void setAppHorizontalCoverUrl(String appHorizontalCoverUrl) {
        this.appHorizontalCoverUrl = appHorizontalCoverUrl;
    }

    public String getAppVerticalCoverUrl() {
        return appVerticalCoverUrl;
    }

    public void setAppVerticalCoverUrl(String appVerticalCoverUrl) {
        this.appVerticalCoverUrl = appVerticalCoverUrl;
    }

    public Integer getPublishCount() {
        return publishCount;
    }

    public void setPublishCount(Integer publishCount) {
        this.publishCount = publishCount;
    }

    public Long getLastSubmitTime() {
        return lastSubmitTime;
    }

    public void setLastSubmitTime(Long lastSubmitTime) {
        this.lastSubmitTime = lastSubmitTime;
    }

    public String getLastSubmitVersionNumber() {
        return lastSubmitVersionNumber;
    }

    public void setLastSubmitVersionNumber(String lastSubmitVersionNumber) {
        this.lastSubmitVersionNumber = lastSubmitVersionNumber;
    }

    public String getLastSubmitFormatVersionNumber() {
        return lastSubmitFormatVersionNumber;
    }

    public void setLastSubmitFormatVersionNumber(String lastSubmitFormatVersionNumber) {
        this.lastSubmitFormatVersionNumber = lastSubmitFormatVersionNumber;
    }

    public List<PublishCheckResourceDTO> getPublishResources() {
        return publishResources;
    }

    public void setPublishResources(List<PublishCheckResourceDTO> publishResources) {
        this.publishResources = publishResources;
    }
}
