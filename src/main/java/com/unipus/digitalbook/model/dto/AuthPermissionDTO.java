package com.unipus.digitalbook.model.dto;

import com.unipus.digitalbook.model.entity.AuthPermission;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

public class AuthPermissionDTO implements Serializable {

    @Schema(description = "方法名")
    String methodName;
    @Schema(description = "资源说明")
    String apiDesc;
    @Schema(description = "资源名")
    String apiSummary;
    @Schema(description = "资源地址")
    String apiUrl;

    @Schema(description = "请求方式",examples = {"POST","GET","DELETE","PUT"})
    String requestType;
    @Schema(description = "是否已分配")
    Boolean assigned = Boolean.FALSE;

    @Schema(description = "分配时间")
    Long assignedTime;


    public AuthPermissionDTO() {
        super();
    }


    public AuthPermissionDTO(AuthPermission resource) {
        this.methodName = resource.getMethodName();
        this.apiDesc = resource.getApiDesc();
        this.apiSummary = resource.getApiSummary();
        this.apiUrl = resource.getApiUrl();
        this.requestType = resource.getRequestType();
    }


    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getApiDesc() {
        return apiDesc;
    }

    public void setApiDesc(String apiDesc) {
        this.apiDesc = apiDesc;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getApiSummary() {
        return apiSummary;
    }

    public void setApiSummary(String apiSummary) {
        this.apiSummary = apiSummary;
    }

    public Boolean getAssigned() {
        return assigned;
    }

    public void setAssigned(Boolean assigned) {
        this.assigned = assigned;
    }

    public Long getAssignedTime() {
        return assignedTime;
    }

    public void setAssignedTime(Long assignedTime) {
        this.assignedTime = assignedTime;
    }
}
