package com.unipus.digitalbook.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "id信息")
public class IdDTO<T extends Serializable> implements Serializable {
    private T id;

    public IdDTO(){}

    public IdDTO(T id) {
        this.id = id;
    }
    public T getId() {
        return id;
    }

    public void setId(T id) {
        this.id = id;
    }
}
