package com.unipus.digitalbook.model.dto.complement;

import com.unipus.digitalbook.model.entity.complement.ComplementResourceReference;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Schema(description = "配套资源引用查询结果列表实体")
public class ComplementResourceReferenceListDTO implements Serializable {

    @Schema(description = "配套资源引用列表")
    private List<ComplementResourceReferenceDTO> complementResourceReferenceDTOList;

    public ComplementResourceReferenceListDTO(){
        this.complementResourceReferenceDTOList = Collections.emptyList();
    }

    public ComplementResourceReferenceListDTO(List<ComplementResourceReference> complementResourceReferences) {
        if(CollectionUtils.isEmpty(complementResourceReferences)){
            this.complementResourceReferenceDTOList = Collections.emptyList();
        }else {
            this.complementResourceReferenceDTOList = complementResourceReferences.stream().map(ComplementResourceReferenceDTO::new).toList();
        }
    }

    public List<ComplementResourceReferenceDTO> getComplementResourceReferenceDTOList() {
        return complementResourceReferenceDTOList;
    }

    public void setComplementResourceReferenceDTOList(List<ComplementResourceReferenceDTO> complementResourceReferenceDTOList) {
        this.complementResourceReferenceDTOList = complementResourceReferenceDTOList;
    }

}