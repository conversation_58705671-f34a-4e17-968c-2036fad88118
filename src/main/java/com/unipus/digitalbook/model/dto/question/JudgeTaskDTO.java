package com.unipus.digitalbook.model.dto.question;

import com.unipus.digitalbook.model.entity.question.JudgeTaskTicket;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "判题任务")
public class JudgeTaskDTO implements Serializable {
    @Schema(description = "答案id")
    private String bizAnswerId;

    @Schema(description = "子题id")
    private String childId;

    public JudgeTaskDTO() {

    }

    public JudgeTaskDTO(JudgeTaskTicket taskTicket) {
        this.bizAnswerId = taskTicket.getBizAnswerId();
        UserAnswer userAnswer = taskTicket.getUserAnswer();
        this.childId = userAnswer.getBizQuestionId();
    }

    public String getBizAnswerId() {
        return bizAnswerId;
    }

    public void setBizAnswerId(String bizAnswerId) {
        this.bizAnswerId = bizAnswerId;
    }

    public String getChildId() {
        return childId;
    }

    public void setChildId(String childId) {
        this.childId = childId;
    }
}
