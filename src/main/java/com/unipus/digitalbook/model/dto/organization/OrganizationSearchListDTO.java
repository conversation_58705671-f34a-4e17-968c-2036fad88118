package com.unipus.digitalbook.model.dto.organization;

import com.unipus.digitalbook.model.entity.Organization;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

@Schema(description = "组织列表返回类型")
public class OrganizationSearchListDTO implements Serializable {

    @Schema(description = "组织列表")
    private List<OrganizationSearchListItemDTO> organizationList;

    @Schema(description = "总记录数")
    private Integer total;

    public OrganizationSearchListDTO(List<Organization> organizationList, Integer total) {
        this.organizationList = buildOrganizationList(organizationList);
        this.total = total;
    }

    public List<OrganizationSearchListItemDTO> getOrganizationList() {
        return organizationList;
    }

    public OrganizationSearchListDTO setOrganizationList(List<OrganizationSearchListItemDTO> organizationList) {
        this.organizationList = organizationList;
        return this;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrganizationSearchListDTO.class.getSimpleName() + "[", "]")
                .add("organizationList=" + organizationList)
                .toString();
    }

    // Entity转换DTO-LIST
    public static List<OrganizationSearchListItemDTO> buildOrganizationList(List<Organization> organizationList) {
        if (CollectionUtils.isEmpty(organizationList)){
            return List.of();
        }

        List<OrganizationSearchListItemDTO> targetList = new ArrayList<>();
        organizationList.forEach(entity -> {
            OrganizationSearchListItemDTO target = new OrganizationSearchListItemDTO();
            // 根据映射设置属性值
            target.setId(entity.getId());
            target.setName(entity.getOrgName());
            target.setOrgType(entity.getOrgType());
            target.setParentId(entity.getParentId());
            target.setStatus(entity.getStatus());
            target.setCreateTime(entity.getCreateTime());
            target.setUpdateTime(entity.getUpdateTime());
            target.setParentPath(entity.getParentPath());
            target.setLevel(entity.getLevel());
            targetList.add(target);
        });

        return targetList;
    }

}
