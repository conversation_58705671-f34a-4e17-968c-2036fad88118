package com.unipus.digitalbook.model.dto.organization;

import com.unipus.digitalbook.model.entity.OrgInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "组织信息返回对象")
public class OrgInfoDTO implements Serializable {

    @Schema(description = "组织ID")
    private Long id;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "组织类型")
    private Integer orgType;

    @Schema(description = "父组织ID")
    private Long parentId;

    public OrgInfoDTO(OrgInfo orgInfo) {
        this.id = orgInfo.getId();
        this.orgName = orgInfo.getOrgName();
        this.orgType = orgInfo.getOrgType();
        this.parentId = orgInfo.getParentId();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

}
