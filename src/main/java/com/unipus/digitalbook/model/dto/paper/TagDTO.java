package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.entity.tag.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "标签DTO")
public class TagDTO implements Serializable {
    @Schema(description = "标签ID")
    private Long tagId;
    @Schema(description = "上级ID")
    private Long parentId;
    @Schema(description = "标签名称")
    private String tagName;
    @Schema(description = "标签类型")
    private Integer tagType;

    public TagDTO(Tag tag) {
        this.tagId = tag.getTagId();
        this.tagName = tag.getTagName();
        this.tagType = tag.getTagType();
        this.parentId = tag.getParentId();
    }

    public Long getTagId() {
        return tagId;
    }

    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
}
