package com.unipus.digitalbook.model.dto.highconcurrency;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量操作请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class BatchOperationRequest {
    
    /**
     * 操作类型：INSERT, UPDATE, DELETE
     */
    @NotNull(message = "操作类型不能为空")
    private String operationType;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 操作数据列表
     */
    @Valid
    @NotEmpty(message = "操作数据不能为空")
    @Size(max = 1000, message = "单次操作数据不能超过1000条")
    private List<BatchDataItem> dataItems;
    
    /**
     * 是否异步处理
     */
    private Boolean async = false;
    
    /**
     * 操作优先级
     */
    private Integer priority = 1;
    
    /**
     * 批量数据项
     */
    @Data
    public static class BatchDataItem {
        
        /**
         * 数据ID（更新和删除时必需）
         */
        private Long id;
        
        /**
         * 业务数据
         */
        private String businessData;
        
        /**
         * 扩展数据
         */
        private String extraData;
        
        /**
         * 版本号（乐观锁，更新时使用）
         */
        private Integer version;
    }
}