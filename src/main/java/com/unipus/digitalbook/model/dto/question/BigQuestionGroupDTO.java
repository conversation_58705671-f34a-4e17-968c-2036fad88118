package com.unipus.digitalbook.model.dto.question;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Schema(description = "大题组信息")
public class BigQuestionGroupDTO implements Serializable {

    @Schema(description = "题组ID")
    private String groupId;
    @Schema(description = "题组版本")
    private String versionNumber;
    @Schema(description = "题组类型", example = "single_choice_group")
    private String groupType;
    @Schema(description = "题组说明信息")
    private String direction;
    @Schema(description = "题组内容列表")
    private String material;
    @Schema(description = "难度星级 1-5")
    private Integer groupDifficulty;
    @Schema(description = "题目列表")
    private List<QuestionDTO> list;
    @Schema(description = "题组答案解析")
    private String analysis;
    @Schema(description = "题组设置信息，如答题方式、计分等")
    private QuestionSettingDTO groupSetting;

    @Schema(description = "题组总分")
    private BigDecimal score;

    public BigQuestionGroupDTO(){}

    public BigQuestionGroupDTO(BigQuestionGroup bigQuestionGroup, boolean isReturnAnswer) {
        this.groupId = bigQuestionGroup.getBizGroupId();
        this.groupType = QuestionGroupTypeEnum.getNameByCode(bigQuestionGroup.getType());
        this.direction = bigQuestionGroup.getDirection();
        this.material = bigQuestionGroup.getContent();
        this.groupDifficulty = bigQuestionGroup.getDifficulty();
        this.groupSetting = bigQuestionGroup.getSetting()==null ? null : new QuestionSettingDTO(bigQuestionGroup.getSetting());
        this.versionNumber = bigQuestionGroup.getVersionNumber();
        this.score = bigQuestionGroup.getScore();
        if (isReturnAnswer) {
            this.analysis = bigQuestionGroup.getAnalysis();
        }
        if (bigQuestionGroup.getQuestions() != null) {
            this.list = bigQuestionGroup.getQuestions().stream().map(q -> new QuestionDTO(q, isReturnAnswer)).toList();
        }
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public Integer getGroupDifficulty() {
        return groupDifficulty;
    }

    public void setGroupDifficulty(Integer groupDifficulty) {
        this.groupDifficulty = groupDifficulty;
    }

    public List<QuestionDTO> getList() {
        return list;
    }

    public void setList(List<QuestionDTO> list) {
        this.list = list;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    public QuestionSettingDTO getGroupSetting() {
        return groupSetting;
    }

    public void setGroupSetting(QuestionSettingDTO groupSetting) {
        this.groupSetting = groupSetting;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }
}
