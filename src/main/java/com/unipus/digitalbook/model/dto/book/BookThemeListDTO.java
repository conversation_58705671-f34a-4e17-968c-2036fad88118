package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.BookTheme;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * 教材主题对象
 */
@Schema(description = "教材主题对象")
public class BookThemeListDTO implements Serializable {

    @Schema(description = "主题列表")
    private List<BookThemeDTO> bookThemeDTOList;

    public BookThemeListDTO(List<BookTheme> themeList) {
        this.bookThemeDTOList = themeList.stream().map(BookThemeDTO::build).toList();
    }

    public List<BookThemeDTO> getBookThemeDTOList() {
        return bookThemeDTOList;
    }

    public void setBookThemeDTOList(List<BookThemeDTO> bookThemeDTOList) {
        this.bookThemeDTOList = bookThemeDTOList;
    }
}
