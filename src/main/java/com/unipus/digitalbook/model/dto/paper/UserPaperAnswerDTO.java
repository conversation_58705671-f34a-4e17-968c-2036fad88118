package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.dto.question.AnswerDTO;
import com.unipus.digitalbook.model.dto.question.UserAnswerResultDTO;
import com.unipus.digitalbook.model.entity.paper.UserPaperAnswer;
import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户试卷作答记录DTO
 */
@Schema(description = "用户试卷作答记录DTO")
public class UserPaperAnswerDTO implements Serializable {

    @Schema(description = "试卷实例ID")
    private String instanceId;

    @Schema(description = "试卷成绩批次ID")
    private String scoreBatchId;

    @Schema(description = "试卷总分")
    private BigDecimal totalScore;

    @Schema(description = "提交状态")
    private Integer submitStatus;

    @Schema(description = "正确答案（以大题ID为KEY）")
    private List<AnswerDTO> correctAnswers;

    @Schema(description = "用户答案（以大题ID为KEY）")
    private Map<String, List<UserAnswerResultDTO>> userAnswersMap;

    private UserPaperAnswerDTO(UserPaperAnswer userPaperAnswer){
        this.instanceId = userPaperAnswer.getInstanceId();
        this.scoreBatchId = userPaperAnswer.getScoreBatchId();
        this.totalScore = userPaperAnswer.getTotalScore();
        this.submitStatus = userPaperAnswer.getSubmitStatus();
        this.correctAnswers = buildCorrectAnswerList(userPaperAnswer.getCorrectAnswersMap());
        this.userAnswersMap = buildUserAnswersMap(userPaperAnswer.getUserAnswersMap());
    }

    public static UserPaperAnswerDTO build(UserPaperAnswer userPaperAnswer){
        if(userPaperAnswer == null){
            return null;
        }
        return new UserPaperAnswerDTO(userPaperAnswer);
    }

    // 构建正确答案
    private List<AnswerDTO> buildCorrectAnswerList(Map<String, List<QuestionAnswer>> questionAnswersMap) {
        if (CollectionUtils.isEmpty(questionAnswersMap)) {
            return List.of();
        }
        return questionAnswersMap.entrySet().stream()
                .map(ent -> new AnswerDTO(ent.getKey(), ent.getValue()))
                .toList();
    }

    // 构建用户答案
    private Map<String, List<UserAnswerResultDTO>> buildUserAnswersMap(Map<String, List<UserAnswer>> userAnswersMap) {
        if (CollectionUtils.isEmpty(userAnswersMap)) {
            return Map.of();
        }
        return userAnswersMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                entry -> entry.getValue().stream().map(UserAnswerResultDTO::new).toList()));

    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getScoreBatchId() {
        return scoreBatchId;
    }

    public void setScoreBatchId(String scoreBatchId) {
        this.scoreBatchId = scoreBatchId;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getSubmitStatus() {
        return submitStatus;
    }

    public void setSubmitStatus(Integer submitStatus) {
        this.submitStatus = submitStatus;
    }

    public List<AnswerDTO> getCorrectAnswers() {
        return correctAnswers;
    }

    public void setCorrectAnswers(List<AnswerDTO> correctAnswers) {
        this.correctAnswers = correctAnswers;
    }

    public Map<String, List<UserAnswerResultDTO>> getUserAnswersMap() {
        return userAnswersMap;
    }

    public void setUserAnswersMap(Map<String, List<UserAnswerResultDTO>> userAnswersMap) {
        this.userAnswersMap = userAnswersMap;
    }
}
