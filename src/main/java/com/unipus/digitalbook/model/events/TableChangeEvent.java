package com.unipus.digitalbook.model.events;

import org.springframework.context.ApplicationEvent;

import java.io.Serializable;
import java.time.Instant;
import java.util.Date;

/**
 * 组织相关表变更事件实体
 */
public class TableChangeEvent extends ApplicationEvent implements Serializable {

    private String tableName;

    private String operationType;

    private Object param;

    private String sql;

    private Date changeTime;

    private String operator;

    public TableChangeEvent(Object source, String tableName, String operationType, Object param, String sql, String operator) {
        super(source);
        this.tableName = tableName;
        this.operationType = operationType;
        this.param = param;
        this.sql = sql;
        this.changeTime = Date.from(Instant.now());
        this.operator = operator;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public Object getParam() {
        return param;
    }

    public void setParam(Object param) {
        this.param = param;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
