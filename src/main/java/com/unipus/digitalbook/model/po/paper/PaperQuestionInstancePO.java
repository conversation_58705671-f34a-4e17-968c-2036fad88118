package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.UserPaperScore;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 试卷题目实例
 * Table Name: paper_question_instance
 */
public class PaperQuestionInstancePO {
    // 题目实例ID
    private Long id;
    // 大题ID
    private Long questionGroupId;
    // 大题业务ID
    private String questionBizGroupId;
    // 小题ID
    private Long questionId;
    // 小题业务ID
    private String questionBizId;
    // 试卷实例ID/作答轮次ID
    private String roundId;
    // 本大题得分
    private BigDecimal userScore;
    // 本大题标准分
    private BigDecimal standardScore;
    // 小题是否计分
    private Boolean isScored;
    // 小题是否判题
    private Boolean isJudged;
    // 正确率
    private Boolean isCorrect;
    // 创建时间
    private Date createTime;
    // 最后更新时间
    private Date updateTime;
    // 创建者ID
    private String createBy;
    // 最后更新者ID
    private String updateBy;
    // 是否有效 0-无效 1-有效
    private Boolean enable;
    // 大题类型
    private Integer questionGroupType;

    public PaperQuestionInstancePO() {}

    public PaperQuestionInstancePO(BigQuestionGroup group, Question small, String instanceId,
                                   UserPaperScore.UserQuestionScoreRecord info, String openId) {
        this.questionGroupId = group.getId();
        this.questionBizGroupId = group.getBizGroupId();
        this.questionId = small.getId();
        this.questionBizId = small.getBizQuestionId();
        this.roundId = instanceId;
        this.userScore = info.score();
        this.standardScore = small.getScore();
        this.isScored = info.isScored();
        this.isJudged = info.isJudged();
        this.isCorrect = info.isCorrect();
        this.createBy = openId;
        this.updateBy = openId;
        this.enable = true;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuestionGroupId() {
        return questionGroupId;
    }

    public void setQuestionGroupId(Long questionGroupId) {
        this.questionGroupId = questionGroupId;
    }

    public String getQuestionBizGroupId() {
        return questionBizGroupId;
    }

    public void setQuestionBizGroupId(String questionBizGroupId) {
        this.questionBizGroupId = questionBizGroupId;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public String getQuestionBizId() {
        return questionBizId;
    }

    public void setQuestionBizId(String questionBizId) {
        this.questionBizId = questionBizId;
    }

    public String getRoundId() {
        return roundId;
    }

    public void setRoundId(String roundId) {
        this.roundId = roundId;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public Boolean getScored() {
        return isScored;
    }

    public void setScored(Boolean scored) {
        isScored = scored;
    }

    public Boolean getJudged() {
        return isJudged;
    }

    public void setJudged(Boolean judged) {
        isJudged = judged;
    }

    public Boolean getCorrect() {
        return isCorrect;
    }

    public void setCorrect(Boolean correct) {
        isCorrect = correct;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getQuestionGroupType() {
        return questionGroupType;
    }

    public void setQuestionGroupType(Integer questionGroupType) {
        this.questionGroupType = questionGroupType;
    }
}