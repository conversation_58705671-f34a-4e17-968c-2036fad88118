package com.unipus.digitalbook.model.po.question;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.SmallQuestion;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * 
 * @TableName question_group
 */
public class QuestionGroupPO implements Serializable {
    /**
     * 题组ID
     */
    private Long id;

    /**
     * 父题组id
     */
    private Long parentId;

    /**
     * 题组业务id
     */
    private String bizGroupId;

    /**
     * 题组版本号
     */
    private String versionNumber;

    /**
     * 题组类型
     */
    private Integer type;

    /**
     * 作答提示
     */
    private String direction;

    /**
     * 材料内容
     */
    private String content;

    /**
     * 答案解析 如果为空则说明不需要解析
     */
    private String analysis;

    /**
     * 题干
     */
    private String questionText;

    /**
     * 题组难度级别0-5
     */
    private Integer difficulty;

    /**
     * 题组分
     */
    private BigDecimal score;

    /**
     * 题组排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    public QuestionGroupPO(){}
    public QuestionGroupPO(BigQuestionGroup bigQuestionGroup) {
        this.setBizGroupId(bigQuestionGroup.getBizGroupId());
        this.setParentId(bigQuestionGroup.getParentId());
        this.setType(bigQuestionGroup.getType());
        this.setDirection(bigQuestionGroup.getDirection());
        this.setContent(bigQuestionGroup.getContent());
        this.setAnalysis(bigQuestionGroup.getAnalysis());
        this.setQuestionText(bigQuestionGroup.getQuestionText());
        this.setDifficulty(bigQuestionGroup.getDifficulty());
        this.setScore(bigQuestionGroup.getScore());
        this.setEnable(bigQuestionGroup.getEnable());
        this.setVersionNumber(bigQuestionGroup.getVersionNumber());
        this.setCreateBy(bigQuestionGroup.getCreateBy());
        this.setSortOrder(bigQuestionGroup.getSortOrder());
        this.setUpdateBy(bigQuestionGroup.getUpdateBy());
        this.setCreateTime(bigQuestionGroup.getCreateTime());
        this.setUpdateTime(bigQuestionGroup.getUpdateTime());
    }

    public QuestionGroupPO(Question question, Long parentId) {
        this.setBizGroupId(question.getBizQuestionId());
        this.setParentId(parentId);
        this.setType(question.getQuestionType());
        this.setDirection(question.getDirection());
        this.setContent(question.getContent());
        this.setAnalysis(question.getAnalysis());
        Optional.ofNullable(question.getQuestionText())
                .ifPresent(q -> this.setQuestionText(q.serialize()));
        this.setDifficulty(question.getDifficulty());
        this.setScore(question.getScore());
        this.setCreateBy(question.getCreateBy());
        this.setUpdateBy(question.getUpdateBy());
        this.setCreateTime(question.getCreateTime());
        this.setUpdateTime(question.getUpdateTime());
        this.setVersionNumber(question.getVersionNumber());
        this.setEnable(question.getEnable());
        this.setSortOrder(question.getSortOrder());
    }

    public BigQuestionGroup toBigQuestion() {
        BigQuestionGroup bigQuestionGroup = new BigQuestionGroup();
        bigQuestionGroup.setId(this.id);
        bigQuestionGroup.setBizGroupId(this.bizGroupId);
        bigQuestionGroup.setVersionNumber(this.versionNumber);
        bigQuestionGroup.setParentId(this.parentId);
        bigQuestionGroup.setType(this.type);
        bigQuestionGroup.setDirection(this.direction);
        bigQuestionGroup.setContent(this.content);
        bigQuestionGroup.setAnalysis(this.analysis);
        bigQuestionGroup.setQuestionText(this.questionText);
        bigQuestionGroup.setDifficulty(this.difficulty);
        bigQuestionGroup.setScore(this.score);
        bigQuestionGroup.setCreateBy(this.createBy);
        bigQuestionGroup.setUpdateBy(this.updateBy);
        bigQuestionGroup.setCreateTime(this.createTime);
        bigQuestionGroup.setUpdateTime(this.updateTime);
        bigQuestionGroup.setSortOrder(this.sortOrder);
        return bigQuestionGroup;
    }

    public SmallQuestion toSmallQuestion() {
        SmallQuestion smallQuestion = new SmallQuestion();
        smallQuestion.setId(this.getId());
        smallQuestion.setBizQuestionId(this.getBizGroupId());
        smallQuestion.setQuestionType(this.getType());
        smallQuestion.setVersionNumber(this.getVersionNumber());
        smallQuestion.setDirection(this.getDirection());
        smallQuestion.setContent(this.getContent());
        smallQuestion.setAnalysis(this.getAnalysis());
        smallQuestion.setQuestionText(QuestionText.deserialize(this.getQuestionText()));
        smallQuestion.setDifficulty(this.getDifficulty());
        smallQuestion.setScore(this.getScore());
        smallQuestion.setCreateBy(this.getCreateBy());
        smallQuestion.setUpdateBy(this.getUpdateBy());
        smallQuestion.setCreateTime(this.getCreateTime());
        smallQuestion.setUpdateTime(this.getUpdateTime());
        smallQuestion.setSortOrder(this.getSortOrder());
        return smallQuestion;
    }

    public QuestionGroupPO(QuestionGroupPO source, Long parentId, String versionNumber, Long userId){
        this.id = null;
        this.parentId = parentId;
        this.bizGroupId = source.getBizGroupId();
        this.versionNumber = versionNumber;
        this.type = source.getType();
        this.direction = source.getDirection();
        this.content = source.getContent();
        this.analysis = source.getAnalysis();
        this.questionText = source.getQuestionText();
        this.difficulty = source.getDifficulty();
        this.score = source.getScore();
        this.sortOrder = source.getSortOrder();
        this.createBy = userId;
        this.enable = source.getEnable();
    }

    /**
     * 题组ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 题组ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 父题组id
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * 父题组id
     */
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getBizGroupId() {
        return bizGroupId;
    }


    public void setBizGroupId(String bizGroupId) {
        this.bizGroupId = bizGroupId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    /**
     * 题组类型
     */
    public Integer getType() {
        return type;
    }

    /**
     * 题组类型
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 作答提示
     */
    public String getDirection() {
        return direction;
    }

    /**
     * 作答提示
     */
    public void setDirection(String direction) {
        this.direction = direction;
    }

    /**
     * 材料内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 材料内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 答案解析 如果为空则说明不需要解析
     */
    public String getAnalysis() {
        return analysis;
    }

    /**
     * 答案解析 如果为空则说明不需要解析
     */
    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    /**
     * 题干
     */
    public String getQuestionText() {
        return questionText;
    }

    /**
     * 题干
     */
    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    /**
     * 题组难度级别0-5
     */
    public Integer getDifficulty() {
        return difficulty;
    }

    /**
     * 题组难度级别0-5
     */
    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    /**
     * 题组分
     */
    public BigDecimal getScore() {
        return score;
    }

    /**
     * 题组分
     */
    public void setScore(BigDecimal score) {
        this.score = score;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
}