package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.paper.UserPaperScore;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * 试卷轮次表
 * Table Name: paper_round
 */
public class PaperRoundPO {
    // 主键ID
    private String id;
    // 评分批次ID
    private String scoreBatchId;
    // 用户得分
    private BigDecimal userScore;
    // 标准分
    private BigDecimal standardScore;
    // 题目总数
    private Integer questionCount;
    // 用户正确题目数
    private Integer correctCount;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 创建人
    private String createBy;
    // 更新人
    private String updateBy;
    // 是否启用
    private Boolean enable;

    public PaperRoundPO() {}

    public PaperRoundPO(PaperInstance paperInstance, UserPaperScore userPaperScore, String openId) {
        // 计算用户试卷总分
        BigDecimal localUserScore = Optional.ofNullable(userPaperScore).map(UserPaperScore::getUserScore)
                .orElse(BigDecimal.ZERO);
        // 试卷标准分
        BigDecimal localStandardScore = Optional.ofNullable(userPaperScore).map(UserPaperScore::getStandardScore)
                .orElse(paperInstance.getTotalScore());
        // 试卷题目总数（小题数量）
        Integer totalSmallQuestionCount = Optional.ofNullable(userPaperScore).map(UserPaperScore::fetchTotalObjectiveSmallQuestionCount)
                .orElse(paperInstance.getSmallQuestionTotalCountInPaper());
        // 用户试卷题目正确数量（小题数量）
        Integer correctSmallQuestionCount = Optional.ofNullable(userPaperScore).map(UserPaperScore::fetchCorrectObjectiveSmallQuestionCount)
                .orElse(0);

        this.id = paperInstance.getInstanceId();
        this.scoreBatchId = paperInstance.getScoreBatchId();
        this.userScore = localUserScore;
        this.standardScore = localStandardScore;
        this.questionCount = totalSmallQuestionCount;
        this.correctCount = correctSmallQuestionCount;
        this.createBy = openId;
        this.updateBy = openId;
        this.enable = true;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getScoreBatchId() {
        return scoreBatchId;
    }

    public void setScoreBatchId(String scoreBatchId) {
        this.scoreBatchId = scoreBatchId;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public Integer getCorrectCount() {
        return correctCount;
    }

    public void setCorrectCount(Integer correctCount) {
        this.correctCount = correctCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}