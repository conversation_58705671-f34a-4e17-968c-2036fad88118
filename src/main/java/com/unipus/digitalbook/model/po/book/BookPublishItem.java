package com.unipus.digitalbook.model.po.book;


import com.unipus.digitalbook.model.entity.book.BookChangeItemEntity;

import java.io.Serializable;

public class BookPublishItem implements Serializable {
    /**
     * 1：教材基本信息
     * 2：教材简介
     * 3：版权信息
     * 4：配套资源
     * 5：教材章节
     */
    private Integer typeCode;
    private String resourceId;
    private Long versionId;

    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public BookChangeItemEntity toEntity() {
        BookChangeItemEntity entity = new BookChangeItemEntity();
        entity.setTypeCode(this.typeCode);
        entity.setResourceId(this.resourceId);
        entity.setVersionId(this.versionId);
        return entity;
    }
}
