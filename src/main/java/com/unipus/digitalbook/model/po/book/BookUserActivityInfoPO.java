package com.unipus.digitalbook.model.po.book;

import com.unipus.digitalbook.model.entity.Series;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.BookBasic;
import com.unipus.digitalbook.model.entity.book.UserBook;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户操作教材行为
 */
public class BookUserActivityInfoPO implements Serializable {

    /**
     * 用户 ID，标识具体用户
     */
    private Long userId;

    /**
     * 编辑时间
     */
    private Date editTime;

    /**
     * 加入编辑时间
     */
    private Date joinEditTime;

    /**
     * 加入预览时间
     */
    private Date joinPreviewTime;

    /**
     * 教材 ID，关联到 book 表的主键
     */
    private String bookId;

    public BookUserActivityInfoPO() {
        super();
    }

    public static List<UserBook> toUserBookEntities(List<BookPO> books,
                                                    Long userId,
                                                    Map<String, BookUserActivityInfoPO> bookUserActivityInfoMap,
                                                    Map<String, BookBasicPO> bookBasicMap,
                                                    Map<Long, Series> seriesMap) {
        return books.stream().map(book -> toUserBookEntity(userId, book, bookUserActivityInfoMap.get(book.getId()), bookBasicMap.get(book.getId()),
                seriesMap.get(bookBasicMap.get(book.getId()).getSeriesId()))).toList();
    }

    public static UserBook toUserBookEntity(Long userId, BookPO bookPO, BookUserActivityInfoPO userActivityInfo, BookBasicPO bookBasic, Series series) {
        UserBook userBook = new UserBook();
        userBook.setUserId(userId);
        if (userActivityInfo != null) {
            userBook.setEditTime(userActivityInfo.getEditTime());
            userBook.setJoinEditTime(userActivityInfo.getJoinEditTime());
            userBook.setJoinPreviewTime(userActivityInfo.getJoinPreviewTime());
        }
        if (bookPO != null) {
            Book book = bookPO.toEntity();
            if (bookBasic != null) {
                BookBasic entity = bookBasic.toEntity();
                entity.setSeries(series);
                book.fillBasicInfo(entity);
            }
            userBook.setBook(book);
            userBook.setBookId(book.getId());
        }
        return userBook;
    }

    public Date getEditTime() {
        return editTime;
    }

    public void setEditTime(Date editTime) {
        this.editTime = editTime;
    }

    public Date getJoinEditTime() {
        return joinEditTime;
    }

    public void setJoinEditTime(Date joinEditTime) {
        this.joinEditTime = joinEditTime;
    }

    public Date getJoinPreviewTime() {
        return joinPreviewTime;
    }

    public void setJoinPreviewTime(Date joinPreviewTime) {
        this.joinPreviewTime = joinPreviewTime;
    }

    /**
     * 用户 ID，标识具体用户
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * 用户 ID，标识具体用户
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }


    /**
     * 教材 ID，关联到 book 表的主键
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * 教材 ID，关联到 book 表的主键
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }
}