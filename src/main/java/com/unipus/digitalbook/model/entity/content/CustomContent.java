package com.unipus.digitalbook.model.entity.content;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自建内容实体类
 */
@Slf4j
public class CustomContent {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 内容业务ID
     */
    private String bizId;

    /**
     * 内容类型  1：自定义章节/2：自定义段落
     */
    private Integer type;

    /**
     * 内容状态  0：编写中/1：待发布/2：已发布
     */
    private Integer status;

    /**
     * 内容名称
     */
    private String name;

    /**
     * 内容
     */
    private String content;

    /**
     * 学生内容
     */
    private String studentContent;

    /**
     * 内容哈希码
     */
    private String contentHashCode;

    /**
     * 学生内容哈希码
     */
    private String studentContentHashCode;

    /**
     * 头图地址
     */
    private String headerImg;

    /**
     * 资源信息
     */
    private String resource;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 自建内容目录节点列表
     */
    private List<CustomContentHeaderNode> catalogNodeList;

    /**
     * 自建内容结构节点列表
     */
    private List<CustomContentNode> totalStructNodeList;

    /**
     * 章节的题
     */
    private List<BigQuestionGroup> questionList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStudentContent() {
        return studentContent;
    }

    public void setStudentContent(String studentContent) {
        this.studentContent = studentContent;
    }

    public String getContentHashCode() {
        return contentHashCode;
    }

    public void setContentHashCode(String contentHashCode) {
        this.contentHashCode = contentHashCode;
    }

    public String getStudentContentHashCode() {
        return studentContentHashCode;
    }

    public void setStudentContentHashCode(String studentContentHashCode) {
        this.studentContentHashCode = studentContentHashCode;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public List<CustomContentHeaderNode> getCatalogNodeList() {
        return catalogNodeList;
    }

    public void setCatalogNodeList(List<CustomContentHeaderNode> catalogNodeList) {
        this.catalogNodeList = catalogNodeList;
    }

    public List<CustomContentNode> getTotalStructNodeList() {
        return totalStructNodeList;
    }

    public void setTotalStructNodeList(List<CustomContentNode> totalStructNodeList) {
        this.totalStructNodeList = totalStructNodeList;
    }

    public List<BigQuestionGroup> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(List<BigQuestionGroup> questionList) {
        this.questionList = questionList;
    }

    public void fillCatalogNodeList(String catalog) {
        this.catalogNodeList = parseCatalog(catalog);
    }

    private List<CustomContentHeaderNode> parseCatalog(String catalog) {
        List<CustomContentHeaderNode> headerNodeResList = new ArrayList<>();
        if (StringUtils.isBlank(catalog)) {
            log.debug("目录为空，返回空列表");
            return headerNodeResList;
        }

        try {
            return parseAndValidateCatalog(catalog);
        } catch (JSONException e) {
            log.error("解析目录JSON格式错误: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("解析目录时发生未知异常: {}", e.getMessage(), e);
        }

        return headerNodeResList;
    }

    private List<CustomContentHeaderNode> parseAndValidateCatalog(String catalog) {
        // 解析JSON字符串到CustomContentHeaderNode列表
        List<CustomContentHeaderNode> parsedNodes = JSON.parseArray(catalog, CustomContentHeaderNode.class);
        if (parsedNodes == null) {
            return new ArrayList<>();
        }

        // 过滤掉无效的CustomContentHeaderNode
        List<CustomContentHeaderNode> validNodes = new ArrayList<>();
        for (CustomContentHeaderNode node : parsedNodes) {
            if (isValidHeaderNode(node)) {
                validNodes.add(node);
            } else {
                log.warn("忽略无效的目录节点: {}\n 原始数据：{}", node, catalog);
            }
        }

        return validNodes;
    }

    /**
     * 验证CustomContentHeaderNode对象是否有效
     *
     * @param node 待验证的CustomContentHeaderNode对象
     * @return 是否有效
     */
    private boolean isValidHeaderNode(CustomContentHeaderNode node) {
        if (node == null) {
            return false;
        }
        // 验证必要字段是否为空
        if (StringUtils.isBlank(node.getId()) ||
                StringUtils.isBlank(node.getType())) {
            return false;
        }
        // 验证标题类型是否符合h1-h6格式
        String nodeType = node.getType().toLowerCase();
        if (!nodeType.matches("h[1-6]")) {
            return false;
        }
        // key值验证 - 确保为正整数
        if (node.getKey() <= 0) {
            return false;
        }

        // 其他可能的业务逻辑验证...
        if (StringUtils.isBlank(node.getText())) {
            return false;
        }
        log.debug("验证通过: {}", node);
        return true;
    }

    /**
     * 获取自建内容节点树结构
     * 参照ChapterVersion.getChapterNodeTree()方法实现
     *
     * @return 构建好的树形结构节点列表
     */
    public List<CustomContentNode> getCustomContentNodeTree() {
        if (totalStructNodeList == null || totalStructNodeList.isEmpty()) {
            return new ArrayList<>();
        }

        // 结果列表，用于存储顶层节点
        List<CustomContentNode> result = new ArrayList<>();

        // 用于跟踪当前各级标题的节点
        Map<Integer, CustomContentNode> headerStack = new HashMap<>();

        for (CustomContentNode node : totalStructNodeList) {
            // 创建新节点的深拷贝，避免修改原始列表
            CustomContentNode newNode = copyNode(node);

            // 精确匹配h1-h6标题
            if (isHeaderNode(node.getType())) {
                try {
                    // 获取标题级别 (h1->1, h2->2, ...)
                    int level = Integer.parseInt(node.getType().substring(1));

                    // 清理更低级别的标题节点
                    for (int i = level + 1; i <= 6; i++) {
                        headerStack.remove(i);
                    }

                    if (level == 1) {
                        // 顶级标题直接加入结果列表
                        result.add(newNode);
                        headerStack.put(level, newNode);
                    } else {
                        // 子标题加入上一级标题的children
                        CustomContentNode parent = null;
                        // 寻找最近的上级标题
                        for (int i = level - 1; i >= 1; i--) {
                            if (headerStack.containsKey(i)) {
                                parent = headerStack.get(i);
                                break;
                            }
                        }

                        if (parent != null) {
                            if (parent.getChildren() == null) {
                                parent.setChildren(new ArrayList<>());
                            }
                            parent.getChildren().add(newNode);
                            headerStack.put(level, newNode);
                        } else {
                            // 如果没有找到父节点（不应该发生），则作为顶级节点
                            result.add(newNode);
                            headerStack.put(level, newNode);
                        }
                    }
                } catch (NumberFormatException e) {
                    // 如果解析标题级别失败，作为内容节点处理
                    addContentNode(newNode, headerStack, result);
                }
            } else {
                // 内容节点处理
                addContentNode(newNode, headerStack, result);
            }
        }

        return result;
    }

    /**
     * 创建自建内容节点的深拷贝，避免修改原始列表
     * 参照ChapterVersion.copyNode()方法实现
     *
     * @param original 原始节点
     * @return 深拷贝的节点
     */
    private CustomContentNode copyNode(CustomContentNode original) {
        CustomContentNode copy = new CustomContentNode();
        copy.setId(original.getId());
        copy.setText(original.getText());
        copy.setType(original.getType());
        copy.setWordCount(original.getWordCount());
        copy.setCjkWordCount(original.getCjkWordCount());
        copy.setNonCjkWordCount(original.getNonCjkWordCount());
        copy.setAudioDuration(original.getAudioDuration());
        copy.setVideoDuration(original.getVideoDuration());
        copy.setOffset(original.getOffset());
        copy.setQuestionType(original.getQuestionType());
        // 不复制children，会在构建树结构时添加
        return copy;
    }

    /**
     * 辅助方法：添加内容节点
     * 参照ChapterVersion.addContentNode()方法实现
     *
     * @param node 要添加的节点
     * @param headerStack 标题节点栈
     * @param result 结果列表
     */
    private void addContentNode(CustomContentNode node, Map<Integer, CustomContentNode> headerStack, List<CustomContentNode> result) {
        // 尝试找到最近的标题节点作为父节点
        CustomContentNode parent = null;
        for (int i = 6; i >= 1; i--) {
            if (headerStack.containsKey(i)) {
                parent = headerStack.get(i);
                break;
            }
        }

        if (parent != null) {
            // 如果找到父节点，将内容节点添加到其子节点列表
            if (parent.getChildren() == null) {
                parent.setChildren(new ArrayList<>());
            }
            parent.getChildren().add(node);
        } else {
            // 如果没有找到父节点，将内容节点添加到顶级列表
            result.add(node);
        }
    }

    /**
     * 检查是否为h1-h6标题节点
     * 参照ChapterVersion.isHeaderNode()方法实现
     *
     * @param type 节点类型
     * @return 是否为标题节点
     */
    private boolean isHeaderNode(String type) {
        return type != null &&
                (type.equals("h1") || type.equals("h2") || type.equals("h3") ||
                        type.equals("h4") || type.equals("h5") || type.equals("h6"));
    }
}
