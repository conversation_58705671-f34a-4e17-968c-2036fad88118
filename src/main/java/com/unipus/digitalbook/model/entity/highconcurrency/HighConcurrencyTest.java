package com.unipus.digitalbook.model.entity.highconcurrency;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 高并发测试实体
 * 用于测试高并发场景下的数据操作
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("high_concurrency_test")
public class HighConcurrencyTest {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 测试名称
     */
    @TableField("test_name")
    private String testName;
    
    /**
     * 测试值
     */
    @TableField("test_value")
    private String testValue;
    
    /**
     * 计数器，用于并发测试
     */
    @TableField("counter")
    private Integer counter;
    
    /**
     * 状态
     */
    @TableField("status")
    private String status;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 版本号（乐观锁）
     */
    @Version
    @TableField("version")
    private Integer version;
}