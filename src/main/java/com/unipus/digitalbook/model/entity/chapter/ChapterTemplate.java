package com.unipus.digitalbook.model.entity.chapter;

import java.util.Date;


/**
 * 章节内容模板类
 */
public class ChapterTemplate {
    /**
     * 模板ID
     */
    private Integer id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板来源章节ID
     */
    private String fromChapterId;

    /**
     * 模板JSON存储地址
     */
    private String url;

    /**
     * 模板预览图片地址
     */
    private String previewImageUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效（0-无效，1-有效）
     */
    private Boolean enable;

    public static class Builder {
        private Integer id;
        private String name;
        private String fromChapterId;
        private String url;
        private String previewImageUrl;
        private Long createBy;
        private Long updateBy;

        public Builder() {
            super();
        }

        public static Builder newBuilder() {
            return new Builder();
        }

        // 提供链式调用来设置各个属性
        public Builder id(Integer id) {
            this.id = id;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder fromChapterId(String fromChapterId) {
            this.fromChapterId = fromChapterId;
            return this;
        }

        public Builder url(String url) {
            this.url = url;
            return this;
        }

        public Builder previewImageUrl(String previewImageUrl) {
            this.previewImageUrl = previewImageUrl;
            return this;
        }

        public Builder createBy(Long createBy) {
            this.createBy = createBy;
            return this;
        }

        public Builder updateBy(Long updateBy) {
            this.updateBy = updateBy;
            return this;
        }

        public ChapterTemplate build() {
            if ( name == null || fromChapterId == null || url == null) {
                throw new IllegalStateException("ChapterTemplate对象缺少必要属性");
            }
            if (createBy == null && updateBy == null) {
                throw new IllegalStateException("ChapterTemplate对象缺少必要属性");
            }
            ChapterTemplate chapterTemplate = new ChapterTemplate();
            chapterTemplate.setId(id);
            chapterTemplate.setName(name);
            chapterTemplate.setFromChapterId(fromChapterId);
            chapterTemplate.setUrl(url);
            chapterTemplate.setPreviewImageUrl(previewImageUrl);
            chapterTemplate.setCreateBy(createBy);
            chapterTemplate.setUpdateBy(updateBy);
            // 其他属性（createTime, updateTime, enable）在Builder中未定义，因此这里不进行设置
            return chapterTemplate;
        }
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = trimString(name);
    }

    public String getFromChapterId() {
        return fromChapterId;
    }

    public void setFromChapterId(String fromChapterId) {
        this.fromChapterId = trimString(fromChapterId);
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = trimString(url);
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = trimString(previewImageUrl);
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    // 辅助方法：去除字符串两端空格
    private String trimString(String str) {
        return str == null ? null : str.trim();
    }
}