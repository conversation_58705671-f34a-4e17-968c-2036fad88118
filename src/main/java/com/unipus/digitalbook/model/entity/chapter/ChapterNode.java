package com.unipus.digitalbook.model.entity.chapter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

import static com.unipus.digitalbook.model.entity.chapter.ChapterNode.ChapterTreeBuilder.extractTocTree;
import static com.unipus.digitalbook.model.entity.chapter.ChapterNode.ChapterTreeBuilder.printTree;
import static com.unipus.digitalbook.model.entity.chapter.ChapterNode.LexicalJsonParser.parseLexicalJson;

@Slf4j
public class ChapterNode {
    String id;
    String text;
    String type;

    @Deprecated
    Long wordCount;
    /**
     * 中日韩字数
     */
    Long cjkWordCount;
    /**
     * 非中日韩字数
     */
    Long nonCjkWordCount;
    Long audioDuration;
    Long videoDuration;
    /**
     * 节点位置
     */
    Integer offset;
    /**
     * 节点题目类型
     */
    String questionType;
    List<ChapterNode> children;

    /**
     * children 属性名
     */
    private static final String CHILDREN_FILED = "children";
    String parentId;

    Integer totalNonHDescendants;
    boolean isHeader;
    public ChapterNode(){
        super();
    }
    public ChapterNode(String id, String text, String type) {
        this.id = id;
        this.text = text;
        this.type = type;
        this.children = new ArrayList<>();
    }
    @Override
    public String toString() {
        return new StringJoiner(", ", ChapterNode.class.getSimpleName() + "[", "]").add("type='" + type + "'").add("id='" + id + "'")

                .add("text='" + text + "'")

                .toString();
    }

    public static List<ChapterNode> buildTree(List<ChapterNode> flatList) {
        if (flatList == null || flatList.isEmpty()) {
            return new ArrayList<>();
        }
        // 结果列表，用于存储顶层节点
        List<ChapterNode> roots = new ArrayList<>();

        // 用于跟踪当前各级标题的节点
        Map<Integer, ChapterNode> headerStack = new HashMap<>();

        for (ChapterNode node : flatList) {
            // 精确匹配h1-h6标题
            if (isHeaderNode(node.getType())) {
                try {
                    node.setHeader(true);
                    // 获取标题级别 (h1->1, h2->2, ...)
                    int level = Integer.parseInt(node.getType().substring(1));

                    // 清理更低级别的标题节点
                    for (int i = level + 1; i <= 6; i++) {
                        headerStack.remove(i);
                    }

                    if (level == 1) {
                        // 顶级标题直接加入结果列表
                        roots.add(node);
                        headerStack.put(level, node);
                    } else {
                        // 子标题加入上一级标题的children
                        ChapterNode parent = null;
                        // 寻找最近的上级标题
                        for (int i = level - 1; i >= 1; i--) {
                            if (headerStack.containsKey(i)) {
                                parent = headerStack.get(i);
                                break;
                            }
                        }

                        if (parent != null) {
                            if (parent.getChildren() == null) {
                                parent.setChildren(new ArrayList<>());
                            }
                            node.setParentId(parent.getId());
                            parent.getChildren().add(node);
                            headerStack.put(level, node);
                        } else {
                            // 如果没有找到父节点（不应该发生），则作为顶级节点
                            roots.add(node);
                            headerStack.put(level, node);
                        }
                    }
                } catch (NumberFormatException e) {
                    // 如果解析标题级别失败，作为内容节点处理
                    addContentNode(node, headerStack, roots);
                }
            } else {
                // 内容节点处理
                addContentNode(node, headerStack, roots);
            }
        }

        return roots;
    }

    private static boolean isHeaderNode(String type) {
        return type != null &&
                (type.equals("h1") || type.equals("h2") || type.equals("h3") ||
                        type.equals("h4") || type.equals("h5") || type.equals("h6"));
    }

    private static void addContentNode(ChapterNode node, Map<Integer, ChapterNode> headerStack, List<ChapterNode> result) {
        // 尝试找到最近的标题节点作为父节点
        ChapterNode parent = null;
        for (int i = 6; i >= 1; i--) {
            if (headerStack.containsKey(i)) {
                parent = headerStack.get(i);
                break;
            }
        }

        if (parent != null) {
            // 如果找到父节点，将内容节点添加到其子节点列表
            if (parent.getChildren() == null) {
                parent.setChildren(new ArrayList<>());
            }
            node.setParentId(parent.getId());
            parent.getChildren().add(node);
        } else {
            // 如果没有找到父节点，将内容节点添加到顶级列表
            result.add(node);
        }
    }
    public static void calculateTotalNonHDescendants(ChapterNode root, int deep) {
        if (root == null || deep > 6) return;
        List<ChapterNode> children = root.getChildren();
        if (children == null || children.isEmpty()) {
            root.setTotalNonHDescendants(0);
            return;
        }
        int total = 0;
        for (ChapterNode child : children) {
            calculateTotalNonHDescendants(child, deep + 1);
            if (!isHeaderNode(child.getType())) {
                total += 1;
            }
            total += child.getTotalNonHDescendants();
        }
        root.setTotalNonHDescendants(total);
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getWordCount() {
        return wordCount;
    }

    public void setWordCount(Long wordCount) {
        this.wordCount = wordCount;
    }

    public Long getCjkWordCount() {
        return cjkWordCount;
    }

    public void setCjkWordCount(Long cjkWordCount) {
        this.cjkWordCount = cjkWordCount;
    }

    public Long getNonCjkWordCount() {
        return nonCjkWordCount;
    }

    public void setNonCjkWordCount(Long nonCjkWordCount) {
        this.nonCjkWordCount = nonCjkWordCount;
    }

    public Long getAudioDuration() {
        return audioDuration;
    }

    public void setAudioDuration(Long audioDuration) {
        this.audioDuration = audioDuration;
    }

    public Long getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(Long videoDuration) {
        this.videoDuration = videoDuration;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public List<ChapterNode> getChildren() {
        return children;
    }

    public void setChildren(List<ChapterNode> children) {
        this.children = children;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getTotalNonHDescendants() {
        return totalNonHDescendants;
    }

    public void setTotalNonHDescendants(Integer totalNonHDescendants) {
        this.totalNonHDescendants = totalNonHDescendants;
    }

    public boolean isHeader() {
        return isHeader;
    }

    public void setHeader(boolean header) {
        isHeader = header;
    }

    public static class LexicalJsonParser {
        /**
         * 将 Lexical JSON 解析为 ChapterNode 列表
         *
         * @param jsonString Lexical 格式的 JSON 字符串
         * @return ChapterNode 对象列表
         */
        public static List<ChapterNode> parseLexicalJson(String jsonString) {
            List<ChapterNode> chapterNodes = new ArrayList<>();

            // 使用 Fastjson2 解析 JSON 字符串
            JSONObject rootObj = JSON.parseObject(jsonString);

            // 获取 root 对象
            JSONObject root = rootObj.getJSONObject("root");

            // 获取 children 数组
            List<JSONObject> children = root.getList(CHILDREN_FILED, JSONObject.class);

            // 遍历 children 数组中的每个元素
            for (JSONObject child : children) {
                // 先处理当前节点
                ChapterNode node = parseNodeToChapterNode(child);
                if (node != null) {
                    chapterNodes.add(node);
                }
                // 递归寻找嵌套的标题节点
                findNestedHeadings(child, chapterNodes);
            }

            return chapterNodes;
        }

        /**
         * 递归查找嵌套的标题节点
         *
         * @param nodeObj      当前处理的节点
         * @param chapterNodes 收集节点的列表
         */
        private static void findNestedHeadings(JSONObject nodeObj, List<ChapterNode> chapterNodes) {
            if (nodeObj == null || !nodeObj.containsKey(CHILDREN_FILED)) {
                return;
            }

            List<JSONObject> children = nodeObj.getList(CHILDREN_FILED, JSONObject.class);
            if (children == null) {
                return;
            }

            for (JSONObject child : children) {
                // 处理标题节点
                if ("heading".equals(child.getString("type"))) {
                    ChapterNode node = parseNodeToChapterNode(child);
                    if (node != null) {
                        chapterNodes.add(node);
                    }
                }

                // 继续递归查找
                findNestedHeadings(child, chapterNodes);
            }
        }

        /**
         * 将单个节点解析为 ChapterNode 对象
         *
         * @param nodeObj 单个节点的 JSONObject 表示
         * @return 解析后的 ChapterNode 对象，如果完全无法解析则返回 null
         */
        private static ChapterNode parseNodeToChapterNode(JSONObject nodeObj) {
            if (nodeObj == null) {
                return null;
            }

            String id = null;
            String text;
            String nodeType;

            // 获取 id
            if (nodeObj.containsKey("$")) {
                JSONObject metaObj = nodeObj.getJSONObject("$");
                if (metaObj != null && metaObj.containsKey("__anchor__")) {
                    id = metaObj.getString("__anchor__");
                }
            }

            // 获取类型和文本，根据节点类型进行不同处理
            String type = nodeObj.getString("type");

            if ("heading".equals(type)) {
                // 对于标题类型的节点
                nodeType = nodeObj.getString("tag"); // 获取 tag 作为 type
                text = extractTextContent(nodeObj);
            } else if ("text".equals(type)) {
                // 直接文本节点
                nodeType = "text";
                text = nodeObj.getString("text");
            } else {
                // 其他类型的节点
                nodeType = type;
                text = extractTextContent(nodeObj);
            }

            // 只要有一个值不为空，就创建节点
            if (id != null || text != null || nodeType != null) {
                return new ChapterNode(id, text, nodeType);
            }

            return null;
        }

        /**
         * 从节点中提取所有文本内容
         *
         * @param nodeObj 节点对象
         * @return 提取的文本内容，没有文本则返回null
         */
        private static String extractTextContent(JSONObject nodeObj) {
            if (nodeObj == null) {
                return null;
            }

            // 如果节点自身有text属性，直接返回
            if (nodeObj.containsKey("text")) {
                return nodeObj.getString("text");
            }

            // 检查是否有子节点
            if (!nodeObj.containsKey(CHILDREN_FILED)) {
                return null;
            }

            List<JSONObject> children = nodeObj.getList(CHILDREN_FILED, JSONObject.class);
            if (children == null || children.isEmpty()) {
                return null;
            }

            // 收集所有子节点的文本
            StringBuilder sb = new StringBuilder();
            for (JSONObject child : children) {
                if (child.containsKey("text")) {
                    sb.append(child.getString("text"));
                } else {
                    String childText = extractTextContent(child);
                    if (childText != null) {
                        sb.append(childText);
                    }
                }
            }
            return !sb.isEmpty() ? sb.toString() : null;
        }
    }

    public static void main(String[] args) {
        String jsonString = """
                {"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"第二百五十六章 诛仙","type":"text","version":1}],"direction":"ltr","format":"center","indent":0,"type":"heading","version":1,"$":{"__anchor__":"Ti__tlwx8VTVPh1LqcyTV"},"tag":"h1"},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"rZQRoVT47GsEwPootWJ9w"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"H2标题","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"heading","version":1,"$":{"__anchor__":"XCTixXO88GERBPm7kHjjv"},"tag":"h2"},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"血色红芒遮天蔽日飘了过来，通天峰上看去，整个天幕都变作了削红色，暗红的乌云滚滚翻腾，让人看着便有一种透不过气来的感觉。在这片红云之下，什么东西都被染作了红色，天是红的，山是红的，云海上票动的云气是红的，虹桥上流下的水珠是红的，甚至仿佛连凛冽的山风吹过，","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"M8Eje0ZFb-YavRS3c3e7n"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"myd4oel1zY9_NIsnDk4f4"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"仿佛也是红色的。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"heading","version":1,"$":{"__anchor__":"Lcc8PldB9wzC-4JYv1f8e"},"tag":"h5"},{"children":[{"children":[{"children":[{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"浓浓的血腥气，从风中吹来，弥漫在通天峰上。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"LGNB-NoPHwkxip28q-UOJ"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":3,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"3ibwzNORmzd7Udd2n_BV9"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":1,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"CbyX8hasvFU3j0BTYp4VJ"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":1,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"ZJ4pqaq985sEpQqlv-b3E"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":1,"rowSpan":1}],"direction":"ltr","format":"","indent":0,"type":"tablerow","version":1},{"children":[{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"无数个身影正从通天峰下从四面八方向上攀爬而来，密密麻麻几乎看不到有缝隙，到处都是人影，每个人的眼中都闪烁着红色异样光芒。看着这些已经疯狂的人群，其中大部分人从身上衣着来看都是青云山下居住的普通百姓，然而寻常百姓又岂能像这般行动矫健攀爬如猿猴，这其中的古怪，自然便是在夺去他们心志的那诡异血芒中了","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"H9BW3a6bF2v1F67CZ1twf"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":2,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"NsDisXzIBcbRdcYF1EML_"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"ux1RgglV0OcuQZ_kadRED"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"y5YxQr4cEjEaiKlPiVAv0"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1}],"direction":"ltr","format":"","indent":0,"type":"tablerow","version":1},{"children":[{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"41dSoA_ZNj4dRyskTtt4p"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":2,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"nL4-E759AqWz4SGkTYwr7"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"aa4i_8avzRrwQFxJ4KY9F"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"AlR2-M8o6m0UWA_mllGgF"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1}],"direction":"ltr","format":"","indent":0,"type":"tablerow","version":1}],"direction":"ltr","format":"","indent":0,"type":"table","version":1,"colWidths":[224,92,92,92]}],"direction":"ltr","format":"","indent":0,"type":"table-container","version":1},{"type":"insert-image","version":1,"mediaFile":{"fileUrl":"https://ipublish-test-1313083974.cos.ap-beijing.myqcloud.com/m912j1ln_m76xezt3/640.png","fileName":"640","mediaName":"诛仙"},"anchor":"mgihaw-TJOSxiEpSdtbMs","width":"auto","height":"auto","mediaAlign":"AlignLeft"},{"type":"question-block","version":1,"questionType":"fill_blanks_group","questionData":"{\\"recognition\\":true,\\"groupId\\":\\"14c959de-2abd-4d8b-a715-5708a6c9dced\\",\\"direction\\":\\"{\\\\\\"root\\\\\\":{\\\\\\"children\\\\\\":[{\\\\\\"children\\\\\\":[{\\\\\\"detail\\\\\\":0,\\\\\\"format\\\\\\":0,\\\\\\"mode\\\\\\":\\\\\\"normal\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\",\\\\\\"text\\\\\\":\\\\\\"撒打算大时\\\\\\",\\\\\\"type\\\\\\":\\\\\\"text\\\\\\",\\\\\\"version\\\\\\":1}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"base-paragraph\\\\\\",\\\\\\"version\\\\\\":1,\\\\\\"$\\\\\\":{\\\\\\"__anchor__\\\\\\":\\\\\\"GHSt4cJFXxhv6QjYZhCZR\\\\\\"},\\\\\\"textFormat\\\\\\":0,\\\\\\"textStyle\\\\\\":\\\\\\"\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\"}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"root\\\\\\",\\\\\\"version\\\\\\":1}}\\",\\"material\\":\\"{\\\\\\"root\\\\\\":{\\\\\\"children\\\\\\":[{\\\\\\"children\\\\\\":[{\\\\\\"detail\\\\\\":0,\\\\\\"format\\\\\\":0,\\\\\\"mode\\\\\\":\\\\\\"normal\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\",\\\\\\"text\\\\\\":\\\\\\"啊啊撒打算大\\\\\\",\\\\\\"type\\\\\\":\\\\\\"text\\\\\\",\\\\\\"version\\\\\\":1}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"heading\\\\\\",\\\\\\"version\\\\\\":1,\\\\\\"$\\\\\\":{\\\\\\"__anchor__\\\\\\":\\\\\\"Y3izCVMpWQeCrdtSDqgGT\\\\\\"},\\\\\\"tag\\\\\\":\\\\\\"h1\\\\\\"},{\\\\\\"children\\\\\\":[],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"base-paragraph\\\\\\",\\\\\\"version\\\\\\":1,\\\\\\"$\\\\\\":{\\\\\\"__anchor__\\\\\\":\\\\\\"mcg596RMFKlWJzMFGGi_b\\\\\\"},\\\\\\"textFormat\\\\\\":0,\\\\\\"textStyle\\\\\\":\\\\\\"\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\"}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"root\\\\\\",\\\\\\"version\\\\\\":1}}\\",\\"groupType\\":\\"fill_blanks_group\\",\\"list\\":[{\\"id\\":\\"a2bc4bb6-ac5f-40a9-86b4-2dc489566e34\\",\\"questionType\\":\\"fill_blanks\\",\\"quesText\\":\\"{\\\\\\"root\\\\\\":{\\\\\\"children\\\\\\":[{\\\\\\"children\\\\\\":[{\\\\\\"detail\\\\\\":0,\\\\\\"format\\\\\\":0,\\\\\\"mode\\\\\\":\\\\\\"normal\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\",\\\\\\"text\\\\\\":\\\\\\"不费吹灰之力，便可拥有无数大军，且就算迷惑的寻常百姓，通过四灵血阵也能激发他们十倍的生命潜能，这般算去，竟当真是找不到任何可以破解对付魔教鬼王的办法了。一个疯了的寻常百姓，青云门中任何一个人都可以不放在眼里，但一百个一千个呢，更何况眼下足足有十万之众满山遍野如一群疯狂了的蚂蚁般冲了上来，直令人心\\\\\\",\\\\\\"type\\\\\\":\\\\\\"text\\\\\\",\\\\\\"version\\\\\\":1},{\\\\\\"type\\\\\\":\\\\\\"insert-dig-empty\\\\\\",\\\\\\"version\\\\\\":1,\\\\\\"id\\\\\\":\\\\\\"b4ac0770-a0ec-43b2-896b-154afba109e0\\\\\\",\\\\\\"value\\\\\\":\\\\\\"底发\\\\\\",\\\\\\"config\\\\\\":{\\\\\\"isShowValue\\\\\\":false},\\\\\\"index\\\\\\":1},{\\\\\\"detail\\\\\\":0,\\\\\\"format\\\\\\":0,\\\\\\"mode\\\\\\":\\\\\\"normal\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\",\\\\\\"text\\\\\\":\\\\\\"寒。\\\\\\",\\\\\\"type\\\\\\":\\\\\\"text\\\\\\",\\\\\\"version\\\\\\":1}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"heading\\\\\\",\\\\\\"version\\\\\\":1,\\\\\\"$\\\\\\":{\\\\\\"__anchor__\\\\\\":\\\\\\"QCRN3dnTgNgxFsmhbem01\\\\\\"},\\\\\\"tag\\\\\\":\\\\\\"h2\\\\\\"},{\\\\\\"children\\\\\\":[],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"base-paragraph\\\\\\",\\\\\\"version\\\\\\":1,\\\\\\"$\\\\\\":{\\\\\\"__anchor__\\\\\\":\\\\\\"kBTl7_hjxYw_6pT6u8WGh\\\\\\"},\\\\\\"textFormat\\\\\\":0,\\\\\\"textStyle\\\\\\":\\\\\\"\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\"}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"root\\\\\\",\\\\\\"version\\\\\\":1}}\\",\\"analysis\\":\\"{\\\\\\"root\\\\\\":{\\\\\\"children\\\\\\":[{\\\\\\"children\\\\\\":[{\\\\\\"detail\\\\\\":0,\\\\\\"format\\\\\\":0,\\\\\\"mode\\\\\\":\\\\\\"normal\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\",\\\\\\"text\\\\\\":\\\\\\"撒打算大时代\\\\\\",\\\\\\"type\\\\\\":\\\\\\"text\\\\\\",\\\\\\"version\\\\\\":1}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"base-paragraph\\\\\\",\\\\\\"version\\\\\\":1,\\\\\\"$\\\\\\":{\\\\\\"__anchor__\\\\\\":\\\\\\"pozBqgzNUj_xV8JhoizQf\\\\\\"},\\\\\\"textFormat\\\\\\":0,\\\\\\"textStyle\\\\\\":\\\\\\"\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\"}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"root\\\\\\",\\\\\\"version\\\\\\":1}}\\",\\"knowledgePointIdList\\":[],\\"setting\\":{\\"isSetting\\":false,\\"answerType\\":\\"\\",\\"isJudgment\\":true,\\"isScoring\\":true,\\"answerTimeList\\":[],\\"totalAnswerTime\\":0,\\"totalScore\\":\\"\\",\\"scoreList\\":[],\\"pcLayoutType\\":\\"fall\\",\\"appLayoutType\\":\\"fall\\",\\"audioSetting\\":{\\"playType\\":\\"click\\",\\"subtitle\\":\\"preAnswer\\",\\"setPlayNum\\":\\"limit\\",\\"playNum\\":1},\\"videoSetting\\":{\\"playType\\":\\"click\\",\\"subtitle\\":\\"preAnswer\\",\\"setPlayNum\\":\\"limit\\",\\"playNum\\":1},\\"isConnect\\":false,\\"connectQuestion\\":\\"writing\\",\\"connectType\\":\\"scaleAndWriting\\"},\\"children\\":[{\\"childId\\":\\"b4ac0770-a0ec-43b2-896b-154afba109e0\\",\\"answers\\":[{\\"answerId\\":\\"db2b04be-5fd0-49d9-af2a-4d038eb72cd5\\",\\"answerValue\\":\\"底发\\"}],\\"score\\":1}]}],\\"analysis\\":\\"{\\\\\\"root\\\\\\":{\\\\\\"children\\\\\\":[{\\\\\\"children\\\\\\":[{\\\\\\"detail\\\\\\":0,\\\\\\"format\\\\\\":0,\\\\\\"mode\\\\\\":\\\\\\"normal\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\",\\\\\\"text\\\\\\":\\\\\\"啊打算大\\\\\\",\\\\\\"type\\\\\\":\\\\\\"text\\\\\\",\\\\\\"version\\\\\\":1}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"base-paragraph\\\\\\",\\\\\\"version\\\\\\":1,\\\\\\"$\\\\\\":{\\\\\\"__anchor__\\\\\\":\\\\\\"Xc7SeLIsXZYlIZ25CPBh2\\\\\\"},\\\\\\"textFormat\\\\\\":0,\\\\\\"textStyle\\\\\\":\\\\\\"\\\\\\",\\\\\\"style\\\\\\":\\\\\\"\\\\\\"}],\\\\\\"direction\\\\\\":\\\\\\"ltr\\\\\\",\\\\\\"format\\\\\\":\\\\\\"\\\\\\",\\\\\\"indent\\\\\\":0,\\\\\\"type\\\\\\":\\\\\\"root\\\\\\",\\\\\\"version\\\\\\":1}}\\",\\"groupDifficulty\\":null,\\"groupKnowledgePointList\\":[],\\"groupSetting\\":{\\"isSetting\\":false,\\"answerType\\":\\"\\",\\"isJudgment\\":true,\\"isScoring\\":true,\\"answerTimeList\\":[],\\"totalAnswerTime\\":0,\\"totalScore\\":\\"\\",\\"scoreList\\":[],\\"pcLayoutType\\":\\"fall\\",\\"appLayoutType\\":\\"fall\\",\\"audioSetting\\":{\\"playType\\":\\"click\\",\\"subtitle\\":\\"preAnswer\\",\\"setPlayNum\\":\\"limit\\",\\"playNum\\":1},\\"videoSetting\\":{\\"playType\\":\\"click\\",\\"subtitle\\":\\"preAnswer\\",\\"setPlayNum\\":\\"limit\\",\\"playNum\\":1},\\"isConnect\\":false,\\"connectQuestion\\":\\"writing\\",\\"connectType\\":\\"scaleAndWriting\\"}}"},{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"在那无数疯狂的人群中，还有为数不少的人在天上血芒的照耀下显得特别活跃，他们的道行显得远远超过了周围那些如蝼蚁一般的百姓，飞腾驭剑，修真道士能做的他们都会，且道行更是极高，有许多已胜过了守卫的青云门弟子。这些人自然便是之前数场战役之中，被鬼王夺去心志的正道修真了。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"listitem","version":1,"value":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"有个这许多高手助战，加上几乎无穷无尽的疯狂人潮，魔教的攻势犹如巨涛拍岸，势不可当，一片红芒照耀之下，防守在云海上的青云门弟子几乎没有像样的抵抗就已经败退下来，纷纷退上了虹桥。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"listitem","version":1,"value":2}],"direction":"ltr","format":"","indent":0,"type":"list","version":1,"listType":"number","start":1,"tag":"ol"},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"5iE60_DlvRLDhEd4WbhEn"},"textFormat":0,"textStyle":"","style":""},{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"没有多久，“嗖嗖”之声不绝于耳，从四面八方涌来的魔教大军已然将空旷巨大的云海平台占据了，放眼望去原本云气缥缈的仙境如今人头攒动，狂吼嘶喊之声此起彼伏，简直如恶鬼地狱一般，到了后来，更多的人纷纷挤上了此处，简直已经没有插脚的地方了。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"listitem","version":1,"value":1},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"而天幕之上，一团比天空红影更深邃百倍，看去犹如一个血球的大红光团缓缓飞到了云海平台的上空，从里面传出了一阵狂笑之声：","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"listitem","version":1,"value":2},{"children":[{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"“哈哈哈哈，青云门的废物们，如今终于知道老夫的厉害了吧！哈哈哈哈……道玄呢，道玄你这个狗才为何还不出来，你不是向来要拯救天下苍生么，诛仙剑阵不是天下无敌嘛，怎么如今却当了缩头乌龟，不敢出来了？哈哈哈哈哈……”","type":"text","version":1}],"direction":"ltr","format":"","indent":1,"type":"listitem","version":1,"value":1}],"direction":"ltr","format":"","indent":0,"type":"list","version":1,"listType":"bullet","start":1,"tag":"ul"}],"direction":"ltr","format":"","indent":0,"type":"listitem","version":1,"value":3}],"direction":"ltr","format":"","indent":0,"type":"list","version":1,"listType":"bullet","start":1,"tag":"ul"},{"children":[],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"o5CQI-TgAKMERT7N6q52b"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"标题：笑声放肆而猖狂，几乎有些歇斯底里，然而其中暴戾之气，却令整座青云山通天峰上，笼罩在了一片绝望的气氛中。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"heading","version":1,"$":{"__anchor__":"UWOjE7ZbtlLzn94Ov_CtO"},"tag":"h2"},{"children":[{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"不过也就是在这个时候，魔教大军的攻势却暂时停滞了下来，原因无他，只是通天峰上正道诸人凭借了地利，死死守住了虹桥。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"WsWpz2QX6NwhpS4WSkif8"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"cZh7fvy6yPl92w4u4EkSA"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"OyhbSE1gcQWJSxa9542FX"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"284m4lJxVoX7BfpTljuMd"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"虹桥乃是天造地设的奇景","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"oGK30yqGiejV8fnJTJ0TE"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"tntcDKFZjA35pyMbGJtBh"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"如今却成了魔教大军难以逾越的天堑奇险，偌大的桥面平时还算开阔，但此刻对于十万魔教大军来说，简直与独木桥无异。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"XbEelyBopgSFiYgpLlTsF"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"column-item","version":1,"width":50},{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"那些疯狂而丧失理智的人纷纷冲上了桥面，但片刻之后就只听“啊啊啊”尖叫之声不时响起，却是有人收脚不住，又或是太过拥挤，生生被推下了虹桥之下的无底深渊，快速化作一个个黑点，被深深的云海所吞没。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"KWawoeqBP2ZR1DfynqDsq"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"column-item","version":1,"width":50}],"direction":"ltr","format":"","indent":0,"type":"column-container","version":1},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"11MUjvps3iZfxPfxuKQAP"},"textFormat":0,"textStyle":"","style":""},{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"而正道这里，原本是被魔教大军突袭打了个措手不及，加上的确实力差距太大，所以在云海之上才转眼崩溃，但此刻原先聚集在玉清殿上的精英纷纷加入站团，战力大盛，只看着虹桥这里半空中纵横挥舞的法宝毫光，已然强过了刚才不知多少倍。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"0DFBzTB9uln-gyY4KNfAK"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"TsusGHQzmvvaqt5-KHIia"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"标题：魔教人数虽多，但能正面打斗的只有数十人而已，","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"heading","version":1,"$":{"__anchor__":"VxnG-4t7n7ZGm1x3pdwY0"},"tag":"h2"},{"children":[],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"FJFIUOWqF7KitzwtomJJP"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"而绝大多数魔教大军都是鬼王利用四灵血阵的妖力蛊惑心志而来的，虽然四灵血阵可以激发他们潜力，变得力大无穷，攀爬如飞，但终究不能令他们一日千里就瞬间学会各种仙家术法驭剑飞行，是以魔教声势虽盛，大多半人却只能傻傻站在地面向前冲去，碰到通天峰上虹桥这等天堑，便只有徒呼奈何了！","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"7MLSnPA47VmLNIuguPYah"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"highlight-block","version":1,"style":""},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"collapsible-head","version":1},{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"虽然人群之中，还有不少被夺去心智的修真之士","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"nz2PX8unDFw1Ef7uEtnPV"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"O6N3wP0peDRogdvXyWASa"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"也能驭剑飞起在半空相搏。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"eFiCjC93OLXsG-utmPTB7"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"Aw59eu1B-cXVErJceqS5s"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"但终究只是少数，正道这里一面加派人手死死守住虹桥桥头，一面分派高手对付那少数飞跃而来的魔教高手，以多打少，都是转眼间就压制了下去。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"l1iYX5mOywn9oB5CIfE-0"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"collapsible-content","version":1}],"direction":"ltr","format":"","indent":0,"type":"collapsible-block","version":1,"style":""},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"GMUngk1RSIm_NuZfTXPNl"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"0wkbT41eWm0r-8qYFcAUY"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"PHgVI0AW-L1BIqqdBUtUQ"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"yxLQ9NsdAfGjfKaniVetf"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"h7wHMWpDhTzXJ4ReV2yDr"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"N4SteZ4_kbK8i-RbaR2jM"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"h4Bm1ibgz7-8SKkBCWYaN"},"textFormat":0,"textStyle":"","style":""},{"children":[{"children":[{"children":[{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"uBUzZvdhPxvOH2EL1gjWu"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":3,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"wQuk3rO-0TFR_TEtrdyxe"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":1,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"hFklQOB064Dbtud-i3g57"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":1,"rowSpan":1}],"direction":"ltr","format":"","indent":0,"type":"tablerow","version":1},{"children":[{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"rc6xbJvpaLVJ4qpiit9gT"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":2,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"gmqOr6us0mZj33mCweuBW"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"hIwrjKm9EKXtPF_iC-Zl3"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1}],"direction":"ltr","format":"","indent":0,"type":"tablerow","version":1}],"direction":"ltr","format":"","indent":0,"type":"table","version":1,"colWidths":[92,92,92]}],"direction":"ltr","format":"","indent":0,"type":"table-container","version":1},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"v200Uk1d8cO4NPqoeovks"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"如此这般，正道这方居然慢慢稳住阵脚，将局势扳了回来。反观魔教那边，无数眼冒红光疯狂的人张牙舞爪，却只能拥挤在小小但漫长的虹桥之上，进不能进，退不能退，时间稍久。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"i8ejYXy9qeEtwy81qHL55"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"骚动越来越厉害，竟有越来越多的人落下了虹桥，就次丧命，看那纷纷落下的黑影，竟似乎比正道中人手下杀死的人数还要多上许多。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"1yLhn6ljKCfoZA0mqS3Z0"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"e6-55v1m41_buolVp_13F"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"标题：这番情景自是大出魔教这方的意料之外，而正道则是士气大盛，虽然此刻局势仍是不容乐观，但终究比刚才那突然开战时的兵败如山倒要好得多了。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"heading","version":1,"$":{"__anchor__":"AERY9B8FFJJVi3w9reHuP"},"tag":"h1"},{"children":[],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"xl8NZ5l51QirAW_KbiSnu"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"hIjjMBUaDsaqn6WOlRvqr"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"只是，这些许的希望并没有在正道中人的心里存在多久，在魔教大军被阻挡在虹桥一端之后，天际苍穹中那诡异的巨大血球就缓缓越过云海平台，飞到了虹桥上方。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"zQyfLvQ4-IPLa803-zWBq"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"zTUDuFWGwt7QAUg9nPrMP"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"标题：赤红的血芒吞吐伸展着，在半空中像是一个张牙舞爪的可怕恶魔。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"heading","version":1,"$":{"__anchor__":"y4ChE8o9FS9wtpw7Qb00E"},"tag":"h3"},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"翻滚升腾的血气都在急速旋转着，片刻之后，从巨大的血球之中，突然向着通天峰上虹桥一端的正道人群中，射下十几道血色的光柱。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"q5sxDFYL7oPbAEuy2e_zY"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"In4dDXrq8BVtvOiip3zet"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"天音寺普泓大师等人之前已然与魔教交过手，是以看到那巨大血球飞上来的时候面色便已凝重，此番看到那奇异光柱照下，普泓大师的脸色更是大变，疾声大喊道：“快闪开，那光柱正是妖人蛊惑心智的东西。”","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"xinON3MZoXC_r5KQLQffZ"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"IHjlkaU98---ULjJF-QqG"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"NPJEzQ1yFfR-1lEzOTTSh"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"auKFmPA1gTuMhtHhixYMz"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"标题：众人听了都是纷纷变色","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"heading","version":1,"$":{"__anchor__":"m5EOXqrCAoJFn4xX9qEGl"},"tag":"h1"},{"children":[],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"2edpuCuAoV7kPLjVD49L7"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"走避不迭，但正道中人密集守在虹桥桥头抵御魔教攻势，天上光柱射下的速度又快，一时间哪里能够完全闪避。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"DtKA2mt9828zsFn1Kav_p"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"DppnWm8Qy9sU3DRoZIgp0"},"textFormat":0,"textStyle":"","style":""},{"children":[{"children":[{"children":[{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"c00","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"XnB9mswU_rLKzkaaZMUnH"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":3,"rowSpan":1},{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"c10","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"0j8kJKmOjPP33Op3Wfbgs"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":1,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"DQaWAVHcDCRwLAhAhwVSk"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":1,"rowSpan":1}],"direction":"ltr","format":"","indent":0,"type":"tablerow","version":1,"height":33},{"children":[{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"c01","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"dMBQ5V-nE50o6ACQzBqkH"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":2,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"lzbjyVJB_gW2JDlzcnTLd"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"-QnOoAslJj-0Fwp8T8V0k"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1}],"direction":"ltr","format":"","indent":0,"type":"tablerow","version":1},{"children":[{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"548qEvdRRsIP3U-_SiALm"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":2,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"QDH5DFeShTSPQemr8OIm7"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1},{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"7bbZRALsPeSZs5r68nowy"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"tablecell","version":1,"backgroundColor":null,"colSpan":1,"headerState":0,"rowSpan":1}],"direction":"ltr","format":"","indent":0,"type":"tablerow","version":1}],"direction":"ltr","format":"","indent":0,"type":"table","version":1,"colWidths":[92,92,92]}],"direction":"ltr","format":"","indent":0,"type":"table-container","version":1},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"EpgCsscV--qekpCktw3-i"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"advanced-layout","version":1,"style":"background-image: url(https://ipublish-test-1313083974.cos.ap-beijing.myqcloud.com/m9139bv7_i8brs9ho); background-size: contain; background-repeat: no-repeat; background-position: center"},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"1Wd-6wJKLfROeIl6Fw91O"},"textFormat":0,"textStyle":"","style":""},{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"只是几声惨叫发出，却是已然有几位正道弟子走避不及，被血色光柱罩在其中，顿时只见那数人身躯大震，随后面目扭曲，动作变得缓慢僵硬，双眼之中慢慢发出红色的光芒来。","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"quote","version":1},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"q7YSqyJqwvX8kee52bT8i"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"Rjk4GFruBr1KiKXESmnI0"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"25BOCXhlN9OYlC-c2jZke"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"iLJdMdh-FGDdAvz1QZt6o"},"textFormat":0,"textStyle":"","style":""},{"children":[],"direction":null,"format":"","indent":0,"type":"base-paragraph","version":1,"$":{"__anchor__":"UWHZMsERSmOs-_0ZsoIwP"},"textFormat":0,"textStyle":"","style":""}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}
                """;
        Date start = new Date();
        List<ChapterNode> nodes = parseLexicalJson(jsonString);
        List<TreeNode> tree = ChapterTreeBuilder.buildChapterTree(nodes);
        Date end = new Date();
        log.info("耗时：{}ms", end.getTime() - start.getTime());
        for (ChapterNode node : nodes) {
            log.info(String.valueOf(node));
        }
        // 打印树结构
        printTree(tree, "");

        // 提取仅包含目录节点的树
        List<TreeNode> tocTree = extractTocTree(tree);

        log.info("\n仅目录树结构：");
        printTree(tocTree, "");

    }

    public static class TreeNode {
        ChapterNode data;
        List<TreeNode> children;

        public TreeNode(ChapterNode data) {
            this.data = data;
            this.children = new ArrayList<>();
        }

        // 深拷贝构造函数，用于创建节点副本
        public TreeNode(TreeNode original) {
            this.data = original.data;
            this.children = new ArrayList<>();
        }
    }

    public static class ChapterTreeBuilder {
        private ChapterTreeBuilder(){
            super();
        }

        // 构建层级目录树的主方法
        public static List<TreeNode> buildChapterTree(List<ChapterNode> nodes) {
            List<TreeNode> result = new ArrayList<>();
            if (nodes == null || nodes.isEmpty()) {
                return result;
            }

            // 用于存储当前路径上的标题节点
            Stack<TreeNode> headingStack = new Stack<>();
            // 记录已经见过的标题类型及其层级
            Map<String, Integer> headingLevels = new HashMap<>();
            int currentLevel = 0;

            for (ChapterNode node : nodes) {
                TreeNode treeNode = new TreeNode(node);

                if (isHeading(node.type)) {
                    // 为这个标题类型分配一个层级（如果之前没见过）
                    if (!headingLevels.containsKey(node.type)) {
                        headingLevels.put(node.type, ++currentLevel);
                    }

                    int nodeLevel = headingLevels.get(node.type);

                    // 弹出所有层级大于或等于当前节点层级的标题
                    while (!headingStack.isEmpty() && headingLevels.get(headingStack.peek().data.type) >= nodeLevel) {
                        headingStack.pop();
                    }

                    if (headingStack.isEmpty()) {
                        // 如果栈为空，这是一个顶级节点
                        result.add(treeNode);
                    } else {
                        // 否则，将节点添加为栈顶节点的子节点
                        headingStack.peek().children.add(treeNode);
                    }

                    // 将当前标题节点入栈
                    headingStack.push(treeNode);
                } else {
                    // 对于非标题节点，将其添加为最近一个标题节点的子节点
                    if (!headingStack.isEmpty()) {
                        headingStack.peek().children.add(treeNode);
                    } else {
                        // 如果没有标题节点，则作为顶级节点添加
                        result.add(treeNode);
                    }
                }
            }
            return result;
        }

        /**
         * 提取仅包含目录节点的树结构
         * 性能优化：使用迭代而非递归，避免不必要的对象创建
         *
         * @param fullTree 包含所有节点的完整树结构
         * @return 仅包含目录节点的树结构
         */
        public static List<TreeNode> extractTocTree(List<TreeNode> fullTree) {
            if (fullTree == null || fullTree.isEmpty()) {
                return new ArrayList<>();
            }

            List<TreeNode> tocTree = new ArrayList<>();

            for (TreeNode node : fullTree) {
                if (isHeading(node.data.type)) {
                    // 创建新的目录节点
                    TreeNode tocNode = new TreeNode(node);

                    // 迭代处理子节点，仅保留标题节点
                    List<TreeNode> tocChildren = extractHeadingNodesOnly(node.children);
                    tocNode.children.addAll(tocChildren);

                    tocTree.add(tocNode);
                }
            }

            return tocTree;
        }

        /**
         * 从节点列表中提取仅包含标题类型的节点
         * 使用迭代而非递归，提高性能
         */
        private static List<TreeNode> extractHeadingNodesOnly(List<TreeNode> nodes) {
            List<TreeNode> headingNodes = new ArrayList<>();

            // 使用队列进行广度优先遍历，避免递归调用开销
            Queue<TreeNode> queue = new LinkedList<>(nodes);

            while (!queue.isEmpty()) {
                TreeNode current = queue.poll();

                if (isHeading(current.data.type)) {
                    // 创建新节点，仅包含标题信息
                    TreeNode headingNode = new TreeNode(current);
                    headingNodes.add(headingNode);

                    // 将当前标题节点的子节点加入队列，保持原始顺序
                    for (TreeNode child : current.children) {
                        if (isHeading(child.data.type)) {
                            headingNode.children.add(new TreeNode(child));
                        } else {
                            queue.add(child);
                        }
                    }
                } else {
                    // 非标题节点，将其所有子节点加入队列继续处理
                    queue.addAll(current.children);
                }
            }

            return headingNodes;
        }

        // 判断节点是否为标题节点
        private static boolean isHeading(String type) {
            return type != null && type.matches("h[1-6]");
        }

        // 用于打印树结构的辅助方法（测试用）
        public static void printTree(List<TreeNode> tree, String indent) {
            for (TreeNode node : tree) {
                log.info("{}- {} ({})", indent, node.data.type, node.data.text);
                printTree(node.children, indent + "  ");
            }
        }

    }

}