package com.unipus.digitalbook.model.entity.template;

import lombok.Data;

import java.util.Date;

/**
 * 教材关联模板信息
 */
@Data
public class PaperScoreTemplateRelation {

    /**
     * 教材关联模板模板Id
     */
    private Long id;

    /**
     * 教材Id
     */
    private String bookId;

    /**
     * 模板类型
     */
    private Integer paperScoreTemplateType;

    /**
     * 模板Id
     */
    private Long paperScoreTemplateId;

    /**
     * 创建用户Id
     */
    private Long createUserId;

    /**
     * 更新时间
     */
    private Date updateTime;
}
