package com.unipus.digitalbook.model.entity.question;

import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户作答节点
 */
public class UserAnswerNode {
    /**
     * 题id
     */
    private String bizQuestionId;

    /**
     * 题版本号
     */
    private String versionNumber;

    /**
     * 题目总分
     */
    private BigDecimal questionScore;

    /**
     * 题的类型 @see com.unipus.digitalbook.model.enums.QuestionTypeEnum
     */
    private Integer questionType;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 业务答案id，比如评测id
     */
    private String bizAnswerId;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 答案
     */
    private String answer;

    /**
     * 评语
     */
    private String evaluation;

    /**
     * 作答结果状态
     */
    private Integer status;

    /**
     * 选项列表
     */
    private List<ChoiceQuestionOption> options;

    private List<QuestionAnswer> correctAnswers;
    /**
     * 答案列表
     */
    private List<UserAnswerNode> userAnswers;

    /**
     * 选择角色列表
     */
    private List<String> selectRoleList;

    /**
     * 角色
     */
    private String role;

    public UserAnswerNode toUserAnswerNode(BigQuestionGroup question, List<UserAnswer> userAnswers) {
        UserAnswerNode answerNode = new UserAnswerNode();
        answerNode.setBizQuestionId(question.getBizGroupId());
        answerNode.setQuestionType(question.getType());
        answerNode.setVersionNumber(question.getVersionNumber());
        answerNode.setQuestionScore(question.getScore());
        Map<String, UserAnswer> userQuestionAnswersMap = userAnswers == null ? Collections.emptyMap() : userAnswers.stream().collect(Collectors.toMap(UserAnswer::getBizQuestionId, userAnswer -> userAnswer, (v1, v2) -> v1));
        List<UserAnswerNode> answerNodes = toAnswerNodes(null, question.getQuestions(), userQuestionAnswersMap);
        answerNode.setUserAnswers(answerNodes);
        answerNode.setScore(answerNodes.stream().map(UserAnswerNode::getScore).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        return answerNode;
    }

    private List<UserAnswerNode> toAnswerNodes(Question parentQuestion, List<Question> questions, Map<String, UserAnswer> userQuestionAnswersMap) {
        return questions.stream().map(q -> {
            if (CollectionUtils.isEmpty(q.getQuestions())) {
                UserAnswer userAnswer = userQuestionAnswersMap.get(q.getBizQuestionId());
                return getUserAnswerNode(parentQuestion, q, userAnswer);
            } else {
                List<UserAnswerNode> answerNodes = toAnswerNodes(q, q.getQuestions(), userQuestionAnswersMap);
                List<String> roles = answerNodes.stream().filter(u -> u.getRole() != null && u.getStatus() != null && u.getStatus() > 0).map(UserAnswerNode::getRole)
                        .distinct().toList();
                UserAnswerNode userAnswerNode = getUserAnswerNodeWithoutQuestion(parentQuestion, q);
                userAnswerNode.setSelectRoleList(roles);
                userAnswerNode.setUserAnswers(answerNodes);
                userAnswerNode.setScore(answerNodes.stream().map(UserAnswerNode::getScore).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                return userAnswerNode;
            }
        }).toList();
    }


    private UserAnswerNode getUserAnswerNodeWithoutQuestion(Question parentQuestion, Question q) {
        UserAnswerNode userAnswerNode = new UserAnswerNode();
        userAnswerNode.setQuestionScore(q.getScore());
        userAnswerNode.setCorrectAnswers(q.getAnswers());
        userAnswerNode.setQuestionType(q.getQuestionType());
        userAnswerNode.setVersionNumber(q.getVersionNumber());
        userAnswerNode.setBizQuestionId(q.getBizQuestionId());
        QuestionText questionText = q.getQuestionText();
        if(questionText != null && questionText.getOptions() != null) {
            userAnswerNode.setOptions(questionText.getOptions());
        } else {
            userAnswerNode.setOptions(q.getOptions());
        }

        boolean pass = parentQuestion != null && parentQuestion.getQuestionText() != null;
        pass = pass && (questionText != null && questionText.getRole() != null
                && parentQuestion.getQuestionText().getKeywords() != null);
        if (pass) {
            parentQuestion.getQuestionText().getKeywords()
                    .stream()
                    .filter(k -> k.getId().equals(questionText.getRole()))
                    .findAny()
                    .ifPresent(k -> userAnswerNode.setRole(k.getText()));
        }

        return userAnswerNode;
    }
    private UserAnswerNode getUserAnswerNode(Question parentQuestion, Question q, UserAnswer userAnswer) {
        UserAnswerNode userAnswerNode = getUserAnswerNodeWithoutQuestion(parentQuestion, q);
        if (userAnswer != null) {
            userAnswerNode.setBizQuestionId(userAnswer.getBizQuestionId());
            userAnswerNode.setScore(userAnswer.getScore());
            userAnswerNode.setAnswer(userAnswer.getAnswer());
            userAnswerNode.setEvaluation(userAnswer.getEvaluation());
            userAnswerNode.setStatus(userAnswer.getStatus());
            userAnswerNode.setBizAnswerId(userAnswer.getBizAnswerId());
            userAnswerNode.setBatchId(userAnswer.getBatchId());
        }
        return userAnswerNode;
    }


    public String getBizQuestionId() {
        return bizQuestionId;
    }

    public void setBizQuestionId(String bizQuestionId) {
        this.bizQuestionId = bizQuestionId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    public BigDecimal getQuestionScore() {
        return questionScore;
    }

    public void setQuestionScore(BigDecimal questionScore) {
        this.questionScore = questionScore;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public List<UserAnswerNode> getUserAnswers() {
        return userAnswers;
    }

    public void setUserAnswers(List<UserAnswerNode> userAnswers) {
        this.userAnswers = userAnswers;
    }

    public String getBizAnswerId() {
        return bizAnswerId;
    }

    public void setBizAnswerId(String bizAnswerId) {
        this.bizAnswerId = bizAnswerId;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getEvaluation() {
        return evaluation;
    }

    public void setEvaluation(String evaluation) {
        this.evaluation = evaluation;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<ChoiceQuestionOption> getOptions() {
        return options;
    }

    public void setOptions(List<ChoiceQuestionOption> options) {
        this.options = options;
    }

    public List<QuestionAnswer> getCorrectAnswers() {
        return correctAnswers;
    }

    public void setCorrectAnswers(List<QuestionAnswer> correctAnswers) {
        this.correctAnswers = correctAnswers;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public List<String> getSelectRoleList() {
        return selectRoleList;
    }

    public void setSelectRoleList(List<String> selectRoleList) {
        this.selectRoleList = selectRoleList;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }
}
