package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Schema(description = "用户试卷基础信息")
@Data
public class UserPaperSyncInfo {

    @Schema(description = "用户ID")
    private String openId;
    @Schema(description = "数据包")
    private String dataPackage;
    @Schema(description = "客户端IP")
    private String clientIp;

    @Schema(description = "教材ID")
    private String bookId;
    @Schema(description = "教材版本号")
    private String bookVersionNumber;
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷版本号")
    private String paperVersionNumber;
    @Schema(description = "试卷实例ID")
    private String instanceId;
    @Schema(description = "试卷类型,1:常规卷/2:挑战卷/3:诊断卷")
    private Integer paperType;
    @Schema(description = "试卷名称")
    private String paperName;

    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "用户得分")
    private BigDecimal userScore;
    @Schema(description = "试卷总分")
    private BigDecimal totalScore;
    @Schema(description = "题目列表")
    private List<BigQuestionGroup> questionGroups;
    @Schema(description = "用户答案列表")
    private Map<String, List<UserAnswer>> userAnswersMap;

    // 提交用户试卷作答成绩用
    public UserPaperSyncInfo(UserPaperSubmitExtInfo ext) {

        this.openId = ext.getUserAccessInfo().getOpenId();
        this.dataPackage = ext.getUserAccessInfo().getDataPackage();
        this.clientIp = ext.getUserAccessInfo().getClientIp();

        this.bookId = ext.getBookId();
        this.bookVersionNumber = ext.getBookVersionNumber();
        this.paperId = ext.getPaperId();
        this.paperVersionNumber = ext.getPaperVersionNumber();
        this.instanceId = ext.getInstanceId();
        this.paperType = ext.getPaperType();
        this.paperName = ext.getPaperName();

        this.questionGroups =  ext.getBigQuestionGroups();
        this.questionCount = ext.getQuestionCount();
        this.userAnswersMap = ext.getUserAnswerMap();
        this.totalScore = ext.getTotalScoreFromQuestions();
        this.userScore = ext.getUserScoreFromJudgedUserAnswer();
    }
}
