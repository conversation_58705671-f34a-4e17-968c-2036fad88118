package com.unipus.digitalbook.model.entity.template;

import com.unipus.digitalbook.model.po.template.PaperScoreTemplatePO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 模板信息
 */
@Data
public class PaperScoreTemplateList {

    /**
     * 模板信息列表
     */
    private List<PaperScoreTemplate> templateList;

    /**
     * 总数量
     */
    private Integer total;

    public static PaperScoreTemplateList assemblyPaperScoreTemplateList(List<PaperScoreTemplatePO> list, Integer total) {
        PaperScoreTemplateList paperScoreTemplateList = new PaperScoreTemplateList();
        if (CollectionUtils.isNotEmpty(list)) {
            paperScoreTemplateList.setTemplateList(list.stream().map(PaperScoreTemplatePO::toEntity).toList());
        }
        paperScoreTemplateList.setTotal(total);
        return paperScoreTemplateList;
    }
}
