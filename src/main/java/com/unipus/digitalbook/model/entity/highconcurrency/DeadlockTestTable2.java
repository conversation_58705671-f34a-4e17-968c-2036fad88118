package com.unipus.digitalbook.model.entity.highconcurrency;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 死锁测试表2实体
 * 用于模拟死锁场景
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("deadlock_test_table2")
public class DeadlockTestTable2 {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联ID
     */
    @TableField("ref_id")
    private Long refId;
    
    /**
     * 数据值
     */
    @TableField("data_value")
    private String dataValue;
    
    /**
     * 更新次数
     */
    @TableField("update_count")
    private Integer updateCount;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}