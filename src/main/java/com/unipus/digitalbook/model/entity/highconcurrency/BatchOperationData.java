package com.unipus.digitalbook.model.entity.highconcurrency;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 批量数据操作实体
 * 用于演示高并发批量操作
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("batch_operation_data")
public class BatchOperationData {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 业务数据
     */
    @TableField("business_data")
    private String businessData;
    
    /**
     * 数据状态：PENDING, PROCESSING, COMPLETED, FAILED
     */
    @TableField("status")
    private String status;
    
    /**
     * 数据类型
     */
    @TableField("data_type")
    private String dataType;
    
    /**
     * 批次号
     */
    @TableField("batch_no")
    private String batchNo;
    
    /**
     * 处理优先级
     */
    @TableField("priority")
    private Integer priority;
    
    /**
     * 扩展字段
     */
    @TableField("extra_data")
    private String extraData;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 创建用户
     */
    @TableField("create_user")
    private Long createUser;
    
    /**
     * 更新用户
     */
    @TableField("update_user")
    private Long updateUser;
    
    /**
     * 版本号（乐观锁）
     */
    @Version
    @TableField("version")
    private Integer version;
}