package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.book.BookNode;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.Objects;

@Schema(description = "试卷管理实体类")
public class Paper extends BookNode {

    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷类型")
    private PaperTypeEnum paperType;
    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "创建人ID")
    private Long creatorId;
    @Schema(description = "创建人名称")
    private String creatorName;
    @Schema(description = "引用标识")
    private Boolean referenceFlag;
    @Schema(description = "上架标识")
    private Boolean publishFlag;
    @Schema(description = "试卷版本信息")
    private PaperVersion paperVersion = new PaperVersion();

    public Paper(){}

    // 构建试卷信息(仅构建试卷基本信息，试卷名称/试卷说明/试卷内容保存到试卷扩展信息中)
    public BigQuestionGroup buildQuestionGroup(Long userId){
        BigQuestionGroup questionGroup = new BigQuestionGroup();
        questionGroup.setBizGroupId(this.getPaperId());
        questionGroup.setParentId(null);
        // 试卷类型(题组类型)
        questionGroup.setType(PaperTypeEnum.getQuestionGroupTypeByCode(this.paperType.getCode()).getCode());
        // 试卷版本号
        questionGroup.setVersionNumber(this.getVersionNumber());
        questionGroup.setCreateBy(userId);
        questionGroup.setUpdateBy(userId);
        questionGroup.setEnable(true);
        return questionGroup;
    }

    public Paper(BigQuestionGroup bigQuestionGroup){
        super.setBookId(null);
        this.paperId = bigQuestionGroup.getBizGroupId();
        this.paperType = PaperTypeEnum.getTypeByGroupCode(bigQuestionGroup.getType());
        this.creatorId = bigQuestionGroup.getCreateBy();
        this.creatorName = null;
        this.createTime = bigQuestionGroup.getCreateTime();
        this.paperVersion = new PaperVersion(bigQuestionGroup);
    }

    public Boolean compareContent(Paper target){
        return Objects.equals(this.getContent(), target.getContent());
    }

    public PaperVersion getPaperVersion() {
        return paperVersion;
    }

    public void setPaperVersion(PaperVersion paperVersion) {
        this.paperVersion = paperVersion;
    }

    public Long getId() {
        return this.getPaperVersion().getId();
    }

    public void setId(Long id) {
        this.getPaperVersion().setId(id);
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
        this.getPaperVersion().setPaperId(paperId);
    }

    public String getPaperName() {
        return this.getPaperVersion().getPaperName();
    }

    public void setPaperName(String paperName) {
        this.getPaperVersion().setPaperName(paperName);
    }

    public PaperTypeEnum getPaperType() {
        return paperType;
    }

    public void setPaperType(PaperTypeEnum paperType) {
        this.paperType = paperType;
    }

    public String getDescription() {
        return this.getPaperVersion().getDescription();
    }

    public void setDescription(String description) {
        this.getPaperVersion().setDescription(description);
    }

    public Integer getQuestionCount() {
        return this.getPaperVersion().getQuestionCount();
    }

    public void setQuestionCount(Integer questionCount) {
        this.getPaperVersion().setQuestionCount(questionCount);
    }

    public Integer getTotalScore() {
        return this.getPaperVersion().getTotalScore();
    }

    public void setTotalScore(Integer totalScore) {
        this.getPaperVersion().setTotalScore(totalScore);
    }

    public String getContent() {
        return this.getPaperVersion().getContent();
    }

    public void setContent(String content) {
        this.getPaperVersion().setContent(content);
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getVersionNumber() {
        return this.getPaperVersion().getVersionNumber();
    }

    public void setVersionNumber(String versionNumber) {
        this.getPaperVersion().setVersionNumber(versionNumber);
    }

    public Boolean getEnable() {
        return this.getPaperVersion().getEnable();
    }

    public void setEnable(Boolean enable) {
        this.getPaperVersion().setEnable(enable);
    }

    public Boolean getReferenceFlag() {
        return referenceFlag;
    }

    public void setReferenceFlag(Boolean referenceFlag) {
        this.referenceFlag = referenceFlag;
    }

    public Boolean getPublishFlag() {
        return publishFlag;
    }

    public void setPublishFlag(Boolean publishFlag) {
        this.publishFlag = publishFlag;
    }
}
