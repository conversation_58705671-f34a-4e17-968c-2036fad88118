package com.unipus.digitalbook.model.entity.publish;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 教材版本实体类
 */
public class BookVersion implements Serializable {
    // 发布版本 ID
    private Long id;
    // 教材 ID
    private String bookId;
    // 版本号
    private String versionNum;
    // 显示版本号
    private String showVersionNumber;
    // 排序
    private Integer sortOrder;
    // 发布状态
    private Integer publishStatus;
    // 创建时间
    private Date createTime;
    // 最后更新时间
    private Date updateTime;
    // 创建者ID
    private Long createBy;
    // 最后更新者ID
    private Long updateBy;
    // 是否有效 0-无效 1-有效
    private Boolean enable;

    // 教材版本章节版本id列表
    private List<Long> chapterVersionIdList;
    public BookVersion() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(String versionNum) {
        this.versionNum = versionNum;
    }

    public Integer getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(Integer publishStatus) {
        this.publishStatus = publishStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getShowVersionNumber() {
        return showVersionNumber;
    }

    public void setShowVersionNumber(String showVersionNumber) {
        this.showVersionNumber = showVersionNumber;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public List<Long> getChapterVersionIdList() {
        return chapterVersionIdList;
    }

    public void setChapterVersionIdList(List<Long> chapterVersionIdList) {
        this.chapterVersionIdList = chapterVersionIdList;
    }
}
