package com.unipus.digitalbook.model.enums;

import io.micrometer.common.lang.Nullable;

import java.util.HashMap;
import java.util.Map;

/**
 * 配套资源后缀枚举类型
 */
public enum ComplementSuffixEnum {
    PDF("pdf", ComplementTypeEnum.DOC),
    PPT("ppt", ComplementTypeEnum.DOC),
    PPTX("pptx", ComplementTypeEnum.DOC),
    DOC("doc", ComplementTypeEnum.DOC),
    DOCX("docx", ComplementTypeEnum.DOC),
    XLS("xls", ComplementTypeEnum.DOC),
    XLSX("xlsx", ComplementTypeEnum.DOC),
    CSV("csv", ComplementTypeEnum.DOC),
    AVI("avi", ComplementTypeEnum.VIDEO),
    WMV("wmv", ComplementTypeEnum.VIDEO),
    MP4("mp4", ComplementTypeEnum.VIDEO),
    MPEG("mpeg", ComplementTypeEnum.VIDEO),
    MKV("mkv", ComplementTypeEnum.VIDEO),
    MP3("mp3", ComplementTypeEnum.AUDIO),
    WAV("wav", ComplementTypeEnum.AUDIO),
    WMA("wma", ComplementTypeEnum.AUDIO),
    JPEG("jpeg", ComplementTypeEnum.IMAGE),
    JPG("jpg", ComplementTypeEnum.IMAGE),
    PNG("png", ComplementTypeEnum.IMAGE),
    GIF("gif", ComplementTypeEnum.IMAGE),
    BMP("bmp", ComplementTypeEnum.IMAGE);

    private final String suffix;
    private final ComplementTypeEnum type;

    private static final Map<String, ComplementSuffixEnum> MAP = new HashMap<>();

    static {
        for (ComplementSuffixEnum e : ComplementSuffixEnum.values()) {
            MAP. put(e.suffix, e);
        }
    }

    ComplementSuffixEnum(String suffix, ComplementTypeEnum type) {
        this.suffix = suffix;
        this.type = type;
    }

    public String getSuffix() {
        return suffix;
    }

    public ComplementTypeEnum getType() {
        return type;
    }

    @Nullable
    public static ComplementSuffixEnum fromSuffix(String suffix) {
       return MAP.get(suffix.toLowerCase());
    }
}
