package com.unipus.digitalbook.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.unipus.digitalbook.common.utils.JsonUtil;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 模板详情评价短语类型
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum PaperScoreTemplateDEPTypeEnum {
    CONGRATULATIONS(1, "恭喜"),
    WELL_DONE(2, "好样的"),
    COME_ON(3, "加油");

    private final Integer code;
    private final String desc;

    PaperScoreTemplateDEPTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static Optional<PaperScoreTemplateDEPTypeEnum> getByCode(Integer code) {
        if (ObjectUtils.isEmpty(code)) {
            return Optional.empty();
        }

        return Arrays.stream(PaperScoreTemplateDEPTypeEnum.values()).filter(p -> p.getCode().equals(code)).findFirst();
    }

    // 使用 @JsonPropertyOrder 强制指定字段顺序
    @JsonPropertyOrder({"desc", "code"})
    private record EnumItem(String desc, Integer code) {}

    public static String toJsonArrayString() {
        List<EnumItem> items = Arrays.stream(values())
                .map(e -> new EnumItem(e.desc, e.code))
                .toList();
        return JsonUtil.toJsonString(items);
    }

}
