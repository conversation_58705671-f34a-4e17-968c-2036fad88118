package com.unipus.digitalbook.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程性质枚举
 * - 必修
 * - 限选
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum CourseNatureEnum implements Serializable {
    REQUIRED("required", "必修"),
    LIMITED("limited", "限选");

    private final String code;
    private final String desc;

    private static final Map<String, CourseNatureEnum> CODE_MAP = new HashMap<>();

    static {
        for (CourseNatureEnum value : CourseNatureEnum.values()) {
            CODE_MAP.put(value.getCode(), value);
        }
    }

    CourseNatureEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CourseNatureEnum> getAllCourseNature() {
        return Arrays.asList(CourseNatureEnum.values());
    }
    public static CourseNatureEnum getByCode(String code) {
        return CODE_MAP.get(code);
    }
    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
