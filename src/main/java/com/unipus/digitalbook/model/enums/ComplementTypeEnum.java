package com.unipus.digitalbook.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 配套资源类型类型
 */
public enum ComplementTypeEnum {
    DOC(1, "文档", 200L * 1024 * 1024, "200MB"),
    AUDIO(2, "音频", 100L * 1024 * 1024, "100MB"),
    VIDEO(3, "视频", 800L * 1024 * 1024, "800MB"),
    IMAGE(4, "图片", 50L * 1024 * 1024, "50MB");

    private final Integer code;
    private final String desc;

    private final Long maxSize;

    private final String maxSizeDesc;

    private static final Map<Integer, ComplementTypeEnum> CODE_MAP = new HashMap<>();

    static {
        for (ComplementTypeEnum typeEnum : ComplementTypeEnum.values()) {
            CODE_MAP.put(typeEnum.getCode(), typeEnum);
        }
    }

    ComplementTypeEnum(Integer code, String desc, Long maxSize, String maxSizeDesc) {
        this.code = code;
        this.desc = desc;
        this.maxSize = maxSize;
        this.maxSizeDesc = maxSizeDesc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Long getMaxSize() {
        return maxSize;
    }

    public String getMaxSizeDesc() {
        return maxSizeDesc;
    }

    public static ComplementTypeEnum getByCode(Integer code) {
        return CODE_MAP.get(code);
    }
}
