package com.unipus.digitalbook.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 用户身份
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum UserIdentityEnum {
    PREVIEWER(1, "预览者"),
    COLLABORATOR(2, "协作者"),
    EDITOR(3, "编者");

    private static final Map<Integer, UserIdentityEnum> CODE_MAP = new HashMap<>();

    static {
        for (UserIdentityEnum item : UserIdentityEnum.values()) {
            CODE_MAP.put(item.getCode(), item);
        }
    }

    UserIdentityEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserIdentityEnum getByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    public static UserIdentityEnum getByBookPermission(int bookPermissionCode, Long userId, Long editorId) {
        if (bookPermissionCode == PermissionTypeEnum.EDIT.getCode()) {
            if (Objects.equals(userId, editorId)) {
                return EDITOR;
            }
            return COLLABORATOR;
        }
        return PREVIEWER;
    }
    private final Integer code;
    private final String description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
