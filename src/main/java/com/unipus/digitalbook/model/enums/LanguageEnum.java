package com.unipus.digitalbook.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支持的语种
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum LanguageEnum implements Serializable {
    ENGLISH("en", "英语"),
    CHINESE("zh", "中文"),
    JAPANESE("ja", "日语"),
    KOREAN("ko", "韩语"),
    GERMAN("de", "德语"),
    FRENCH("fr", "法语"),
    RUSSIAN("ru", "俄语"),
    SPANISH("es", "西班牙语"),
    ITALIAN("it", "意大利语"),
    ARABIC("ar", "阿拉伯语"),
    FINNISH("fi", "芬兰语"),

    ;

    private final String code;
    private final String desc;

    private static final Map<String, LanguageEnum> CODE_MAP = new HashMap<>();

    static {
        for (LanguageEnum lang : LanguageEnum.values()) {
            CODE_MAP.put(lang.getCode(), lang);
        }
    }

    LanguageEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LanguageEnum getByCode(String code) {
        return CODE_MAP.get(code);
    }

    public static List<LanguageEnum> getAllLanguages() {
        return List.of(LanguageEnum.values());
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
