package com.unipus.digitalbook.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务类型枚举
 * - 大学英语
 * - 英语专业
 * - 职业教育
 * - 研究生英语
 * - 综合语种
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum BusinessTypeEnum implements Serializable {
    UNIVERSITY_ENGLISH("university_english", "大学英语"),
    ENGLISH_MAJOR("english_major", "英语专业"),
    PROFESSIONAL_EDUCATION("professional_education", "职业教育"),
    GRADUATE_ENGLISH("graduate_english", "研究生英语"),
    MULTILINGUAL("multilingual", "综合语种");
    private final String code;
    private final String desc;

    private static final Map<String, BusinessTypeEnum> CODE_MAP = new HashMap<>();

    static {
        for (BusinessTypeEnum type : BusinessTypeEnum.values()) {
            CODE_MAP.put(type.getCode(), type);
        }
    }

    BusinessTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BusinessTypeEnum getByCode(String code) {
        return CODE_MAP.get(code);
    }

    public static List<BusinessTypeEnum> getAllBusinessTypes() {
        return List.of(BusinessTypeEnum.values());
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
