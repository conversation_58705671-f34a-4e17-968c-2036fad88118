package com.unipus.digitalbook.model.params.paper.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.WritingQuestion;

/**
 * 写作题题
 */
public class PaperWritingQuestionParam extends PaperQuestionBaseParam {

    @Override
    public void valid() {// 无特殊参数校验
    }


    @Override
    protected Question toQuestion(QuestionText questionText) {
        QuestionText currentQuestionText = new QuestionText(questionText.getText(), questionText.getPlainText());
        currentQuestionText.setAnswerWordLimit(getAnswerWordLimit());
        WritingQuestion question = new WritingQuestion();
        question.setQuestionText(currentQuestionText);
        return question;
    }
}
