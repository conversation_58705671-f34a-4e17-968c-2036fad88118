package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 题目查询参数
 */
public class QuestionQueryParam implements Params {
    @Schema(description = "题目ID信息列表")
    private List<IdVersion> idVersion;

    @Schema(description = "是否包含题目内容", defaultValue = "false")
    private Boolean includeContent = false;

    public QuestionQueryParam() {}

    public QuestionQueryParam(List<IdVersion> idVersion, Boolean includeContent) {
        this.idVersion = idVersion;
        this.includeContent = includeContent;
    }

    public Boolean getIncludeContent() {
        return includeContent;
    }

    public void setIncludeContent(Boolean includeContent) {
        this.includeContent = includeContent;
    }

    public List<IdVersion> getIdVersion() {
        return idVersion;
    }

    public void setIdVersion(List<IdVersion> idVersion) {
        this.idVersion = idVersion;
    }

    @Schema(description = "题目ID信息")
    public static class IdVersion implements Serializable {
        @Schema(description = "题目ID")
        private Long quesId;

        @Schema(description = "题目版本")
        private Integer version;

        public Long getQuesId() {
            return quesId;
        }

        public void setQuesId(Long quesId) {
            this.quesId = quesId;
        }

        public Integer getVersion() {
            return version;
        }

        public void setVersion(Integer version) {
            this.version = version;
        }
    }

    /**
     * 参数
     */
    @Override
    public void valid() {
        if(CollectionUtils.isEmpty(idVersion)){
            throw new IllegalArgumentException("题目ID信息不能为空");
        }
    }
}
