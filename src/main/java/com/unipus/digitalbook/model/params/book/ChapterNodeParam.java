package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.constants.CommonConstant;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Set;

@Schema(description = "章节内的每一个节点数据")
public class ChapterNodeParam implements Params {
    @Schema(description = "标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @Schema(description = "文本")
    private String text;

    @Schema(description = "节点类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;

    @Schema(description = "词数")
    private Long wordCount;

    @Schema(description = "音频时长 (单位：毫秒)")
    private Long audioDuration;

    @Schema(description = "视频时长 (单位：毫秒)")
    private Long videoDuration;

    @Schema(description = "中日韩字符数")
    private Long cjkWordCount;

    @Schema(description = "非中日韩字符数")
    private Long nonCjkWordCount;

    @Schema(description = "题目类型")
    private String questionType;
    private static final Set<String> VALID_TYPES = Set.of(
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "base-paragraph",
            "list",
            "quote",
            "highlight-block",
            "teacher-specific-block",
            "advanced-layout",
            "collapsible-block",
            "insert-image",
            "insert-audio",
            "insert-video",
            "insert-document",
            "column-container",
            "table-container",
            "question-block",
            "competition-highlight",
            "multimodal-annotation",
            "bubble-annotation",
            "quiz-paper-link");

    /**
     * 验证方法，用于验证各个字段的有效性
     */
    @Override
    public void valid() {
        if ("question-block".equals(type) && (text == null || text.isEmpty())) {
            throw new IllegalArgumentException("question-block类型的节点bigQuestionId不能为空");
        }
        //todo 还原
//        if (id == null || id.isEmpty()) {
//            throw new IllegalArgumentException("id不能为空");
//        }
//        if (type == null || type.isEmpty()) {
//            throw new IllegalArgumentException("type不能为空");
//        }
        if (!VALID_TYPES.contains(type)) {
            throw new IllegalArgumentException("type类型无效，有效值为: " + String.join(", ", VALID_TYPES));
        }

        // 标题类型验证
        if (type.startsWith("h")&&type.length()==2) {
            if (text == null || text.isEmpty()) {
                throw new IllegalArgumentException(type + "类型的节点text不能为空");
            }
            // 对于文本类型节点，wordCount不应为空且应大于0
            if (wordCount == null || wordCount <= 0) {
                throw new IllegalArgumentException(type + "类型的节点wordCount必须大于0");
            }
        }

        // 媒体类型特殊验证
        switch (type) {
            case CommonConstant.INSERT_AUDIO_TYPE:
                if (audioDuration == null || audioDuration <= 0) {
                    throw new IllegalArgumentException("insert-audio类型的节点audioDuration必须大于0");
                }
                break;
            case CommonConstant.INSERT_VIDEO_TYPE:
                if (videoDuration == null || videoDuration <= 0) {
                    throw new IllegalArgumentException("insert-video类型的节点videoDuration必须大于0");
                }
                break;
            case CommonConstant.INSERT_IMAGE_TYPE:
                // 图片节点可能需要特殊处理，但无具体字段验证要求
                break;
            default:
                // 其他类型节点不需要特殊验证
                break;
        }

        // 确保无关字段为空
        if (type.equals(CommonConstant.INSERT_AUDIO_TYPE) && (videoDuration != null && videoDuration > 0)) {
            throw new IllegalArgumentException("insert-audio类型的节点不应设置videoDuration");
        }
        if (type.equals(CommonConstant.INSERT_VIDEO_TYPE) && (audioDuration != null && audioDuration > 0)) {
            throw new IllegalArgumentException("insert-video类型的节点不应设置audioDuration");
        }
        if ((type.equals(CommonConstant.INSERT_AUDIO_TYPE)
                || type.equals(CommonConstant.INSERT_VIDEO_TYPE)
                || type.equals(CommonConstant.INSERT_IMAGE_TYPE))
                && (wordCount != null && wordCount > 0)) {
            throw new IllegalArgumentException(type + "类型的节点不应设置wordCount");
        }

    }


    public ChapterNode toEntity() {
        ChapterNode chapterNode= new ChapterNode(id, text, type);
        chapterNode.setWordCount(wordCount);
        chapterNode.setAudioDuration(audioDuration);
        chapterNode.setVideoDuration(videoDuration);
        chapterNode.setCjkWordCount(cjkWordCount);
        chapterNode.setNonCjkWordCount(nonCjkWordCount);
        chapterNode.setQuestionType(questionType);
        return chapterNode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getWordCount() {
        return wordCount;
    }

    public void setWordCount(Long wordCount) {
        this.wordCount = wordCount;
    }

    public Long getAudioDuration() {
        return audioDuration;
    }

    public void setAudioDuration(Long audioDuration) {
        this.audioDuration = audioDuration;
    }

    public Long getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(Long videoDuration) {
        this.videoDuration = videoDuration;
    }

    public Long getCjkWordCount() {
        return cjkWordCount;
    }

    public void setCjkWordCount(Long cjkWordCount) {
        this.cjkWordCount = cjkWordCount;
    }

    public Long getNonCjkWordCount() {
        return nonCjkWordCount;
    }

    public void setNonCjkWordCount(Long nonCjkWordCount) {
        this.nonCjkWordCount = nonCjkWordCount;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }
}

