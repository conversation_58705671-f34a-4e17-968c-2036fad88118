package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.QuestionText;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "关联参数")
public class QuestionRelevancyParam implements Serializable {
    @Schema(description = "关联id")
    private String id;
    @Schema(description = "关联类型")
    private String type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public QuestionText.Relevancy toEntity() {
        QuestionText.Relevancy relevancy = new QuestionText.Relevancy();
        relevancy.setId(id);
        relevancy.setType(type);
        return relevancy;
    }
}
