package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.QuestionTag;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Schema(description = "标签参数")
public class QuestionTagParam implements Params {
    @Schema(description = "父级标签id")
    private Long parentId;
    @Schema(description = "标签名称")
    private String name;
    @Schema(description = "标签类型 1技能点")
    private Integer tagType;

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public QuestionTag toEntity() {
        if (!StringUtils.hasText(name)) {
            return null;
        }
        QuestionTag questionTag = new QuestionTag();
        questionTag.setParentId(parentId);
        questionTag.setName(name);
        questionTag.setTagType(tagType);
        return questionTag;
    }

    public static List<List<QuestionTag>> toEntityList(List<List<QuestionTagParam>> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return Collections.emptyList();
        }
        return tags.stream().map(tagList -> tagList.stream().map(QuestionTagParam::toEntity)
                .filter(Objects::nonNull).toList()).toList();
    }

    @Override
    public void valid() {
        if (tagType == null) {
            throw new IllegalArgumentException("标签类型不能为空");
        }
    }
}
