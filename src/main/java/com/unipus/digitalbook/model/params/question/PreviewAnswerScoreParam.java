package com.unipus.digitalbook.model.params.question;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerList;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "评分接口参数")
public class PreviewAnswerScoreParam implements Params {
    @Schema(description = "大题题组参数")
    private BigQuestionGroupParam group;

    @Schema(description = "用户答题参数")
    private List<UserAnswerParam> userAnswers;

    @Schema(description = "题目版本")
    private String versionNumber = IdentifierUtil.DEFAULT_VERSION_NUMBER;

    public BigQuestionGroup toQuestionEntity(Long userId) {
        return group.toEntity(userId, versionNumber);
    }

    public UserAnswerList toUserAnswerEntity(String openId) {
        // 批次
        String batch = IdentifierUtil.getShortUUID();
        List<UserAnswer> list = userAnswers.stream()
                .map(u -> u.toEntity(openId, versionNumber, batch))
                .toList();
        return new UserAnswerList(list);
    }

    public BigQuestionGroupParam getGroup() {
        return group;
    }

    public void setGroup(BigQuestionGroupParam group) {
        this.group = group;
    }

    public List<UserAnswerParam> getUserAnswers() {
        return userAnswers;
    }

    public void setUserAnswers(List<UserAnswerParam> userAnswers) {
        this.userAnswers = userAnswers;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    @Override
    public void valid() {
        if(group == null) {
            throw new IllegalArgumentException("group不能为空");
        }
        group.valid();
    }
}
