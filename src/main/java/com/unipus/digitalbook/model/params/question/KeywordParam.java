package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.QuestionText;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "关键字参数")
public class KeywordParam implements Serializable {
    @Schema(description = "关键字id")
    private String keywordId;
    @Schema(description = "关键字值")
    private String keywordValue;

    public QuestionText.Keyword toEntity() {
        QuestionText.Keyword keyword = new QuestionText.Keyword();
        keyword.setText(keywordValue);
        keyword.setId(keywordId);
        return keyword;
    }
    public String getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(String keywordId) {
        this.keywordId = keywordId;
    }

    public String getKeywordValue() {
        return keywordValue;
    }

    public void setKeywordValue(String keywordValue) {
        this.keywordValue = keywordValue;
    }
}
