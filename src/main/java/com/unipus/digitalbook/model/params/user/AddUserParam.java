package com.unipus.digitalbook.model.params.user;

import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.StringJoiner;
@Schema(description = "添加用户入参")
public class AddUserParam implements Params {

    @Schema(description = "所属机构ID", example = "12345")
    private Long organizationId;

    @Schema(description = "用户姓名", example = "张三")
    private String name;

    @Schema(description = "手机号", example = "13800138000")
    private String cellPhone;

    @Schema(description = "角色ID列表", example = "[1, 2, 3]")
    private List<Long> roleIds;

    public Long getOrganizationId() {
        return organizationId;
    }

    public AddUserParam setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public String getName() {
        return name;
    }

    public AddUserParam setName(String name) {
        this.name = name;
        return this;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public AddUserParam setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
        return this;
    }

    public List<Long> getRoleIds() {
        return roleIds;
    }

    public AddUserParam setRoleIds(List<Long> roleIds) {
        this.roleIds = roleIds;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", AddUserParam.class.getSimpleName() + "[", "]")
                .add("organizationId=" + organizationId)
                .add("name='" + name + "'")
                .add("cellPhone='" + cellPhone + "'")
                .add("roleIds=" + roleIds)
                .toString();
    }


public UserInfo toEntity() {
    // 创建一个新的UserInfo对象
    UserInfo userInfo = new UserInfo();
    // 设置用户的姓名
    userInfo.setName(this.getName());
    // 设置用户的手机号
    userInfo.setCellPhone(this.getCellPhone());
    // 返回填充了部分信息的UserInfo对象
    return userInfo;
}


    /**
     *
     */
    @Override
    public void valid() {
        // 所属机构：
        if (this.getOrganizationId() == null) {
            throw new IllegalArgumentException("请选择机构");
        }

        // 用户昵称：
        String checkName = this.getName();
        if (checkName == null || checkName.trim().isEmpty()) {
            throw new IllegalArgumentException("请输入用户昵称");
        }
        if (checkName.length() < 2 || checkName.length() > 16) {
            throw new IllegalArgumentException("请输入2-16位字符");
        }
        if (checkName.startsWith(" ") || checkName.endsWith(" ")) {
            throw new IllegalArgumentException("首位不可输入空格");
        }
        if (!checkName.matches("[\\u4e00-\\u9fa5a-zA-Z0-9-_ ]+")) {
            throw new IllegalArgumentException("请输入中文、字母、数字、\"-\"、\"_\"、空格（首尾除外）");
        }
        if (checkName.matches("^1\\d{10}$")) {
            throw new IllegalArgumentException("不可使用手机号");
        }

        // 注册手机号：
        String phone = this.getCellPhone();
        if (phone == null || phone.trim().isEmpty()) {
            throw new IllegalArgumentException("请输入注册手机号");
        }
        if (!phone.matches("^1\\d{10}$")) {
            throw new IllegalArgumentException("请输入以1开头的11位的手机号");
        }

        // 设置角色：
        if (this.getRoleIds() == null || this.getRoleIds().isEmpty()) {
            throw new IllegalArgumentException("请选择角色");
        }
    }

}
