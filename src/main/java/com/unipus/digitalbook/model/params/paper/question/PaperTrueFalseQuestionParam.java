package com.unipus.digitalbook.model.params.paper.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.TrueFalseQuestion;
import com.unipus.digitalbook.model.params.question.QuestionRelevancyParam;
import org.springframework.util.CollectionUtils;

/**
 * 判断题参数
 */
public class PaperTrueFalseQuestionParam extends PaperQuestionBaseParam {


    @Override
    public void valid() {// 无特殊参数校验
    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        TrueFalseQuestion trueFalseQuestion = new TrueFalseQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        if (!CollectionUtils.isEmpty(getRelevancyList())) {
            currentQuestionText.setRelevancy(getRelevancyList().stream().map(QuestionRelevancyParam::toEntity).toList());
        }
        trueFalseQuestion.setQuestionText((currentQuestionText));
        return trueFalseQuestion;
    }
}
