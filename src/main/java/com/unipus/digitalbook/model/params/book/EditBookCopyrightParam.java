package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.book.BookCopyright;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.Objects;
import java.util.regex.Pattern;

@Schema(description = "编辑版权信息")
public class EditBookCopyrightParam implements Params {
    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "总主编")
    private String chiefEditor;

    @Schema(description = "主编")
    private String editor;

    @Schema(description = "副主编")
    private String deputyEditor;

    @Schema(description = "项目策划")
    private String projectPlanner;
    @Schema(description = "出版时间-年")
    private Integer publishYear;
    @Schema(description = "出版时间-月")
    private Integer publishMonth;
    @Schema(description = "出版时间-日")
    private Integer publishDay;
    @Schema(description = "责任编辑")
    private String executiveEditor;

    @Schema(description = "责任校对")
    private String proofreader;

    @Schema(description = "数字编辑")
    private String digitalEditor;

    @Schema(description = "封面设计")
    private String coverDesigner;

    @Schema(description = "版式设计")
    private String layoutDesigner;

    @Schema(description = "出版发行")
    private String publisher;

    @Schema(description = "字数")
    private String wordCount;

    @Schema(description = "ISBN")
    private String isbn;

    @Schema(description = "定价")
    private BigDecimal price;

    @Schema(description = "版次")
    private String edition;

    @Schema(description = "专业审定人员")
    private String professionalReviewer;

    public BookCopyright toEntity(Long userId) {
        BookCopyright bookCopyright = new BookCopyright();
        bookCopyright.setBookId(bookId);
        bookCopyright.setChiefEditor(chiefEditor);
        bookCopyright.setEditor(editor);
        bookCopyright.setDeputyEditor(deputyEditor);
        bookCopyright.setProjectPlanner(projectPlanner);
        bookCopyright.setPublishYear(publishYear);
        bookCopyright.setPublishMonth(publishMonth);
        bookCopyright.setPublishDay(publishDay);
        bookCopyright.setExecutiveEditor(executiveEditor);
        bookCopyright.setProofreader(proofreader);
        bookCopyright.setDigitalEditor(digitalEditor);
        bookCopyright.setCoverDesigner(coverDesigner);
        bookCopyright.setLayoutDesigner(layoutDesigner);
        bookCopyright.setPublisher(publisher);
        bookCopyright.setWordCount(wordCount);
        bookCopyright.setIsbn(isbn);
        bookCopyright.setPrice(price);
        bookCopyright.setEdition(edition);
        bookCopyright.setProfessionalReviewer(professionalReviewer);
        bookCopyright.setCreateBy(userId);
        bookCopyright.setUpdateBy(userId);
        bookCopyright.setEnable(true);
        return bookCopyright;
    }

    @Override
    public void valid() {
        if(!StringUtils.hasText(bookId)){
            throw new IllegalArgumentException("bookId不能为空");
        }
        // 必填字段校验
        validateRequired(editor, "主编");
        validateRequired(digitalEditor, "数字编辑");
        validateRequired(publisher, "出版发行");
        validateRequired(edition, "版次");
        validatePrice(price);
        validatePublishDate(publishYear, publishMonth, publishDay);

        // 长度校验
        validateLength(chiefEditor, 100, "总主编");
        validateLength(editor, 100, "主编");
        validateLength(deputyEditor, 100, "副主编");
        validateLength(projectPlanner, 100, "项目策划");
        validateLength(executiveEditor, 100, "责任编辑");
        validateLength(proofreader, 100, "责任校对");
        validateLength(digitalEditor, 100, "数字编辑");
        validateLength(coverDesigner, 100, "封面设计");
        validateLength(layoutDesigner, 100, "版式设计");
        validateLength(publisher, 100, "出版发行");
        validateLength(edition, 10, "版次");
        validateLength(isbn, 30, "ISBN");
        validateLength(professionalReviewer, 100, "专业审定人员");
    }

    private void validatePrice(BigDecimal price) {
        if (price != null) {
            String pricePattern = "^\\d{1,5}(\\.\\d{1,2})?$";
            String priceStr = price.stripTrailingZeros().toPlainString(); // 去除无效小数位
            if (!Pattern.matches(pricePattern, priceStr)) {
                throw new IllegalArgumentException("定价格式错误，仅允许输入最多 5 位整数和 2 位小数");
            }
        }
    }
    private void validateRequired(Object value, String fieldName) {
        if (Objects.isNull(value) || (value instanceof String str && str.trim().isEmpty())) {
            throw new IllegalArgumentException("请输入" + fieldName + "。");
        }
    }

    private void validateLength(String value, int maxLength, String fieldName) {
        if (value != null && value.length() > maxLength) {
            throw new IllegalArgumentException(fieldName + "最大可输入" + maxLength + "个字符");
        }
    }

    private void validatePublishDate(Integer year, Integer month, Integer day) {
        if (year == null || year < 1900 || year > 2100) {
            throw new IllegalArgumentException("出版年份必须在 1900 - 2100 之间。");
        }
        if (month == null || month < 1 || month > 12) {
            throw new IllegalArgumentException("出版月份必须在 1 - 12 之间。");
        }
        int maxDay = YearMonth.of(year, month).lengthOfMonth();
        if (day != null && (day < 0 || day > maxDay)) {
            throw new IllegalArgumentException("出版日期不合法，" + year + "年" + month + "月最多 " + maxDay + " 天。");
        }
    }
    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChiefEditor() {
        return chiefEditor;
    }

    public void setChiefEditor(String chiefEditor) {
        this.chiefEditor = chiefEditor;
    }

    public String getEditor() {
        return editor;
    }

    public void setEditor(String editor) {
        this.editor = editor;
    }

    public String getDeputyEditor() {
        return deputyEditor;
    }

    public void setDeputyEditor(String deputyEditor) {
        this.deputyEditor = deputyEditor;
    }

    public String getProjectPlanner() {
        return projectPlanner;
    }

    public void setProjectPlanner(String projectPlanner) {
        this.projectPlanner = projectPlanner;
    }

    public String getExecutiveEditor() {
        return executiveEditor;
    }

    public void setExecutiveEditor(String executiveEditor) {
        this.executiveEditor = executiveEditor;
    }

    public String getProofreader() {
        return proofreader;
    }

    public void setProofreader(String proofreader) {
        this.proofreader = proofreader;
    }

    public String getDigitalEditor() {
        return digitalEditor;
    }

    public void setDigitalEditor(String digitalEditor) {
        this.digitalEditor = digitalEditor;
    }

    public String getCoverDesigner() {
        return coverDesigner;
    }

    public void setCoverDesigner(String coverDesigner) {
        this.coverDesigner = coverDesigner;
    }

    public String getLayoutDesigner() {
        return layoutDesigner;
    }

    public void setLayoutDesigner(String layoutDesigner) {
        this.layoutDesigner = layoutDesigner;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public Integer getPublishYear() {
        return publishYear;
    }

    public void setPublishYear(Integer publishYear) {
        this.publishYear = publishYear;
    }

    public Integer getPublishMonth() {
        return publishMonth;
    }

    public void setPublishMonth(Integer publishMonth) {
        this.publishMonth = publishMonth;
    }

    public Integer getPublishDay() {
        return publishDay;
    }

    public void setPublishDay(Integer publishDay) {
        this.publishDay = publishDay;
    }

    public String getIsbn() {
        return isbn;
    }

    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    public void setWordCount(String wordCount) {
        this.wordCount = wordCount;
    }

    public String getWordCount() {
        return wordCount;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getEdition() {
        return edition;
    }

    public void setEdition(String edition) {
        this.edition = edition;
    }

    public String getProfessionalReviewer() {
        return professionalReviewer;
    }

    public void setProfessionalReviewer(String professionalReviewer) {
        this.professionalReviewer = professionalReviewer;
    }
}
