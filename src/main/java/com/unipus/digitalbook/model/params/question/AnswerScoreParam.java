package com.unipus.digitalbook.model.params.question;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.question.SubmitAnswerContext;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerList;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

import java.util.List;

@Schema(description = "评分接口参数")
public class AnswerScoreParam implements Params {
    @Schema(description = "题组ID")
    private String groupId;

    @Schema(description = "题目版本")
    private String versionNumber;
    @Schema(description = "教材Id")
    private String bookId;

    @Schema(description = "教材版本")
    private String bookVersionNumber;

    @Schema(description = "章节id")
    private String chapterId;

    @Schema(description = "章节版本")
    private String chapterVersionNumber;

    @Schema(description = "用户答题参数")
    private List<UserAnswerParam> userAnswers;

    @Override
    public void valid() {
        if (!StringUtils.hasText(groupId)) {
            throw new IllegalArgumentException("题组ID不能为空");
        }
        if (!StringUtils.hasText(versionNumber)) {
            throw new IllegalArgumentException("题目版本不能为空");
        }
    }

    public UserAnswerList toEntity(String openId, Long tenantId, String envPartition) {
        // 批次
        String batch = IdentifierUtil.getShortUUID();
        List<UserAnswer> list = userAnswers.stream()
                .map(u -> u.toEntity(tenantId, envPartition, openId, versionNumber, batch))
                .toList();
        return new UserAnswerList(list);
    }

    public SubmitAnswerContext toContext(Long tenantId, String openId, String envPartition, String clientIp, String dataPackage, Long chapterVersionId) {
        SubmitAnswerContext context = new SubmitAnswerContext();
        context.setBookId(bookId);
        context.setBookVersionNumber(bookVersionNumber);
        context.setContentId(chapterId);
        context.setContentVersionId(chapterVersionId);
        context.setTenantId(tenantId);
        context.setOpenId(openId);
        context.setEnvPartition(envPartition);
        context.setClientIp(clientIp);
        context.setDataPackage(dataPackage);
        context.setContentType(ContentTypeEnum.CHAPTER);
        return context;
    }


    public List<UserAnswerParam> getUserAnswers() {
        return userAnswers;
    }

    public void setUserAnswers(List<UserAnswerParam> userAnswers) {
        this.userAnswers = userAnswers;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getChapterVersionNumber() {
        return chapterVersionNumber;
    }

    public void setChapterVersionNumber(String chapterVersionNumber) {
        this.chapterVersionNumber = chapterVersionNumber;
    }
}
