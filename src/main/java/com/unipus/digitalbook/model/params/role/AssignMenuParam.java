package com.unipus.digitalbook.model.params.role;

import com.unipus.digitalbook.model.entity.role.RolePermission;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "角色分配菜单权限请求参数")
public class AssignMenuParam implements Params {
    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "菜单参数")
    private List<MenuPermissionParam> menuPermissions;

    public RolePermission toEntity() {
        RolePermission rolePermission = new RolePermission();
        rolePermission.setRoleId(roleId);
        rolePermission.setPermissionItems(menuPermissions.stream().map(MenuPermissionParam::toEntity).toList());
        return rolePermission;
    }
    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public List<MenuPermissionParam> getMenuPermissions() {
        return menuPermissions;
    }

    public void setMenuPermissions(List<MenuPermissionParam> menuPermissions) {
        this.menuPermissions = menuPermissions;
    }

    @Override
    public void valid() {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        if (menuPermissions == null || menuPermissions.isEmpty()) {
            throw new IllegalArgumentException("菜单权限不能为空");
        }
    }
}
