package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Schema(description = "用户资源权限信息参数")
public class ResourceUserPermissionParam implements Params {

    @Schema(description = "资源ID")
    private String resourceId;

    @Schema(description = "用户Id列表")
    private List<Long> userIds;

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }

    @Override
    public void valid() {
        if (resourceId == null) {
            throw new IllegalArgumentException("资源ID不能为空");
        }
    }

    public List<ResourcePermission> toEntity(ResourceTypeEnum resourceType, PermissionTypeEnum permissionType){
        if(CollectionUtils.isEmpty(userIds)){
            // 未指定用户列表时，默认清除该资源关联用户的指定权限
            ResourcePermission resourceUserPermission = new ResourcePermission();
            resourceUserPermission.setResourceId(resourceId);
            resourceUserPermission.setResourceType(resourceType.getCode());
            resourceUserPermission.setUserId(null);
            resourceUserPermission.setPermissionType(permissionType.getCode());
            return List.of(resourceUserPermission);
        }else {
            // 指定用户列表时，为指定用户设置指定权限
            return userIds.stream().map(userId -> {
                ResourcePermission resourceUserPermission = new ResourcePermission();
                resourceUserPermission.setResourceId(resourceId);
                resourceUserPermission.setResourceType(resourceType.getCode());
                resourceUserPermission.setUserId(userId);
                resourceUserPermission.setPermissionType(permissionType.getCode());
                return resourceUserPermission;
            }).toList();
        }
    }

}
