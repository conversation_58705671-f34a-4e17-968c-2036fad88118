package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.ChoiceQuestionOption;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

public class QuestionOptionParam implements Serializable {
    @Schema(description = "选项名称，如 A、B、C、D")
    private String label;
    @Schema(description = "选项的Id")
    private String optionId;
    @Schema(description = "选项的文本内容")
    private String optionValue;
    @Schema(description = "更新人")
    private Long updateBy;

    public ChoiceQuestionOption toEntity(Long userId) {
        ChoiceQuestionOption entity = new ChoiceQuestionOption();
        entity.setName(this.label);
        entity.setContent(this.optionValue);
        entity.setOptionId(this.optionId);
        entity.setUpdateBy(userId);
        entity.setCreateBy(userId);
        return entity;
    }
    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getOptionId() {
        return optionId;
    }

    public void setOptionId(String optionId) {
        this.optionId = optionId;
    }

    public String getOptionValue() {
        return optionValue;
    }

    public void setOptionValue(String optionValue) {
        this.optionValue = optionValue;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }
}