package com.unipus.digitalbook.model.params.user;

import com.unipus.digitalbook.model.params.Params;

import java.util.List;

public class DeleteUserParam implements Params {

    private List<Long> userIdList;

    private Long orgId;

    public List<Long> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<Long> userIdList) {
        this.userIdList = userIdList;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     *
     */
    @Override
    public void valid() {
        if (userIdList == null || userIdList.isEmpty()) {
            throw new IllegalArgumentException("用户id不能为空");
        }
        if (orgId == null || orgId <= 0) {
            throw new IllegalArgumentException("机构id不能为空");
        }
    }
}
