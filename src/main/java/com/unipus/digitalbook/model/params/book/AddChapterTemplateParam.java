package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.chapter.ChapterTemplate;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.StringJoiner;

@Schema(description = "增加章节版本参数")
public class AddChapterTemplateParam implements Params {

    @Schema(description = "模版来源的章节ID")
    private String fromChapterId;

    @Schema(description = "模版名称")
    private String name;

    @Schema(description = "章节内容存储的url")
    private String url;

    @Schema(description = "模版预览图")
    private String previewImageUrl;

    public ChapterTemplate toEntity(Long userId) {
        ChapterTemplate.Builder builder = ChapterTemplate.Builder.newBuilder();
        builder.fromChapterId(fromChapterId)
                .name(name)
                .url(url)
                .previewImageUrl(previewImageUrl)
                .createBy(userId);
        return builder.build();
    }

    @Override
    public void valid() {
        if (StringUtils.isBlank(fromChapterId)) {
            throw new IllegalArgumentException("模版来源的章节ID不能为空");
        }
        if (StringUtils.isBlank(name)) {
            throw new IllegalArgumentException("模版名称不能为空");
        }
        if (StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("模版内容存储的url不能为空");
        }
    }

    public String getFromChapterId() {
        return fromChapterId;
    }

    public void setFromChapterId(String fromChapterId) {
        this.fromChapterId = fromChapterId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", AddChapterTemplateParam.class.getSimpleName() + "[", "]")
                .add("fromChapterId='" + fromChapterId + "'")
                .add("name='" + name + "'")
                .add("url='" + url + "'")
                .add("previewImageUrl='" + previewImageUrl + "'")
                .toString();
    }
}
