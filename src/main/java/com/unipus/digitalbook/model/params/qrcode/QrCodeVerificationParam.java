package com.unipus.digitalbook.model.params.qrcode;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "二维码验证参数")
public class QrCodeVerificationParam implements Params {
    /**
     * 验证结果列表
     */
    @Schema(description = "验证结果列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<VerificationResult> verificationResults;

    /**
     *
     */
    @Override
    public void valid() {
        if (verificationResults == null || verificationResults.isEmpty()) {
            throw new IllegalArgumentException("verificationResults不能为空");
        }
        for (VerificationResult verificationResult : verificationResults) {
            if (verificationResult.qrCodeId == null) {
                throw new IllegalArgumentException("verificationResult.qrCodeId不能为空");
            }
            if (verificationResult.success == null) {
                throw new IllegalArgumentException("verificationResult.success不能为空");
            }
        }
    }

    public List<VerificationResult> getVerificationResults() {
        return verificationResults;
    }

    public void setVerificationResults(List<VerificationResult> verificationResults) {
        this.verificationResults = verificationResults;
    }

    public static class VerificationResult implements Serializable {
        @Schema(description = "二维码ID", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long qrCodeId;

        /**
         * 验证结果: true-验证成功，false-验证失败
         */
        @Schema(description = "验证结果: true-验证成功，false-验证失败", requiredMode = Schema.RequiredMode.REQUIRED)
        private Boolean success;

        public Long getQrCodeId() {
            return qrCodeId;
        }

        public void setQrCodeId(Long qrCodeId) {
            this.qrCodeId = qrCodeId;
        }

        public Boolean getSuccess() {
            return success;
        }

        public void setSuccess(Boolean success) {
            this.success = success;
        }
    }
}

