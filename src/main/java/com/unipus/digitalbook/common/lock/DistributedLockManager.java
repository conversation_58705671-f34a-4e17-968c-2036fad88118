package com.unipus.digitalbook.common.lock;

import com.unipus.digitalbook.conf.lock.DistributedLockConfig;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式锁管理器
 * 基于Redisson实现的分布式锁管理
 * 
 * <AUTHOR>
 */
@Component
public class DistributedLockManager {
    
    private static final Logger logger = LoggerFactory.getLogger(DistributedLockManager.class);
    
    @Autowired
    private RedissonClient redissonClient;
    
    @Autowired
    private DistributedLockConfig lockConfig;
    
    /**
     * 获取锁对象
     * 
     * @param lockKey 锁key
     * @return 锁对象
     */
    public RLock getLock(String lockKey) {
        String fullKey = lockConfig.getLockPrefix() + lockKey;
        return redissonClient.getLock(fullKey);
    }
    
    /**
     * 尝试获取锁（使用默认配置）
     * 
     * @param lockKey 锁key
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey) {
        return tryLock(lockKey, lockConfig.getDefaultWaitTime(), lockConfig.getDefaultLeaseTime());
    }
    
    /**
     * 尝试获取锁
     * 
     * @param lockKey 锁key
     * @param waitTime 等待时间（毫秒）
     * @param leaseTime 锁持有时间（毫秒）
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, long waitTime, long leaseTime) {
        RLock lock = getLock(lockKey);
        try {
            long startTime = System.currentTimeMillis();
            boolean acquired = lock.tryLock(waitTime, leaseTime, TimeUnit.MILLISECONDS);
            long duration = System.currentTimeMillis() - startTime;
            
            if (acquired) {
                logger.debug("Successfully acquired lock: {}, wait time: {}ms", lockKey, duration);
                
                // 检查是否需要告警
                if (lockConfig.isEnableMonitoring() && duration > lockConfig.getTimeoutAlertThreshold()) {
                    logger.warn("Lock acquisition took too long: {}, duration: {}ms", lockKey, duration);
                }
            } else {
                logger.debug("Failed to acquire lock: {}, wait time: {}ms", lockKey, duration);
            }
            
            return acquired;
        } catch (InterruptedException e) {
            logger.error("Lock acquisition interrupted: {}", lockKey, e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            logger.error("Failed to acquire lock: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 释放锁
     * 
     * @param lock 锁对象
     */
    public void unlock(RLock lock) {
        try {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
                logger.debug("Successfully released lock");
            }
        } catch (Exception e) {
            logger.error("Failed to release lock", e);
        }
    }
    
    /**
     * 释放锁（通过key）
     * 
     * @param lockKey 锁key
     */
    public void unlock(String lockKey) {
        RLock lock = getLock(lockKey);
        unlock(lock);
    }
    
    /**
     * 在锁保护下执行操作
     * 
     * @param lockKey 锁key
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁失败或执行异常
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> operation) {
        return executeWithLock(lockKey, operation, 
            lockConfig.getDefaultWaitTime(), lockConfig.getDefaultLeaseTime());
    }
    
    /**
     * 在锁保护下执行操作
     * 
     * @param lockKey 锁key
     * @param operation 要执行的操作
     * @param waitTime 等待时间（毫秒）
     * @param leaseTime 锁持有时间（毫秒）
     * @param <T> 返回类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁失败或执行异常
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> operation, long waitTime, long leaseTime) {
        RLock lock = getLock(lockKey);
        
        try {
            if (!lock.tryLock(waitTime, leaseTime, TimeUnit.MILLISECONDS)) {
                throw new RuntimeException("Failed to acquire lock: " + lockKey + 
                    " within " + waitTime + "ms");
            }
            
            logger.debug("Executing operation with lock: {}", lockKey);
            return operation.get();
            
        } catch (InterruptedException e) {
            logger.error("Lock acquisition interrupted: {}", lockKey, e);
            Thread.currentThread().interrupt();
            throw new RuntimeException("Lock acquisition interrupted", e);
        } catch (Exception e) {
            logger.error("Error executing operation with lock: {}", lockKey, e);
            throw new RuntimeException("Failed to execute operation with lock", e);
        } finally {
            unlock(lock);
        }
    }
    
    /**
     * 在锁保护下执行操作（无返回值）
     * 
     * @param lockKey 锁key
     * @param operation 要执行的操作
     */
    public void executeWithLock(String lockKey, Runnable operation) {
        executeWithLock(lockKey, () -> {
            operation.run();
            return null;
        });
    }
    
    /**
     * 检查锁是否被当前线程持有
     * 
     * @param lockKey 锁key
     * @return 是否被当前线程持有
     */
    public boolean isHeldByCurrentThread(String lockKey) {
        RLock lock = getLock(lockKey);
        return lock.isHeldByCurrentThread();
    }
    
    /**
     * 检查锁是否存在
     * 
     * @param lockKey 锁key
     * @return 是否存在
     */
    public boolean isLocked(String lockKey) {
        RLock lock = getLock(lockKey);
        return lock.isLocked();
    }
}