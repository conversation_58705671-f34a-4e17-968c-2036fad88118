package com.unipus.digitalbook.common.exception.business;

import com.unipus.digitalbook.model.enums.ResultMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义异常-业务模块通用异常
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BizException extends RuntimeException {

    private final Integer code;

    private final String message;

    public BizException(Integer code) {
        this.code = code;
        ResultMessage resultMessage = ResultMessage.getMessage(code);
        this.message = resultMessage==null ? "" : resultMessage.getMessage();
    }

    public BizException(Integer code, Object... args) {
        this.code = code;
        ResultMessage resultMessage = ResultMessage.getMessage(code);
        this.message = resultMessage==null ? "" : resultMessage.getMessage(args);
    }

    public BizException(ResultMessage resultMessage, Object... args) {
        this.code = resultMessage.getCode();
        this.message = resultMessage.getMessage(args);
    }

    public BizException(String message) {
        this.code = 500;
        this.message = message;
    }
}
