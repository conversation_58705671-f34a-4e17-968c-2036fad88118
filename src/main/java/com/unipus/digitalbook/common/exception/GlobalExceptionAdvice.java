package com.unipus.digitalbook.common.exception;

import com.unipus.digitalbook.common.exception.assistant.AssistantException;
import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.exception.cos.COSCredentialException;
import com.unipus.digitalbook.common.exception.cos.COSException;
import com.unipus.digitalbook.common.exception.db.InsertFailedException;
import com.unipus.digitalbook.common.exception.knowledge.*;
import com.unipus.digitalbook.common.exception.org.OrgListEmptyException;
import com.unipus.digitalbook.common.exception.org.RedisCatchException;
import com.unipus.digitalbook.common.exception.paper.UserPaperNotPassException;
import com.unipus.digitalbook.common.exception.permission.PermissionCheckException;
import com.unipus.digitalbook.common.exception.qrcode.Cms2QrCodeException;
import com.unipus.digitalbook.common.exception.qrcode.QrCodeExportRelationalException;
import com.unipus.digitalbook.common.exception.question.GrpcQuestionInvalidResponseException;
import com.unipus.digitalbook.common.exception.soe.SoeException;
import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.common.exception.user.*;
import com.unipus.digitalbook.common.utils.ExceptionNotificationUtil;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.FeishuBotService;
import com.unipus.digitalbook.service.remote.restful.developer.DeveloperFeishuApiService;
import com.unipus.digitalbook.service.remote.restful.developer.model.DeveloperProcessRequest;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.naming.NoPermissionException;
import java.io.Serializable;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;

/**
 * 异常统一处理拦截
 */

@Slf4j
@ControllerAdvice
public class GlobalExceptionAdvice<T> {

    @Resource
    private FeishuBotService feishuBotService;

    @Resource
    @Qualifier("exceptionNotificationExecutor")
    private ExecutorService exceptionNotificationExecutor;

    @Value("${app.env:dev}")
    private String appEnv;

    @Resource
    private DeveloperFeishuApiService developerFeishuApiService;

    // 本地请求地址，用于过滤本地调试异常
    private static final String[] LOCAL_HOSTS = {
        "localhost", "127.0.0.1", "0:0:0:0:0:0:0:1"
    };

    @ResponseBody
    @ExceptionHandler(value = IllegalArgumentException.class)
    public Response<Serializable> illegalArgumentHandler(IllegalArgumentException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }


    @ResponseBody
    @ExceptionHandler(value = InsertFailedException.class)
    public Response<Serializable> insertFailedExceptionHandler(InsertFailedException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = UserNotExistException.class)
    public Response<Serializable> userNotExistExceptionHandler(UserNotExistException e) {
        log.error(e.getMessage(), e);
        if (StringUtils.hasText(e.getMessage())){
            return Response.fail(e.getMessage());
        }
        return Response.fail("用户信息不存在");
    }

    @ResponseBody
    @ExceptionHandler(value = UserAlreadyActivatedException.class)
    public Response<Serializable> userAlreadyActivatedExceptionHandler(UserAlreadyActivatedException e) {
        log.error(e.getMessage(), e);
        if (StringUtils.hasText(e.getMessage())){
            return Response.fail(e.getMessage());
        }
        return Response.fail("用户已激活，无法操作。");
    }

    @ResponseBody
    @ExceptionHandler(value = UserAlreadyExistException.class)
    public Response<Serializable> userAlreadyExistExceptionHandler(UserAlreadyExistException e) {
        log.error(e.getMessage(), e);
        if (StringUtils.hasText(e.getMessage())){
            return Response.fail(e.getMessage());
        }
        return Response.fail("该用户在机构中已存在");
    }

    /**
     * 自定义验证异常
     */
    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Response<Serializable> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        String message = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
        return Response.fail(message);
    }

    @ResponseBody
    @ExceptionHandler(value = NoPermissionException.class)
    public Response<Serializable> noPermissionExceptionHandler(NoPermissionException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }
    @ResponseBody
    @ExceptionHandler(value = IllegalStateException.class)
    public Response<Serializable> illegalStateExceptionHandler(IllegalStateException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }
    @ResponseBody
    @ExceptionHandler(value = NoSuchElementException.class)
    public Response<Serializable> noSuchElementExceptionHandler(NoSuchElementException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = DuplicateRecordException.class)
    public Response<Serializable> duplicateRecordExceptionHandler(DuplicateRecordException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 对象存储认证异常
     */
    @ResponseBody
    @ExceptionHandler(value = COSCredentialException.class)
    public Response<Serializable> cosCredentialExceptionHandler(COSCredentialException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 对象存储异常
     */
    @ResponseBody
    @ExceptionHandler(value = COSException.class)
    public Response<Serializable> cosExceptionHandler(COSException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * soe异常
     */
    @ResponseBody
    @ExceptionHandler(value = SoeException.class)
    public Response<Serializable> soeExceptionHandler(SoeException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 组织列表异常
     */
    @ResponseBody
    @ExceptionHandler(value = OrgListEmptyException.class)
    public Response<Serializable> orgExceptionHandler(OrgListEmptyException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 组织缓存异常
     */
    @ResponseBody
    @ExceptionHandler(value = RedisCatchException.class)
    public Response<Serializable> orgExceptionHandler(RedisCatchException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 数据权限异常
     */
    @ResponseBody
    @ExceptionHandler(value = PermissionCheckException.class)
    public Response<Serializable> permissionExceptionHandler(PermissionCheckException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 二维码异常
     */
    @ResponseBody
    @ExceptionHandler(value = Cms2QrCodeException.class)
    public Response<Serializable> cms2QrCodeExceptionHandler(Cms2QrCodeException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 二维码导出异常
     */
    @ResponseBody
    @ExceptionHandler(value = QrCodeExportRelationalException.class)
    public Response<Serializable> qrCodeExportRelationalExceptionHandler(QrCodeExportRelationalException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 题库返回值异常
     */
    @ResponseBody
    @ExceptionHandler(value = GrpcQuestionInvalidResponseException.class)
    public Response<Serializable> grpcQuestionInvalidResponseExceptionHandler(GrpcQuestionInvalidResponseException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 用户身份信息异常
     */
    @ResponseBody
    @ExceptionHandler(value = UserAuthInfoException.class)
    public Response<Serializable> userAuthInfoExceptionHandler(UserAuthInfoException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 用户信息导入异常
     */
    @ResponseBody
    @ExceptionHandler(value = UserImportException.class)
    public Response<Serializable> userImportExceptionHandler(UserImportException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ResponseBody
    @ExceptionHandler(value = {KnowledgeException.class, KnowledgeCreateException.class, KnowledgeNotExistException.class, KnowledgeResourceExistException.class, KnowledgeResourceUrlNullException.class})
    public Response knowledgeExceptionHandler(KnowledgeException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getCode(), e.getMessage());
    }

    /**
     * 过关率检查异常
     */
    @ResponseBody
    @ExceptionHandler(value = UserPaperNotPassException.class)
    public Response<Serializable> userPaperNotPassException(UserPaperNotPassException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getCode(), e.getMessage());
    }


    /**
     * 三方用户服务异常
     */
    @ResponseBody
    @ExceptionHandler(value = ThirdPartyUserException.class)
    public Response<Serializable> thirdPartyUserExceptionHandler(ThirdPartyUserException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ResponseBody
    @ExceptionHandler(value = BizException.class)
    public Response<Serializable> bizExceptionHandler(BizException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(ClientAbortException.class)
    public void handleClientAbort(ClientAbortException ex) {
        log.error("客户端主动断开连接: {}", ex.getMessage());
    }
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public Response<Serializable> exceptionHandler(Exception e) {
        log.error(e.getMessage(), e);
        // 异步发送异常通知（仅在非本地环境）
        sendExceptionNotificationAsync(e);
        //不能把错误返回给前端
        //因为可以根据报错 知道服务器信息，方便注入攻击者调试
        return Response.fail("服务器开小差去了(((o(*ﾟ▽ﾟ*)o)))");
    }

    /**
     * 数字人异常
     */
    @ResponseBody
    @ExceptionHandler(value = AssistantException.class)
    public Response assistantExceptionHandler(AssistantException e) throws Exception {
        return getMessage(e);
    }

    /**
     * 租户消息异常
     */
    @ResponseBody
    @ExceptionHandler(value = TenantMessageException.class)
    public Response tenantMessageExceptionHandler(TenantMessageException e) throws Exception {
        return getMessage(e);
    }

    /**
     * 异步join异常
     */
    @ResponseBody
    @ExceptionHandler(value = CompletionException.class)
    public Response completionExceptionHandler(CompletionException e) throws Exception {
        return getCauseMessage(e);
    }

    /**
     * 异步发送异常通知
     */
    private void sendExceptionNotificationAsync(Exception exception) {
        // 在虚拟线程启动前获取请求上下文信息
        final ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        final String requestUrl;
        final String requestMethod;
        final String userAgent;
        final String requestId;
        final String serverName;

        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            requestUrl = request.getRequestURL().toString();
            requestMethod = request.getMethod();
            requestId = request.getAttribute("requestId")!=null?request.getAttribute("requestId").toString():"-";
            userAgent = request.getHeader("User-Agent");
            serverName = request.getServerName();
        } else {
            requestUrl = null;
            requestMethod = null;
            userAgent = null;
            requestId = null;
            serverName = null;
        }

        // 检查请求地址是否为本地地址，如果是则跳过异常通知
        if (isLocalHost(serverName)) {
            log.debug("本地请求地址 {} 触发的异常，跳过异常通知发送", serverName);
            return;
        }

        //过滤压测请求
        if (userAgent != null
                && userAgent.contains("Apache-HttpClient")
                && exception.getMessage().contains("Broken pipe")
                && !appEnv.equalsIgnoreCase("prod")) {
            return;
        }

        // 使用线程池中的虚拟线程异步发送异常通知，确保能够完成执行
        exceptionNotificationExecutor.submit(() -> {
            try {
                // 构建异常通知内容，使用预先获取的请求信息，并包含环境信息
                String notificationContent = ExceptionNotificationUtil.buildExceptionNotificationContent(
                    exception, requestUrl, requestMethod, userAgent, requestId, appEnv);

                // 发送到飞书（带Grafana按钮）
                boolean success = feishuBotService.sendErrorNotificationWithGrafanaButton(
                    "[" + ExceptionNotificationUtil.getEnvironmentDisplayName(appEnv) + "] " + exception.getClass().getSimpleName() + ": " + exception.getMessage(),
                    notificationContent,
                    requestId
                );

                developerFeishuApiService.processException(new DeveloperProcessRequest(appEnv,requestId,"",feishuBotService.buildGrafanaUrl(requestId),requestUrl,exception));

                if (success) {
                    log.info("异常通知发送成功");
                } else {
                    log.warn("异常通知发送失败");
                }
            } catch (Exception notificationException) {
                // 通知失败不影响主流程
                log.error("发送异常通知失败", notificationException);
            }
        });

        log.debug("异常通知任务已提交到线程池");
    }

    /**
     * 判断是否为本地请求地址
     */
    private boolean isLocalHost(String serverName) {
        if (serverName == null || serverName.trim().isEmpty()) {
            return false;
        }

        // 检查是否为本地请求地址
        for (String localHost : LOCAL_HOSTS) {
            if (localHost.equalsIgnoreCase(serverName)) {
                return true;
            }
        }

        return false;
    }

    private Response getMessage(Exception e) {
        if (StringUtils.hasText(e.getMessage())) {
            log.error(e.getMessage(), e);
            return Response.fail(e.getMessage());
        } else {
            return getCauseMessage(e);
        }
    }

    private Response getCauseMessage(Exception e) {
        if (e.getCause() != null && StringUtils.hasText(e.getCause().getMessage())) {
            log.error(e.getCause().getMessage(), e.getCause());
            return Response.fail(e.getCause().getMessage());
        } else {
            return Response.fail("服务器开小差去了(((o(*ﾟ▽ﾟ*)o)))");
        }
    }

}
