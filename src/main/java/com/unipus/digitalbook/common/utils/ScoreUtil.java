package com.unipus.digitalbook.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class ScoreUtil {

    public ScoreUtil(){
        throw new UnsupportedOperationException();
    }

    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100);

    /**
     * 保留两位小数
     * @param score 分数
     * @return 保留两位小数的分数
     */
    public static BigDecimal keepTwoDecimal(BigDecimal score) {
        if (score == null) {
            return null;
        }
        return score.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 百分制分数并保留一位小数
     * @param totalScore 总分
     * @param score 得分
     * @return 百分制分数
     */
    public static BigDecimal keepOneDecimalForPercent(BigDecimal totalScore, BigDecimal score) {
        if (score == null || totalScore == null) {
            return null;
        }
        if (totalScore.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return score.multiply(HUNDRED).divide(totalScore, 1, RoundingMode.HALF_UP);
    }
}
