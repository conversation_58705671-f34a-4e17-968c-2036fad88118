package com.unipus.digitalbook.common.utils;

import org.apache.commons.codec.digest.Blake3;
import org.bouncycastle.crypto.digests.Blake2bDigest;
import org.bouncycastle.util.Strings;
import org.bouncycastle.util.encoders.Hex;

import java.io.ByteArrayOutputStream;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.nio.ByteBuffer;

public class HexUtil {

    /**
     * 安全比较两个对象的哈希值（使用常量时间比较防止时序攻击）
     * @param object1 第一个对象
     * @param object2 第二个对象
     * @return 如果哈希值相同返回true，否则返回false
     */
    public boolean compareObjectHashesSecure(Serializable object1, Serializable object2) {
        String hash1 = generateObjectHash(object1);
        String hash2 = generateObjectHash(object2);

        if (hash1 == null || hash2 == null) {
            return false;
        }

        byte[] hash1Bytes = Hex.decode(hash1);
        byte[] hash2Bytes = Hex.decode(hash2);

        if (hash1Bytes.length != hash2Bytes.length) {
            return false;
        }

        int result = 0;
        for (int i = 0; i < hash1Bytes.length; i++) {
            result |= hash1Bytes[i] ^ hash2Bytes[i];
        }

        return result == 0;
    }



    /**
     * 使用Blake2b和快速序列化为对象生成哈希值
     * @param object 需要哈希的对象
     * @return 十六进制格式的哈希值
     */
    public static String generateObjectHash(Object object) {
        if (object == null) {
            return null;
        }
        byte[] objectBytes = switch (object) {
            case byte[] bytes -> bytes;
            case String s -> s.getBytes();
            case Number number -> numberToBytes(number);
            default -> objectToBytes(object);
        };
        return generateBlake2Hash(objectBytes);
    }


    /**
     * 将数字转换为字节数组
     * @param number 需要转换的数字
     * @return 字节数组
     */
    private static byte[] numberToBytes(Number number) {
        if (number instanceof Integer) {
            return ByteBuffer.allocate(4).putInt(number.intValue()).array();
        } else if (number instanceof Long) {
            return ByteBuffer.allocate(8).putLong(number.longValue()).array();
        } else if (number instanceof Float) {
            return ByteBuffer.allocate(4).putFloat(number.floatValue()).array();
        } else if (number instanceof Double) {
            return ByteBuffer.allocate(8).putDouble(number.doubleValue()).array();
        } else if (number instanceof Short) {
            return ByteBuffer.allocate(2).putShort(number.shortValue()).array();
        } else if (number instanceof Byte) {
            return new byte[]{number.byteValue()};
        } else {
            // 对于其他类型的数字，使用其字符串表示
            return number.toString().getBytes();
        }
    }

    /**
     * 使用反射将对象的字段值转换为字节数组
     * @param object 需要转换的对象
     * @return 字节数组
     */
    private static byte[] objectToBytes(Object object) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();

            // 获取对象的所有字段（包括私有字段）
            for (Field field : getAllFields(object.getClass())) {
                field.setAccessible(true);
                Object value = field.get(object);

                if (value != null) {
                    // 将字段名称写入流
                    baos.write(field.getName().getBytes());

                    // 将字段值写入流
                    switch (value) {
                        case String s -> baos.write(s.getBytes());
                        case Number number -> baos.write(numberToBytes(number));
                        case byte[] bytes -> baos.write(bytes);
                        case Boolean b -> baos.write(b.booleanValue() ? 1 : 0);
                        case Character c -> baos.write(ByteBuffer.allocate(2).putChar(c).array());
                        default ->
                            // 对于复杂对象，递归调用
                                baos.write(objectToBytes(value));
                    }
                }
            }

            return baos.toByteArray();
        } catch (Exception e) {
            // 如果反射失败，回退到对象的toString方法
            return object.toString().getBytes();
        }
    }

    /**
     * 获取类及其所有父类的所有字段
     * @param clazz 需要获取字段的类
     * @return 所有字段的数组
     */
    private static Field[] getAllFields(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        if (clazz.getSuperclass() != null) {
            Field[] parentFields = getAllFields(clazz.getSuperclass());
            Field[] allFields = new Field[fields.length + parentFields.length];
            System.arraycopy(fields, 0, allFields, 0, fields.length);
            System.arraycopy(parentFields, 0, allFields, fields.length, parentFields.length);
            return allFields;
        }
        return fields;
    }

    /**
     * 使用Blake2b生成哈希值
     * @param data 需要哈希的数据
     * @return 十六进制格式的哈希值
     */
    private static String generateBlake2Hash(byte[] data) {
        Blake2bDigest digest = new Blake2bDigest(null, 64, null, null);
        digest.update(data, 0, data.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return Hex.toHexString(hash);
    }

    private static String generateBlake3Hash(byte[] data) {
        return Strings.fromByteArray(Blake3.hash(data));
    }
}
