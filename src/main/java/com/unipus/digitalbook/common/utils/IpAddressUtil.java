package com.unipus.digitalbook.common.utils;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class IpAddressUtil {

    private IpAddressUtil(){}

    /**
     * 判断IP地址是否属于指定的子网
     *
     * @param ipAddress IP地址，例：***********99
     * @param cidr 子网地址，格式为 "***********/24"
     * @return true 如果IP地址属于子网，否则false
     */
    public static boolean isIpInSubnet(String ipAddress, String cidr) {
        try {
            // 分割CIDR格式为IP地址和子网掩码位数
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                throw new IllegalArgumentException("无效的CIDR格式");
            }

            String subnetIp = parts[0];
            int maskBits = Integer.parseInt(parts[1]);

            if (maskBits < 0 || maskBits > 32) {
                throw new IllegalArgumentException("子网掩码位数必须在0到32之间");
            }

            // 将IP地址转换为整数
            int ipAddressInt = ipToInt(ipAddress);
            int subnetIpInt = ipToInt(subnetIp);

            // 计算子网掩码
            int mask = 0;
            if (maskBits > 0) {
                mask = -1 << (32 - maskBits);
            }

            // 应用子网掩码并比较
            return (ipAddressInt & mask) == (subnetIpInt & mask);
        } catch (Exception e) {
            throw new IllegalArgumentException("IP地址或子网格式无效", e);
        }
    }

    /**
     * 将IP地址字符串转换为整数表示
     *
     * @param ip IP地址字符串，例如 "***********"
     * @return IP地址的整数表示
     */
    private static int ipToInt(String ip) {
        String[] octets = ip.split("\\.");
        if (octets.length != 4) {
            throw new IllegalArgumentException("无效的IP地址格式");
        }

        int result = 0;
        for (int i = 0; i < 4; i++) {
            int octet = Integer.parseInt(octets[i]);
            if (octet < 0 || octet > 255) {
                throw new IllegalArgumentException("IP地址的每个段必须在0到255之间");
            }
            result = (result << 8) | octet;
        }
        return result;
    }

    /**
     * 取得远程地址（非本机地址）
     * @param clientIps 客户端IP
     * @return true 表示是内网IP，否则false
     */
    public static Set<String> getRemoteIpList(Set<String> clientIps) {
        if(CollectionUtils.isEmpty(clientIps)){
            return Set.of();
        }
        return clientIps.stream().filter(ip-> !IpAddressUtil.isLocalHostIp(ip)).collect(Collectors.toSet());
    }

    /**
     * 是否本地地址
     * @param clientIp ip地址
     * @return true/false
     */
    public static Boolean isLocalHostIp(String clientIp) {
        try {
            // 0.快速判断回环地址
            if(isLoopbackFast(clientIp)){
                return true;
            }

            // 1. 严格检查是否是回环地址
            if (isLoopbackAddressStrict(clientIp)) {
                return true;
            }

            // 2. 获取服务器所有 IP 地址（包括 IPv4 和 IPv6）
            InetAddress localhost = InetAddress.getLocalHost();
            InetAddress[] serverInetAddresses = InetAddress.getAllByName(localhost.getHostName());

            // 3. 支持多网卡服务器，检查 clientIp 是否匹配服务器的任何一个 IP
            for (InetAddress serverAddr : serverInetAddresses) {
                // 使用 equalsIgnoreCase 避免 IPv6 地址格式差异问题
                if (clientIp.equalsIgnoreCase(serverAddr.getHostAddress())) {
                    return true;
                }
            }

            return false;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 快速判断（使用字符串匹配）
     */
    public static boolean isLoopbackFast(String ip) {
        // 1. 快速判断 IPv4 回环地址
        if (ip.startsWith("127.")) {
            return true;
        }
        // 2. 快速判断 IPv6 回环地址
        return ip.equals("::1");
    }

    /**
     * 更严谨的判断（使用 InetAddress 解析）
     */
    public static boolean isLoopbackAddressStrict(String ip) {
        try {
            return InetAddress.getByName(ip).isLoopbackAddress();
        } catch (UnknownHostException e) {
            return false; // 无效 IP 默认不算回环地址
        }
    }

    // 定义了常见的代理IP头部。顺序很重要，X-Forwarded-For 是最标准的。
    private static final List<String> IP_HEADER_CANDIDATES = Arrays.asList(
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_X_FORWARDED_FOR",
            "HTTP_CLIENT_IP",
            "X-Real-IP"
    );

    private static final String UNKNOWN_ADDRESS = "unknown";
    private static final String IPV4_LOOP_IP = "127.0.0.1";
    private static final List<String> IPV6_LOOP_IP_TAGS = Arrays.asList("0:0:0:0:0:0:0:1", "::1");

    /**
     * 在 K8s 集群中正确获取请求源的真实 IP 地址。
     * 此方法考虑了 Ingress、Service Mesh (Sidecar) 等多层代理的情况。
     *
     * @param request 请求对象
     * @return 客户端的真实 IP 地址
     */
    public static String getLastIpAddress(HttpServletRequest request) {
        return getIpAddressSet(request).stream().findFirst().orElse(UNKNOWN_ADDRESS);
    }

    /**
     * 在 K8s 集群中正确获取请求源的真实 IP 地址。
     * 此方法考虑了 Ingress、Service Mesh (Sidecar) 等多层代理的情况。
     *
     * @param request 请求对象
     * @return 客户端的真实 IP 地址
     */
    public static Set<String> getIpAddressSet(HttpServletRequest request) {
        Set<String> ipSet = new HashSet<>();

        // 1. 遍历所有可能的HTTP Header来寻找IP
        for (String header : IP_HEADER_CANDIDATES) {
            String ipListStr = request.getHeader(header);
            if (!isIpAddressValid(ipListStr)) {
                continue;
            }
            System.out.println(MessageFormat.format("ACCESS_IP[{0}: {1}]", header, ipListStr));

            // X-Forwarded-For 格式为 "client, proxy1, proxy2"
            // 提取所有有效的IP并添加到列表中
            Arrays.stream(ipListStr.split(","))
                    .map(String::trim)
                    .filter(IpAddressUtil::isIpAddressValid)
                    .forEach(ipSet::add);
        }
        if (!ipSet.isEmpty()) {
            System.out.println("IP_SET: " + ipSet);
            return ipSet;
        }

        // 2. 如果所有Header都没有，最后的兜底方法才是 getRemoteAddr(), 在有Sidecar的环境下，这通常会返回 127.0.0.1，但这是没有办法的办法。
        return Set.of(getLocalIpAddress(request));
    }

    /**
     * 如果所有Header都没有，最后的兜底方法才是 getRemoteAddr(),
     * 在有Sidecar的环境下，这通常会返回 127.0.0.1，但这是没有办法的办法。
     * @param request 请求对象
     * @return 客户端的本地 IP 地址
     */
    private static String getLocalIpAddress(HttpServletRequest request) {
        String remoteAddr = request.getRemoteAddr();

        // 特殊处理本地IPv6环回地址，并尝试解析主机IP（尽管在K8s中这可能不是客户端IP）
        if (IPV6_LOOP_IP_TAGS.contains(remoteAddr)) {
            try {
                // 在容器化环境中，InetAddress.getLocalHost().getHostAddress() 可能无法准确获取客户端IP。
                // 更好的做法是结合环境变量或K8s元数据服务来确定Pod IP，但需要额外配置。
                InetAddress localIp = InetAddress.getLocalHost();
                remoteAddr = !localIp.isLoopbackAddress() ? localIp.getHostAddress() : IPV4_LOOP_IP;
            } catch (UnknownHostException e) {
                // 如果本地主机信息不可用，则直接回退到环回地址
                remoteAddr = IPV4_LOOP_IP;
            }
        }
        System.out.println("LOCAL_IP: " + remoteAddr);
        return remoteAddr;
    }

    /**
     * 简单的IP地址格式校验（非空且不是 "unknown"）
     * @param ip IP地址字符串
     * @return 是否有效
     */
    private static boolean isIpAddressValid(String ip) {
        return StringUtils.hasText(ip) && !UNKNOWN_ADDRESS.equalsIgnoreCase(ip);
    }
}