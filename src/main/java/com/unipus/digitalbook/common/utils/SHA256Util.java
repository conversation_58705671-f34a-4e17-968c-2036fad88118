package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.common.exception.business.BizException;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class SHA256Util {
    private SHA256Util() {
        // 私有构造方法，防止实例化
    }

    /**
     * 计算字符串的 SHA-256 哈希值
     *
     * @param input 待加密的字符串
     * @return 加密后的十六进制字符串
     */
    public static String hash(String input) {
        try {
            // 获取 SHA-256 算法实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 计算哈希值
            byte[] hashBytes = digest.digest(input.getBytes());

            // 将字节数组转换为十六进制字符串
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new BizException("SHA-256 algorithm not found");
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0'); // 补零
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
