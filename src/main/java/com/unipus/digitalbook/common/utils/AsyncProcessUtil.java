package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.common.exception.business.BizException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 通用异步处理工具类
 * 提供统一的同步/异步处理机制
 *
 * @param <T> 处理结果类型
 */
@Component
@Slf4j
public class AsyncProcessUtil<T> {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private VirtualThreadPoolManager virtualThreadPoolManager;

    @Resource
    private TransactionTemplate transactionTemplate;

    private static final long REDIS_EXPIRE_SECONDS = 300L;
    private static final String REDIS_KEY_PREFIX = "ASYNC_PROCESS:";
    private static final String PROCESS_STATE_RUNNING = "running";
    private static final String PROCESS_STATE_FINISHED = "finished";
    private static final String PROCESS_STATE_ERROR = "error";

    /**
     * 执行处理任务，支持同步或异步模式
     *
     * @param async          是否同步执行: false为同步执行，true为异步执行
     * @param identifier     标识符，用于日志记录（如实例ID、任务ID等）
     * @param task           要执行的任务
     * @param defaultResult  异步模式下返回的默认结果
     * @return 处理结果
     */
    public T process(boolean async, String identifier, Supplier<T> task, T defaultResult) {
        if(isProcessRunning(identifier)){
            log.debug("任务处理中，请稍后再试: {}", identifier);
            throw new BizException("任务处理中，请稍后再试");
        }

        if (async) {
            // 异步执行 - 使用统一的虚拟线程池管理器
            virtualThreadPoolManager.executeAsync(() -> {
                try {
                    setProcessState(identifier, PROCESS_STATE_RUNNING);
                    // 在事务中执行任务
                    executeTaskWithTransaction(task);
                    setProcessState(identifier, PROCESS_STATE_FINISHED);
                    log.debug("异步任务完成: {}", identifier);
                } catch (Exception e) {
                    setProcessState(identifier, PROCESS_STATE_ERROR);
                    log.error("异步任务执行异常: {}", identifier, e);
                }
            });

            // 异步模式下返回默认值
            return defaultResult;
        } else {
            // 同步执行
            try {
                setProcessState(identifier, PROCESS_STATE_RUNNING);
                // 在事务中执行任务
                return executeTaskWithTransaction(task);
            } catch (Exception e) {
                log.error("同步任务执行异常: {}", identifier, e);
                throw new RuntimeException(e);
            }finally {
                setProcessState(identifier, PROCESS_STATE_FINISHED);
            }
        }
    }

    /**
     * 在事务中执行任务
     *
     * @param task 要执行的任务
     * @return 任务执行结果
     */
    private T executeTaskWithTransaction(Supplier<T> task) {
        return transactionTemplate.execute(status -> {
            try {
                log.debug("开始在事务中执行任务");
                T result = task.get();
                log.debug("事务中任务执行成功");
                return result;
            } catch (Exception e) {
                log.error("事务中任务执行失败，将回滚事务", e);
                status.setRollbackOnly();
                throw new RuntimeException("任务执行失败", e);
            }
        });
    }

    /**
     * 检查处理任务是否正在运行
     *
     * @param identifier 标识符
     * @return true表示正在运行，false表示未运行
     */
    public boolean isProcessRunning(String identifier) {
        return PROCESS_STATE_RUNNING.equals(getProcessState(identifier));
    }

    private String getProcessState(String identifier) {
        return stringRedisTemplate.opsForValue().get(getRedisKey(identifier));
    }

    private void setProcessState(String identifier, String state) {
        stringRedisTemplate.opsForValue().set(getRedisKey(identifier), state, REDIS_EXPIRE_SECONDS, TimeUnit.SECONDS);
    }

    private String getRedisKey(String identifier) {
        return REDIS_KEY_PREFIX + identifier;
    }
}