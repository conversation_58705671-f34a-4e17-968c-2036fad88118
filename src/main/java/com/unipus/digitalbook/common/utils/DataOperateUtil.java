package com.unipus.digitalbook.common.utils;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.Instant;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 数据操作工具类
 */
@Component
@Slf4j
public class DataOperateUtil {

    @Resource
    private RedissonClient redissonClient;

    @Value("${app.env}")
    private String env;

    private static final long LOCK_WAIT_TIME = 5L;
    private static final long LOCK_TIMEOUT = 5L;

    private static final String LOCK_KEY_PREFIX = "DataOperate:";

    private String getLockKey(String lockKey) {
        return env + ":" + LOCK_KEY_PREFIX + lockKey;
    }

    /**
     * 单条数据保存或者更新处理，带有分布式锁，锁粒度最小
     *  - 锁粒度最小
     *  - 如果未取得锁，立即返回
     *  - 锁超时（默认5秒），释放锁并返回
     *  - 锁成功，执行检索逻辑，
     * @param entity 实体对象
     * @param lockKeyFn 生成锁 key
     * @param selectFn 查询函数（返回值 true：存在历史数据/false：不存在历史数据）
     * @param insertFn 插入函数
     * @param updateFn 更新函数
     */
    public <T> void saveOrUpdateWithLock(
            T entity,
            Function<T, String> lockKeyFn,   // 生成锁 key
            Function<T, Integer> selectFn,   // 查询函数
            Consumer<T> insertFn,            // 插入函数
            Consumer<T> updateFn             // 更新函数
    ) {
        if(entity == null || lockKeyFn == null || selectFn == null || insertFn == null){
            throw new IllegalArgumentException("参数错误");
        }

        Instant startTime = Instant.now();
        String lockKey = getLockKey(lockKeyFn.apply(entity));
        RLock lock = redissonClient.getLock(lockKey);

        try {
            if (!lock.tryLock(LOCK_WAIT_TIME, LOCK_TIMEOUT, TimeUnit.SECONDS)) {
                throw new RuntimeException("获取锁失败: " + lockKey);
            }
            Integer count = selectFn.apply(entity);
            if (count==null || count == 0) {
                insertFn.accept(entity);
            } else {
                if (updateFn != null){
                    updateFn.accept(entity);
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取锁被中断: " + lockKey, e);
        } catch (Exception e) {
            throw new RuntimeException("数据操作异常: " + e.getMessage(), e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            Long elapsedTime = Instant.now().getMillis() - startTime.getMillis();
            log.debug("数据处理完成，耗时: {}ms，释放锁: {}", elapsedTime, lockKey);
        }
    }
}
