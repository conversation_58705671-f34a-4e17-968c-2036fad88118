package com.unipus.digitalbook.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
public class UrlUtil {

    /**
     * 使用 UriComponentsBuilder 提取域名部分
     *
     * @param url 完整的 URL 字符串
     * @return 域名部分（包含协议和端口）
     */
    public static String getDomainFromUrl(String url) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
        String scheme = builder.build().getScheme(); // 获取协议
        String host = builder.build().getHost(); // 获取主机名
        int port = builder.build().getPort(); // 获取端口号

        // 拼接域名部分
        String domain = scheme + "://" + host;
        if (port != -1) { // 如果端口号不是默认端口，添加端口号
            domain += ":" + port;
        }

        return domain;
    }

    public static void main(String[] args) {
        String url = "https://example.com:8080/path/to/resource";
        String domain = getDomainFromUrl(url);
        log.info("Extracted domain: " + domain); // 输出: Extracted domain: https://example.com:8080
    }
}
