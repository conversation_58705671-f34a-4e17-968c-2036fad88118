package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.service.common.DeadlockPreventionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 数据库操作工具类
 * 提供安全的数据库操作方法，防止死锁
 * 
 * <AUTHOR>
 */
@Component
public class DatabaseOperationUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseOperationUtils.class);
    
    @Autowired
    private DeadlockPreventionService deadlockPreventionService;
    
    /**
     * 默认批次大小
     */
    private static final int DEFAULT_BATCH_SIZE = 100;
    
    /**
     * 安全的批量插入
     * 
     * @param items 要插入的数据列表
     * @param insertFunction 插入函数，返回插入的记录数
     * @param <T> 数据类型
     * @return 总插入记录数
     */
    public <T> int safeBatchInsert(List<T> items, Function<List<T>, Integer> insertFunction) {
        return safeBatchInsert(items, DEFAULT_BATCH_SIZE, insertFunction);
    }
    
    /**
     * 安全的批量插入（指定批次大小）
     * 
     * @param items 要插入的数据列表
     * @param batchSize 批次大小
     * @param insertFunction 插入函数，返回插入的记录数
     * @param <T> 数据类型
     * @return 总插入记录数
     */
    public <T> int safeBatchInsert(List<T> items, int batchSize, Function<List<T>, Integer> insertFunction) {
        if (items == null || items.isEmpty()) {
            return 0;
        }
        
        logger.debug("Starting safe batch insert for {} items with batch size {}", items.size(), batchSize);
        
        final int[] totalInserted = {0};
        
        deadlockPreventionService.executeBatchOperation(items, batchSize, batch -> {
            int inserted = deadlockPreventionService.executeInTransaction(() -> insertFunction.apply(batch));
            totalInserted[0] += inserted;
            logger.debug("Inserted {} records in current batch", inserted);
        });
        
        logger.info("Safe batch insert completed. Total inserted: {}", totalInserted[0]);
        return totalInserted[0];
    }
    
    /**
     * 安全的批量更新
     * 
     * @param ids 要更新的ID列表
     * @param updateFunction 更新函数，返回更新的记录数
     * @return 总更新记录数
     */
    public int safeBatchUpdate(List<Long> ids, Function<List<Long>, Integer> updateFunction) {
        return safeBatchUpdate(ids, DEFAULT_BATCH_SIZE, updateFunction);
    }
    
    /**
     * 安全的批量更新（指定批次大小）
     * 
     * @param ids 要更新的ID列表
     * @param batchSize 批次大小
     * @param updateFunction 更新函数，返回更新的记录数
     * @return 总更新记录数
     */
    public int safeBatchUpdate(List<Long> ids, int batchSize, Function<List<Long>, Integer> updateFunction) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        
        logger.debug("Starting safe batch update for {} IDs with batch size {}", ids.size(), batchSize);
        
        final int[] totalUpdated = {0};
        
        deadlockPreventionService.safeBatchUpdate(ids, batchSize, batch -> {
            int updated = deadlockPreventionService.executeInTransaction(() -> updateFunction.apply(batch));
            totalUpdated[0] += updated;
            logger.debug("Updated {} records in current batch", updated);
        });
        
        logger.info("Safe batch update completed. Total updated: {}", totalUpdated[0]);
        return totalUpdated[0];
    }
    
    /**
     * 安全的批量删除
     * 
     * @param ids 要删除的ID列表
     * @param deleteFunction 删除函数，返回删除的记录数
     * @return 总删除记录数
     */
    public int safeBatchDelete(List<Long> ids, Function<List<Long>, Integer> deleteFunction) {
        return safeBatchDelete(ids, DEFAULT_BATCH_SIZE, deleteFunction);
    }
    
    /**
     * 安全的批量删除（指定批次大小）
     * 
     * @param ids 要删除的ID列表
     * @param batchSize 批次大小
     * @param deleteFunction 删除函数，返回删除的记录数
     * @return 总删除记录数
     */
    public int safeBatchDelete(List<Long> ids, int batchSize, Function<List<Long>, Integer> deleteFunction) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        
        logger.debug("Starting safe batch delete for {} IDs with batch size {}", ids.size(), batchSize);
        
        final int[] totalDeleted = {0};
        
        deadlockPreventionService.safeBatchUpdate(ids, batchSize, batch -> {
            int deleted = deadlockPreventionService.executeInTransaction(() -> deleteFunction.apply(batch));
            totalDeleted[0] += deleted;
            logger.debug("Deleted {} records in current batch", deleted);
        });
        
        logger.info("Safe batch delete completed. Total deleted: {}", totalDeleted[0]);
        return totalDeleted[0];
    }
    
    /**
     * 安全执行单个操作
     * 
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T safeExecute(Supplier<T> operation) {
        return deadlockPreventionService.executeWithDeadlockRetry(operation);
    }
    
    /**
     * 安全执行事务操作
     * 
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T safeTransactionExecute(Supplier<T> operation) {
        return deadlockPreventionService.executeInTransaction(operation);
    }
    
    /**
     * 计算推荐的批次大小
     * 
     * @param totalSize 总数据量
     * @param complexity 操作复杂度（1-简单，2-中等，3-复杂）
     * @return 推荐的批次大小
     */
    public int calculateBatchSize(int totalSize, int complexity) {
        int baseBatchSize = deadlockPreventionService.getRecommendedBatchSize(totalSize);
        
        // 根据操作复杂度调整批次大小
        switch (complexity) {
            case 1: // 简单操作
                return Math.min(baseBatchSize * 2, 500);
            case 2: // 中等复杂度
                return baseBatchSize;
            case 3: // 复杂操作
                return Math.max(baseBatchSize / 2, 20);
            default:
                return baseBatchSize;
        }
    }
}