package com.unipus.digitalbook.task;

import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Sets;
import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.common.utils.ExceptionNotificationUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.common.utils.LockUtil;
import com.unipus.digitalbook.model.entity.tenant.TenantMessage;
import com.unipus.digitalbook.model.enums.ChannelEnum;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.enums.ProduceResultEnum;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantMessagePO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import com.unipus.digitalbook.service.FeishuBotService;
import com.unipus.digitalbook.service.TenantChannelService;
import com.unipus.digitalbook.service.TenantMessageService;
import com.unipus.digitalbook.service.TenantSubscribeService;
import com.unipus.digitalbook.service.processor.tenant.MessageProcessor;
import com.unipus.digitalbook.service.processor.tenant.MessageProcessorFactory;
import com.unipus.digitalbook.service.remote.restful.developer.DeveloperFeishuApiService;
import com.unipus.digitalbook.service.remote.restful.developer.model.DeveloperSyncRequest;
import com.unipus.digitalbook.service.validator.tenant.MessageValidatorFactory;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

@Service
@Slf4j
public class TenantMessageProducerTask {

    @Resource
    private LockUtil lockUtil;

    @Resource
    private TenantMessageService tenantMessageService;

    @Resource
    private TenantSubscribeService tenantSubscribeService;

    @Value("${task.limit:100}")
    private Integer limit;

    @Resource
    private MessageProcessorFactory messageProcessorFactory;

    @Value("${kafka.topic.tenantMessageSaveTopic}")
    private String tenantMessageSaveTopic;

    @Value("${kafka.topic.tenantMessageDeleteTopic}")
    private String tenantMessageDeleteTopic;

    @Resource
    private TenantChannelService tenantChannelService;

    @Resource
    private MessageValidatorFactory messageValidatorFactory;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Value("${app.env}")
    private String appEnv;

    @Resource
    private FeishuBotService feishuBotService;
    @Resource
    private DeveloperFeishuApiService developerFeishuApiService;

    @Resource
    @Qualifier("exceptionNotificationExecutor")
    private ExecutorService exceptionNotificationExecutor;

    private final String feishuMessage = "租户消息messageUuid=%s发送失败，异常信息：%s";

//    @Scheduled(fixedDelayString = "${task.fixedDelay:60000}")
    public void executeTask() {
        List<TenantSubscribePO> tenantSubscribes = tenantSubscribeService.selectAll();
        tenantSubscribes
            .forEach(tenantSubscribe -> {
                Long tenantId = tenantSubscribe.getTenantId();
                String messageTopic = tenantSubscribe.getMessageTopic();
                lockUtil.tryLock(getLockKey(tenantId, messageTopic),
                1, () -> {
                    log.info("tenantId:{}发送消息:{}任务执行开始", tenantId, messageTopic);
                    try {
                        execute(tenantId, messageTopic);
                    } catch (Exception e) {
                        log.error("租户发送消息异常", e);
                    }
                    log.info("tenantId:{}发送消息:{}任务执行结束", tenantId, messageTopic);
            });
        });
    }

    private String getLockKey(Long tenantId, String messageTopic) {
        return "TenantMessageProducerTask-" + tenantId + "-" + messageTopic;
    }

    @SneakyThrows
    private void execute(Long tenantId, String messageTopic) {

        doBatch(id -> tenantMessageService.selectByTenantIdAndMessageTopicLimitFromId(tenantId, messageTopic, id, limit), this::sendMessages);

    }

    @SneakyThrows
    private Long sendMessages(List<TenantMessagePO> tenantMessagePOs) {

        tenantMessagePOs.forEach(tenantMessagePO -> {
            MessageTopicEnum messageTopicEnum = MessageTopicEnum.fromMessageTopic(tenantMessagePO.getMessageTopic());
            sendMessage(tenantMessagePO.toEntity(messageTopicEnum.getSourceType()), messageTopicEnum.getTargetType());
        });

        return tenantMessagePOs.reversed().stream().findFirst().map(TenantMessagePO::getId).orElse(null);

    }

    private void doBatch(Function<Long, List<TenantMessagePO>> dataGetter, Function<List<TenantMessagePO>, Long> dataHandler) {
        int i = 0;
        Long fromId = 0L;
        do {
            List<TenantMessagePO> dataList = dataGetter.apply(fromId);
            if (CollectionUtils.isEmpty(dataList)) {
                log.info("分批次查询结果为空，退出批次循环");
                break;
            }
            fromId = dataHandler.apply(dataList);
            log.info("本批次数据处理完成，批次号[{}]", ++i);
        } while (true);
    }

    public <S, T> T sendMessage(TenantMessage<S> tenantMessage, TypeReference<T> targetType) {

        Long tenantId = tenantMessage.getTenantId();
        String messageTopic = tenantMessage.getMessageTopic();
        S message = tenantMessage.getMessageObj();
        String messageUuid = tenantMessage.getMessageUuid();

        TenantSubscribePO tenantSubscribePO = tenantSubscribeService.selectByTenantIdAndMessageTopic(tenantId, messageTopic);
        if (tenantSubscribePO == null) {
            log.error("tenantId {} not subscribe messageTopic {}", tenantId, messageTopic);
            throw new TenantMessageException(ProduceResultEnum.SUBSCRIBE_NOT_FOUND.getMessage());
        }

        Map<Integer, TenantChannelPO> channelMap = tenantChannelService.selectByTenantIdAndMessageTopic(tenantId, messageTopic);
        List<ChannelEnum> channels = channelMap.values().stream().sorted(Comparator.comparing(TenantChannelPO::getPriority).thenComparing(TenantChannelPO::getId).reversed())
                .map(tenantChannelPO -> ChannelEnum.fromCode(tenantChannelPO.getChannel())).toList();

        if (channels.isEmpty()) {
            log.error("tenantId {} not config messageTopic {} channel", tenantId, messageTopic);
            throw new TenantMessageException(ProduceResultEnum.CHANNEL_NOT_FOUND.getMessage());
        }

        Set<String> errorMessages = Sets.newHashSet();

        for (ChannelEnum channel : channels) {
            try {
                TenantChannelPO tenantChannelPO = channelMap.get(channel.getCode());
                messageValidatorFactory.getValidator(channel).validate(tenantSubscribePO, tenantChannelPO);
                MessageProcessor messageProcessor = messageProcessorFactory.getProcessor(channel);
                T result = messageProcessor.process(messageUuid, tenantSubscribePO, tenantChannelPO, message, targetType);
                deleteMessage(tenantMessage);
                return result;
            } catch (Exception e) {
                updateMessage(tenantMessage, e);
                log.error("messageUuid:{}", messageUuid, e);
                errorMessages.add(e.getMessage());
            }
        }

        throw new TenantMessageException(String.join(",", errorMessages));
    }

    private <S> void deleteMessage(TenantMessage<S> tenantMessage) {
        Long messageId = tenantMessage.getId();
        if (messageId != null) {
            Long tenantId = tenantMessage.getTenantId();
            String messageTopic = tenantMessage.getMessageTopic();
            try {
                kafkaTemplate.send(tenantMessageDeleteTopic, getKafkaKey(tenantId, messageTopic), String.valueOf(messageId));
            } catch (Exception e) {
                log.error("Failed to send Kafka topic: {}, messageId: {}", tenantMessageDeleteTopic, messageId, e);
                tenantMessageService.deleteById(messageId);
            }
        }
    }

    private <S> void updateMessage(TenantMessage<S> tenantMessage, Exception exception) {
        Long messageId = tenantMessage.getId();
        Boolean async = tenantMessage.getAsync();
        if (messageId != null && Objects.equals(async, Boolean.TRUE)) {
            tenantMessageService.updateRetryTimes(messageId);
            Optional.ofNullable(tenantMessageService.selectById(messageId)).ifPresent(po -> {
                Integer retryTimes = po.getRetryTimes();
                if (retryTimes >= 3) {
                    sendFeishuBotNotification(tenantMessage);
                }
            });
        } else if (Objects.equals(async, Boolean.FALSE)) {
            sendFeishuBotNotification(tenantMessage);
        }
    }

    public <S> void saveMessage(TenantMessage<S> tenantMessage) {
        if (tenantMessage.getId() == null) {
            Long tenantId = tenantMessage.getTenantId();
            String messageTopic = tenantMessage.getMessageTopic();
            TenantMessagePO tenantMessagePO = tenantMessage.toPO();
            String kafkaMessage = JsonUtil.writeValueAsString(tenantMessagePO);
            try {
                kafkaTemplate.send(tenantMessageSaveTopic, getKafkaKey(tenantId, messageTopic), kafkaMessage);
            } catch (Exception e) {
                log.error("Failed to send Kafka topic: {}, kafkaMessage: {}", tenantMessageSaveTopic, kafkaMessage, e);
                tenantMessageService.insertMessage(tenantMessagePO);
            }
        }
    }

    private String getKafkaKey(Long tenantId, String messageTopic) {
        return tenantId + "-" + messageTopic;
    }

    private void sendFeishuBotNotification(TenantMessage<?>  tenantMessage) {

        // 使用线程池中的虚拟线程异步发送异常通知，确保能够完成执行
        exceptionNotificationExecutor.submit(() -> {
            try {
                // 构建异常通知内容，使用预先获取的请求信息，并包含环境信息
                String notificationContent = ExceptionNotificationUtil.buildFailedSyncNotificationContent(tenantMessage,appEnv);

                // 发送到飞书（带Grafana按钮）
                boolean success = feishuBotService.sendErrorNotificationWithGrafanaButton(
                        "[" + ExceptionNotificationUtil.getEnvironmentDisplayName(appEnv) + "]\n 租户：" + tenantMessage.getTenantId() + "\n topic：" + tenantMessage.getMessageTopic(),
                        notificationContent,
                        tenantMessage.getMessageUuid(),"【同步】租户消息同步失败","wathet"
                );
                //同步飞速项目
                developerFeishuApiService.syncFail(new DeveloperSyncRequest(tenantMessage,ExceptionNotificationUtil.getEnvironmentDisplayName(appEnv)));
                if (success) {
                    log.info("异常通知发送成功");
                } else {
                    log.warn("异常通知发送失败");
                }
            } catch (Exception notificationException) {
                // 通知失败不影响主流程
                log.error("发送异常通知失败", notificationException);
            }

            log.debug("异常通知任务已提交到线程池");
        });
    }
}
