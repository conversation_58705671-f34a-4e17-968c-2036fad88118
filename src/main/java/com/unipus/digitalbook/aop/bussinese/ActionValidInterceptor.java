package com.unipus.digitalbook.aop.bussinese;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.HmacOneTimePasswordGenerator;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.constants.WebConstant;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.util.Arrays;

/**
 * Controller层用户操作验证拦截器
 */
@Component
@Slf4j
public class ActionValidInterceptor implements HandlerInterceptor {

    private static final String VERIFICATION_HEADER = "v";
    private static final String SECOND_KEY = "Authorization";

    @Resource
    private RequestBodyParser requestBodyParser;


    @Override
    public boolean preHandle(@NonNull HttpServletRequest request,@NonNull HttpServletResponse response,
                             @NonNull Object handler) {

        try {

            // Extract verification header
            String providedHash = request.getHeader(VERIFICATION_HEADER);
            if (!StringUtils.hasText(providedHash)) {
                handleViolation("Missing verification header", request,response);
                return false;
            }

            // Extract OpenID from JWT stored in request attributes (used as unique salt)
            String openId = extractUserId(request);
            if (!StringUtils.hasText(openId)) {
                handleViolation("Missing or invalid OpenID", request,response);
                return false;
            }
            String key = request.getHeader(SECOND_KEY);
            int i = getI(openId);
            byte[] keyBytes= getASCIIKey(key,  i % 10 + 4);
            // Parse request body and extract fields
            RequestBodyParser.RequestFields fields = extractRequestFields(request);
            if (!fields.hasRequiredFields()) {
                handleViolation("Missing required fields 'a' or 'b' in request body", request,response);
                return false;
            }

            // Generate expected hash using OpenID as unique salt and validate
            HmacOneTimePasswordGenerator generator = new HmacOneTimePasswordGenerator();

            String expectedHash = generator.generateOneTimePasswordString(new SecretKeySpec(keyBytes, "HmacSHA1"), fields.fieldA());
            if (!expectedHash.equals(providedHash)) {
                log.debug("key: {}, i : {}, keyBytes: {}, hash: {}", key, i, keyBytes, expectedHash);
                handleViolation("Hash verification failed - potential cheating detected", request,response);
                return false;
            }

            log.debug("Anti-cheat verification passed for OpenID: {} on path: {}",
                    openId, request.getRequestURI());
            return true;

        } catch (Exception e) {
            log.error("Error during anti-cheat verification", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return false;
        }
    }

    private int getI(String openId) {
        int i = 0;
        for (char c : openId.toCharArray()){
            i=i+c;
        }
        return i;
    }
    private String extractUserId(HttpServletRequest request) {
        Object openIdObj = request.getAttribute(WebConstant.JWT_BACKEND_OID);
        return openIdObj != null ? openIdObj.toString() : null;
    }

    private RequestBodyParser.RequestFields extractRequestFields(HttpServletRequest request) throws IOException {
        return requestBodyParser.extractFields(request);
    }

    //todo 改造一下，变成写response的方式
    private void handleViolation(String reason, HttpServletRequest request, HttpServletResponse response) {
        String openId = extractUserId(request);
        String path = request.getRequestURI();

        log.warn("Anti-cheat violation detected - Reason: {}, OpenID: {}, Path: {}, IP: {}",
                reason, openId, path, getClientIpAddress(request));
        try {
            Response.writeErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "涉嫌非法工具，已记录。");
        } catch (IOException e) {
            throw new BizException(e.getMessage());
        }
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    public static byte[] getASCIIKey(String str, int len) {
        int startIndex = (str.length() - len) >> 1;
        byte[] result = new byte[len];
        for (int i = 0; i < len; i++) {
            char c = str.charAt(startIndex + i);
            result[i] = (byte) (c & 0xFF);
        }
        return result;
    }

    public static void main(String[] args) throws InvalidKeyException {
        String openId = "********************************";
        String key = "Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMWVjODk1OWE2NDk0YWI0YTdhOGQzOWVkNzdmMWQ4MSIsImJzaWQiOiJhY1U1Y2E1eVJNdXhwTGJoN0JsRXBBIiwiYmFpZCI6MSwiYnJpZCI6MSwiYm9pZCI6IjExZWM4OTU5YTY0OTRhYjRhN2E4ZDM5ZWQ3N2YxZDgxIiwiaXNzIjoic2VsZiIsImVwaWQiOiJjb3Vyc2UtdjM6N2VkYjMyOTc2MDA1MGQ4KzA2MTZjc2pjKzIwMjUwNjE2MDY1MTAyIiwiZXhwIjoxNzUyODMwODEzLCJpYXQiOjE3NTAyMzg4MTN9.8itaODlEdme3K17To9ZGVfHocnHthuGjJUUhBcBJZJE";
        long a = 1750307625763L;
        ActionValidInterceptor actionValidInterceptor = new ActionValidInterceptor();
        int i = actionValidInterceptor.getI(openId);
        log.info(String.valueOf(i));
        byte[] keyBytes= getASCIIKey(key,  i % 10 + 4);
        log.info(Arrays.toString(keyBytes));
        HmacOneTimePasswordGenerator generator = new HmacOneTimePasswordGenerator();
        String expectedHash = generator.generateOneTimePasswordString(new SecretKeySpec(keyBytes, "HmacSHA1"), a);
        log.info(expectedHash);
    }
}