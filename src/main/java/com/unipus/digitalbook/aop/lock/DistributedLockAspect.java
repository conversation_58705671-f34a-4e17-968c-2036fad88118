package com.unipus.digitalbook.aop.lock;

import com.unipus.digitalbook.common.exception.BusinessException;
import com.unipus.digitalbook.common.lock.DistributedLockManager;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 分布式锁切面
 * 处理@DistributedLock注解，实现方法级别的分布式锁
 * 
 * <AUTHOR>
 */
@Aspect
@Component
@Order(2) // 在限流之后执行
public class DistributedLockAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(DistributedLockAspect.class);
    
    @Autowired
    private DistributedLockManager lockManager;
    
    private final ExpressionParser parser = new SpelExpressionParser();
    
    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        String lockKey = parseKey(distributedLock.key(), joinPoint);
        
        long startTime = System.currentTimeMillis();
        
        if (!lockManager.tryLock(lockKey, distributedLock.waitTime(), distributedLock.leaseTime())) {
            long waitTime = System.currentTimeMillis() - startTime;
            logger.warn("Failed to acquire distributed lock: {}, wait time: {}ms", lockKey, waitTime);
            throw new BusinessException(503, distributedLock.failMessage());
        }
        
        try {
            logger.debug("Acquired distributed lock: {}, wait time: {}ms", 
                lockKey, System.currentTimeMillis() - startTime);
            return joinPoint.proceed();
        } finally {
            lockManager.unlock(lockKey);
            logger.debug("Released distributed lock: {}", lockKey);
        }
    }
    
    /**
     * 解析锁key，支持SpEL表达式
     */
    private String parseKey(String keyExpression, ProceedingJoinPoint joinPoint) {
        if (!keyExpression.contains("#")) {
            return keyExpression;
        }
        
        try {
            // 创建SpEL上下文
            EvaluationContext context = new StandardEvaluationContext();
            
            // 获取方法参数
            Object[] args = joinPoint.getArgs();
            Method method = ((org.aspectj.lang.reflect.MethodSignature) joinPoint.getSignature()).getMethod();
            Parameter[] parameters = method.getParameters();
            
            // 将参数添加到SpEL上下文
            for (int i = 0; i < parameters.length && i < args.length; i++) {
                context.setVariable(parameters[i].getName(), args[i]);
            }
            
            // 解析表达式
            Expression expression = parser.parseExpression(keyExpression);
            Object result = expression.getValue(context);
            
            return result != null ? result.toString() : keyExpression;
        } catch (Exception e) {
            logger.warn("Failed to parse lock key expression: {}, using original key", keyExpression, e);
            return keyExpression;
        }
    }
}