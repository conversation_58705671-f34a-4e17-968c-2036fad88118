package com.unipus.digitalbook.aop.lock;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 分布式锁注解
 * 用于方法级别的分布式锁控制
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributedLock {
    
    /**
     * 锁的key，支持SpEL表达式
     * 例如：'user:' + #userId
     */
    String key();
    
    /**
     * 等待锁的时间（毫秒），默认3秒
     */
    long waitTime() default 3000;
    
    /**
     * 锁持有时间（毫秒），默认60秒
     */
    long leaseTime() default 60000;
    
    /**
     * 获取锁失败时的错误消息
     */
    String failMessage() default "系统繁忙，请稍后再试";
}