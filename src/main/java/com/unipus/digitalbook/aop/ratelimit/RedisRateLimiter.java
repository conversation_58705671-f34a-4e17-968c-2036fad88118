package com.unipus.digitalbook.aop.ratelimit;

import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * Redis限流器实现
 * 使用Redis + Lua脚本实现分布式限流
 * 
 * <AUTHOR>
 */
@Component
public class RedisRateLimiter {
    
    private static final Logger logger = LoggerFactory.getLogger(RedisRateLimiter.class);
    
    @Autowired
    private RedissonClient redissonClient;
    
    /**
     * Lua脚本：滑动窗口限流算法
     * 使用Redis的ZSET数据结构实现滑动时间窗口
     */
    private static final String RATE_LIMIT_SCRIPT = 
        "local key = KEYS[1]\n" +
        "local window = tonumber(ARGV[1])\n" +
        "local limit = tonumber(ARGV[2])\n" +
        "local current = tonumber(ARGV[3])\n" +
        "\n" +
        "-- 清理过期数据\n" +
        "redis.call('ZREMRANGEBYSCORE', key, 0, current - window * 1000)\n" +
        "\n" +
        "-- 获取当前窗口内的请求数\n" +
        "local currentCount = redis.call('ZCARD', key)\n" +
        "\n" +
        "if currentCount < limit then\n" +
        "    -- 添加当前请求\n" +
        "    redis.call('ZADD', key, current, current)\n" +
        "    -- 设置过期时间\n" +
        "    redis.call('EXPIRE', key, window + 1)\n" +
        "    return {1, limit - currentCount - 1}\n" +
        "else\n" +
        "    return {0, 0}\n" +
        "end";
    
    /**
     * 检查是否允许请求通过
     * 
     * @param key 限流key
     * @param maxRequests 最大请求数
     * @param timeWindow 时间窗口（秒）
     * @return 是否允许通过
     */
    public boolean isAllowed(String key, int maxRequests, int timeWindow) {
        try {
            RScript script = redissonClient.getScript();
            long currentTime = System.currentTimeMillis();
            
            Object result = script.eval(
                RScript.Mode.READ_WRITE,
                RATE_LIMIT_SCRIPT,
                RScript.ReturnType.MULTI,
                Collections.singletonList(key),
                timeWindow, maxRequests, currentTime
            );
            
            if (result instanceof Object[]) {
                Object[] resultArray = (Object[]) result;
                Long allowed = (Long) resultArray[0];
                Long remaining = (Long) resultArray[1];
                
                logger.debug("Rate limit check for key: {}, allowed: {}, remaining: {}", 
                    key, allowed, remaining);
                
                return allowed == 1;
            }
            
            return false;
        } catch (Exception e) {
            logger.error("Rate limit check failed for key: {}", key, e);
            // 发生异常时默认允许通过，避免影响正常业务
            return true;
        }
    }
    
    /**
     * 获取剩余可用请求数
     * 
     * @param key 限流key
     * @param maxRequests 最大请求数
     * @param timeWindow 时间窗口（秒）
     * @return 剩余请求数
     */
    public long getRemaining(String key, int maxRequests, int timeWindow) {
        try {
            long currentTime = System.currentTimeMillis();
            String countKey = key;
            
            // 清理过期数据
            redissonClient.getScoredSortedSet(countKey)
                .removeRangeByScore(0, true, currentTime - timeWindow * 1000L, false);
            
            // 获取当前计数
            int currentCount = redissonClient.getScoredSortedSet(countKey).size();
            
            return Math.max(0, maxRequests - currentCount);
        } catch (Exception e) {
            logger.error("Get remaining requests failed for key: {}", key, e);
            return maxRequests;
        }
    }
}