package com.unipus.digitalbook.aop.ratelimit;

import com.unipus.digitalbook.common.exception.BusinessException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 限流切面
 * 拦截带有@RateLimit注解的方法，执行限流逻辑
 * 
 * <AUTHOR>
 */
@Aspect
@Component
@Order(1) // 确保限流切面优先执行
public class RateLimitAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(RateLimitAspect.class);
    
    @Autowired
    private RedisRateLimiter rateLimiter;
    
    @Around("@annotation(rateLimit)")
    public Object around(ProceedingJoinPoint joinPoint, RateLimit rateLimit) throws Throwable {
        String key = generateKey(joinPoint, rateLimit);
        
        boolean allowed = rateLimiter.isAllowed(
            key, 
            rateLimit.maxRequests(), 
            rateLimit.timeWindow()
        );
        
        if (!allowed) {
            logger.warn("Rate limit exceeded for key: {}, max: {}, window: {}s", 
                key, rateLimit.maxRequests(), rateLimit.timeWindow());
            throw new BusinessException(429, rateLimit.message());
        }
        
        logger.debug("Rate limit check passed for key: {}", key);
        return joinPoint.proceed();
    }
    
    /**
     * 生成限流key
     */
    private String generateKey(ProceedingJoinPoint joinPoint, RateLimit rateLimit) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        String keyPrefix = rateLimit.key();
        if (keyPrefix.isEmpty()) {
            keyPrefix = method.getDeclaringClass().getName() + "." + method.getName();
        }
        
        String suffix = "";
        switch (rateLimit.limitType()) {
            case IP:
                suffix = getClientIP();
                break;
            case USER:
                suffix = getCurrentUserId();
                break;
            case GLOBAL:
                suffix = "global";
                break;
        }
        
        return "rate_limit:" + keyPrefix + ":" + suffix;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIP() {
        try {
            ServletRequestAttributes attributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                
                String xForwardedFor = request.getHeader("X-Forwarded-For");
                if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
                    return xForwardedFor.split(",")[0].trim();
                }
                
                String xRealIP = request.getHeader("X-Real-IP");
                if (xRealIP != null && !xRealIP.isEmpty() && !"unknown".equalsIgnoreCase(xRealIP)) {
                    return xRealIP;
                }
                
                return request.getRemoteAddr();
            }
        } catch (Exception e) {
            logger.debug("Failed to get client IP", e);
        }
        return "unknown";
    }
    
    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            ServletRequestAttributes attributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String userId = request.getHeader("X-User-Id");
                if (userId != null && !userId.isEmpty()) {
                    return userId;
                }
                
                // 可以从JWT token中解析用户ID
                String authorization = request.getHeader("Authorization");
                if (authorization != null && authorization.startsWith("Bearer ")) {
                    // TODO: 解析JWT获取用户ID
                    return "user_from_jwt";
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to get current user ID", e);
        }
        return "anonymous";
    }
}