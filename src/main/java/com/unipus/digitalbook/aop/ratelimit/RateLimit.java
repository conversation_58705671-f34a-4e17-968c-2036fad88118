package com.unipus.digitalbook.aop.ratelimit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 限流注解
 * 用于标记需要进行限流控制的方法
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    
    /**
     * 限流key前缀，默认使用方法全路径
     */
    String key() default "";
    
    /**
     * 时间窗口内允许的最大请求数
     */
    int maxRequests() default 100;
    
    /**
     * 时间窗口大小（秒）
     */
    int timeWindow() default 60;
    
    /**
     * 限流类型：IP、USER、GLOBAL
     */
    LimitType limitType() default LimitType.IP;
    
    /**
     * 限流失败时的错误消息
     */
    String message() default "访问过于频繁，请稍后再试";
    
    /**
     * 限流类型枚举
     */
    enum LimitType {
        /** 根据IP限流 */
        IP,
        /** 根据用户限流 */
        USER,
        /** 全局限流 */
        GLOBAL
    }
}