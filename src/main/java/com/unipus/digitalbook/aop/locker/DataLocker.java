package com.unipus.digitalbook.aop.locker;

import java.lang.annotation.*;

/**
 * 数据更新锁
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataLocker {

    /**
     * 锁的key，可以是方法参数的索引，也可以是固定的key
     */
    String key() default "";

    /**
     * 锁的key的前缀
     */
    String prefix() default "dataLocker";

    /**
     * 锁的key，单用户模式下，每个用户只能同时执行一个方法
     */
    boolean single() default false;

    /**
     * 锁的等待时间，默认30秒
     */
    long waitTime() default 10;

    /**
     * 锁的持有时间，默认30秒
     */
    long leaseTime() default 30;
}