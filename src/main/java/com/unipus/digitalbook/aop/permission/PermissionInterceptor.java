package com.unipus.digitalbook.aop.permission;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.common.utils.UserUtil;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.constants.WebConstant;
import com.unipus.digitalbook.model.entity.CurrentUserInfo;
import com.unipus.digitalbook.service.RoleService;
import io.micrometer.common.lang.NonNull;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.PathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.*;

/**
 * 用户【权限】校验拦截器
 */
@Component
@Slf4j
public class PermissionInterceptor implements HandlerInterceptor {

    private final StringRedisTemplate stringRedisTemplate;
    private final RoleService roleService;

    public PermissionInterceptor(StringRedisTemplate stringRedisTemplate, RoleService roleService) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.roleService = roleService;
    }

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                             @NonNull Object handler) throws Exception {

        // 校验用户接口权限
        if(!validatePermission(request)){
            Response.writeErrorResponse(response, HttpServletResponse.SC_FORBIDDEN, "接口权限校验失败，请联系管理员");
            return false;
        }

        // 接口权限校验通过
        return true;
    }

    /**
     * 校验用户接口权限
     * @param request HttpServletRequest
     * @return true:校验通过 false:校验失败
     */
    private boolean validatePermission(HttpServletRequest request) {
        // 1.取得用户ID
        Long userId = (Long) request.getAttribute(WebConstant.JWT_USER_ID);
        if(userId == null){
            log.warn("权限校验失败: 用户ID为空");
            return false;
        }
        if (UserUtil.isSuperAdmin(userId)) {
            return true;
        }
        // 2. 取得用户登录所属组织ID
        String orgIdHeader = request.getHeader(WebConstant.HEADER_ORG_ID);
        if (!StringUtils.hasText(orgIdHeader)){
            log.warn("权限校验失败: 组织ID为空");
            return false;
        }
        Long orgId = Long.parseLong(orgIdHeader);
        log.info("用户ID: {}, 组织ID: {}", userId, orgId);
        // 3. 取得用户权限列表
        Set<String> permissions = getUserPermission(userId, orgId);
        if(CollectionUtils.isEmpty(permissions)){
            log.warn("权限校验失败: 用户权限列表为空");
            return false;
        }

        // 4. 检查接口权限
        if(!checkUserPermission(permissions, request.getContextPath(), request.getRequestURI())){
            log.warn("用户权限校验失败: 用户ID: {}, 请求URI: {}, 请求方法: {}", userId, request.getRequestURI(), request.getMethod());
            return false;
        }

        return true;
    }

    /**
     * 检查用户权限
     *
     * @param userId 用户ID
     * @param orgId 组织ID
     * @return 用户权限列表
     */
    private Set<String> getUserPermission(Long userId, Long orgId) {
        // 取得用户登录信息
        String currentUserInfoStr = stringRedisTemplate.opsForValue().get(CacheConstant.REDIS_USR_PREFIX + userId);
        if(!StringUtils.hasLength(currentUserInfoStr)) {
            log.warn("用户权限校验失败: 用户登录信息为空");
            return Collections.emptySet();
        }
        CurrentUserInfo currentUserInfo = JSON.parseObject(currentUserInfoStr, CurrentUserInfo.class);
        Map<Long, Set<Long>> orgRolesMap = currentUserInfo.getOrgRolesMap();
        if(CollectionUtils.isEmpty(orgRolesMap) || !orgRolesMap.containsKey(orgId)){
            log.warn("用户权限校验失败: 用户组织角色映射为空");
            return Collections.emptySet();
        }
        // 取得用户角色ID列表
        Set<Long> roleIds = orgRolesMap.get(orgId);
        if(CollectionUtils.isEmpty(roleIds)){
            log.warn("用户权限校验失败: 用户角色ID列表为空");
            return Collections.emptySet();
        }
        Set<String> userPermissions = roleService.getPermissionsByRoles(roleIds);
        if(CollectionUtils.isEmpty(userPermissions)){
            log.warn("用户权限校验失败: 用户权限列表为空");
            return Collections.emptySet();
        }
        return userPermissions;
    }

    /**
     * 检查用户是否有访问指定接口的权限
     * @param permissions 用户权限列表
     * @param contextPrefix 统一请求路径前缀
     * @param requestUri 请求的接口路径
     * @return true表示有权限，false表示无权限
     */
    private boolean checkUserPermission(Set<String> permissions, String contextPrefix, String requestUri) {
        String uriWithoutPrefix;
        if(StringUtils.hasText(contextPrefix) && StringUtils.hasText(requestUri) && requestUri.startsWith(contextPrefix)){
            uriWithoutPrefix = requestUri.substring(contextPrefix.length());
        } else {
            uriWithoutPrefix = requestUri;
        }
        PathMatcher matcher = new AntPathMatcher();
        boolean  result = permissions.stream().anyMatch(permission -> matcher.match(permission, uriWithoutPrefix));
        log.debug("用户权限校验: uri[{}],结果[{}]", uriWithoutPrefix, result);
        return result;
    }
}