package com.unipus.digitalbook.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unipus.digitalbook.model.entity.highconcurrency.DeadlockTestTable1;
import com.unipus.digitalbook.model.entity.highconcurrency.DeadlockTestTable2;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 死锁测试Mapper
 * 提供死锁测试相关的数据操作方法
 * 
 * <AUTHOR>
 */
@Mapper
public interface DeadlockTestMapper extends BaseMapper<DeadlockTestTable1> {
    
    /**
     * 模拟死锁场景1：先更新table1再更新table2
     * 
     * @param refId 关联ID
     * @param value1 表1的值
     * @param value2 表2的值
     */
    void simulateDeadlockScenario1(@Param("refId") Long refId, 
                                   @Param("value1") String value1, 
                                   @Param("value2") String value2);
    
    /**
     * 模拟死锁场景2：先更新table2再更新table1
     * 
     * @param refId 关联ID
     * @param value1 表1的值
     * @param value2 表2的值
     */
    void simulateDeadlockScenario2(@Param("refId") Long refId, 
                                   @Param("value1") String value1, 
                                   @Param("value2") String value2);
    
    /**
     * 安全的批量更新表1（按ID排序）
     * 
     * @param ids ID列表
     * @param dataValue 数据值
     * @return 更新记录数
     */
    int safeBatchUpdateTable1(@Param("ids") List<Long> ids, 
                              @Param("dataValue") String dataValue);
    
    /**
     * 安全的批量更新表2（按ID排序）
     * 
     * @param ids ID列表
     * @param dataValue 数据值
     * @return 更新记录数
     */
    int safeBatchUpdateTable2(@Param("ids") List<Long> ids, 
                              @Param("dataValue") String dataValue);
    
    /**
     * 原子性地更新两个表（避免死锁）
     * 
     * @param refId 关联ID
     * @param value1 表1的值
     * @param value2 表2的值
     * @return 更新记录数
     */
    int atomicUpdateBothTables(@Param("refId") Long refId, 
                               @Param("value1") String value1, 
                               @Param("value2") String value2);
    
    /**
     * 获取表1数据
     * 
     * @param refId 关联ID
     * @return 数据
     */
    DeadlockTestTable1 getTable1ByRefId(@Param("refId") Long refId);
    
    /**
     * 获取表2数据
     * 
     * @param refId 关联ID
     * @return 数据
     */
    DeadlockTestTable2 getTable2ByRefId(@Param("refId") Long refId);
    
    /**
     * 递增计数器 - 表1
     * 
     * @param refId 关联ID
     * @return 更新记录数
     */
    @Update("UPDATE deadlock_test_table1 SET update_count = update_count + 1, update_time = NOW() WHERE ref_id = #{refId}")
    int incrementCounterTable1(@Param("refId") Long refId);
    
    /**
     * 递增计数器 - 表2
     * 
     * @param refId 关联ID
     * @return 更新记录数
     */
    @Update("UPDATE deadlock_test_table2 SET update_count = update_count + 1, update_time = NOW() WHERE ref_id = #{refId}")
    int incrementCounterTable2(@Param("refId") Long refId);
}