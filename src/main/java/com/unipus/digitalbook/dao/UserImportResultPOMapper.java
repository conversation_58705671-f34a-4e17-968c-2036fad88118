package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.user.UserImportResultPO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/6 10:41
 */
public interface UserImportResultPOMapper {

    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param userImportResultPO the record
     * @return insert count
     */
    int insert(UserImportResultPO userImportResultPO);

    /**
     * insert record to table selective
     *
     * @param userImportResultPO the record
     * @return insert count
     */
    int insertSelective(UserImportResultPO userImportResultPO);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    UserImportResultPO selectByPrimaryKey(Long id);

    /**
     * update record
     *
     * @param userImportResultPO the updated record
     * @return update count
     */
    int updateByPrimaryKey(UserImportResultPO userImportResultPO);

    /**
     * update record selective
     *
     * @param userImportResultPO the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UserImportResultPO userImportResultPO);
}