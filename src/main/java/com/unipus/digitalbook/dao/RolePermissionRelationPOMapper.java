package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.role.RolePermissionRelationPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【role_permission_relation(角色权限分配关系表，用于管理角色的权限关联)】的数据库操作Mapper
* @createDate 2024-11-28 16:19:34
* @Entity com.unipus.digitalbook.model.po.role.RolePermissionRelationPO
*/
public interface RolePermissionRelationPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(RolePermissionRelationPO rolePermissionRelationPO);

    int insertSelective(RolePermissionRelationPO rolePermissionRelationPO);

    RolePermissionRelationPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RolePermissionRelationPO rolePermissionRelationPO);

    int updateByPrimaryKey(RolePermissionRelationPO rolePermissionRelationPO);

    int batchInsertOrUpdate(List<RolePermissionRelationPO> list);

    List<RolePermissionRelationPO> selectByRoleIdsAndType(List<Long> roleIds, Integer type);

    List<RolePermissionRelationPO> selectByType(Integer type);
}
