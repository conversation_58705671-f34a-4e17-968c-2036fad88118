package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.content.CustomContentQuestionGroupRelationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:19
 */
public interface CustomContentQuestionGroupRelationPOMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table selective
     * @param customContentQuestionGroupRelationPO the record
     * @return insert count
     */
    int insertSelective(CustomContentQuestionGroupRelationPO customContentQuestionGroupRelationPO);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    CustomContentQuestionGroupRelationPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param customContentQuestionGroupRelationPO the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CustomContentQuestionGroupRelationPO customContentQuestionGroupRelationPO);

    /**
     * 批量更新
     *
     * @param list
     * @return
     */
    int updateBatchSelective(@Param("list") List<CustomContentQuestionGroupRelationPO> list);

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<CustomContentQuestionGroupRelationPO> list);

    /**
     * 根据自建内容ID删除关联关系
     *
     * @param contentId 自建内容ID
     * @param tenantId  租户ID
     * @param userId    用户ID
     * @return 删除数量
     */
    int deleteByContentId(@Param("contentId") Long contentId, @Param("tenantId") Long tenantId, @Param("userId") Long userId);

    /**
     * 根据自建内容ID查询关联关系
     *
     * @param contentId 自建内容ID
     * @param tenantId  租户ID
     * @return 关联关系列表
     */
    List<CustomContentQuestionGroupRelationPO> selectByContentId(@Param("contentId") Long contentId, @Param("tenantId") Long tenantId);

    /**
     * 根据内容ID列表批量查询题目关联关系
     *
     * @param contentIds 内容ID列表
     * @param tenantId   租户ID
     * @return 题目关联关系列表
     */
    List<CustomContentQuestionGroupRelationPO> selectByContentIds(@Param("contentIds") List<Long> contentIds, @Param("tenantId") Long tenantId);
}