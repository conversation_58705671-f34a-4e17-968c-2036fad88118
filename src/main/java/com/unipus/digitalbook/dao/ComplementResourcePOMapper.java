package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.po.complement.ComplementResourcePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【complement_resource(教材配套资源)】的数据库操作Mapper
* @createDate 2025-01-08 17:06:19
* @Entity com.unipus.digitalbook.model.po.media.ComplementResourcePO
*/
public interface ComplementResourcePOMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(ComplementResourcePO complementResourcePO);

    ComplementResourcePO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ComplementResourcePO complementResourcePO);

    List<ComplementResourcePO> search(String bookId, Integer resourceType, Integer visibleStatus, PageParams page);

    Integer count(String bookId, Integer resourceType, Integer visibleStatus);

    Integer logicalDelete(@Param("id") Long id, @Param("opUserId") Long opUserId);

    Integer updateVisibleStatus(@Param("id") Long id, @Param("visibleStatus") Integer visibleStatus, @Param("opUserId") Long opUserId);

    List<ComplementResourcePO> selectResourceByBookIdAndVersion(@Param("bookId")String bookId, @Param("version")String version);

    int generateVersionResource(@Param("bookId")String bookId, @Param("version")String version);

    ComplementResourcePO selectByResourceId(@Param("resourceId") String resourceId);

    List<ComplementResourcePO> selectByBookVersionId(@Param("bookVersionId") Long bookVersionId);
}
