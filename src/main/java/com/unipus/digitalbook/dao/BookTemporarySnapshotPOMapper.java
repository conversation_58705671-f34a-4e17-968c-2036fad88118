package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.book.BookTemporarySnapshotPO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025/6/9 18:29
 */
public interface BookTemporarySnapshotPOMapper {

    int insertSelective(BookTemporarySnapshotPO bookTemporarySnapshotPO);

    BookTemporarySnapshotPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BookTemporarySnapshotPO bookTemporarySnapshotPO);

    /**
     * 根据教材ID和章节ID查询最近一次快照
     */
    BookTemporarySnapshotPO selectLatestByBookIdAndChapterId(@Param("bookId") String bookId, @Param("chapterId") String chapterId);
}