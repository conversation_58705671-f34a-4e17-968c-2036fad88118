package com.unipus.digitalbook.dao;


import com.unipus.digitalbook.model.po.template.PaperScoreTemplateDetailPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模板明细
 */
public interface PaperScoreTemplateDetailPOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PaperScoreTemplateDetailPO detail);

    int insertSelective(PaperScoreTemplateDetailPO detail);

    PaperScoreTemplateDetailPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaperScoreTemplateDetailPO detail);

    int updateByPrimaryKey(PaperScoreTemplateDetailPO detail);

    int batchInsert(@Param("list") List<PaperScoreTemplateDetailPO> templateDetailPOList);

    List<PaperScoreTemplateDetailPO> selectByPaperScoreTemplateId(@Param("paperScoreTemplateId") Long paperScoreTemplateId);

    int deleteByIdList(@Param("idList") List<Long> idList,@Param("updateBy") Long updateBy);

    int deleteByPaperScoreTemplateId(@Param("paperScoreTemplateId") Long paperScoreTemplateId,@Param("updateBy") Long currentUserId);
}