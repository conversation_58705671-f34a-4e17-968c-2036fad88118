package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.book.BookUserActivityInfoPO;
import com.unipus.digitalbook.model.po.book.BookUserActivityPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【book_user_activity(用户操作教材行为)】的数据库操作Mapper
* @createDate 2024-12-31 18:14:05
* @Entity com.unipus.digitalbook.model.po.book.BookUserActivityPO
*/
public interface BookUserActivityPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BookUserActivityPO bookUserActivityPO);

    int insertSelective(BookUserActivityPO bookUserActivityPO);

    BookUserActivityPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BookUserActivityPO bookUserActivityPO);

    int updateByPrimaryKey(BookUserActivityPO bookUserActivityPO);

    int batchInsertOrUpdate(List<BookUserActivityPO> list);

    List<BookUserActivityInfoPO> selectByUserIdAndBookIds(Long userId, List<String> bookIds);
}
