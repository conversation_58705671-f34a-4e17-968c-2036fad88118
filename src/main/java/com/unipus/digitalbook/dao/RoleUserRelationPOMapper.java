package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.role.RoleUserRelationPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【role_user_relation(用户角色关系表)】的数据库操作Mapper
* @createDate 2024-12-03 15:49:48
* @Entity com.unipus.digitalbook.model.po.role.RoleUserRelationPO
*/
public interface RoleUserRelationPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(RoleUserRelationPO roleUserRelationPO);

    int insertSelective(RoleUserRelationPO roleUserRelationPO);

    RoleUserRelationPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RoleUserRelationPO roleUserRelationPO);

    int updateByPrimaryKey(RoleUserRelationPO roleUserRelationPO);

    List<RoleUserRelationPO> selectByUserIdAndOrgId(Long userId, Long orgId);

    List<Long> selectIdByRoleId(Long roleId);

    List<RoleUserRelationPO> selectRoleMapByUserId(Long userId);

    int bathLogicalDelete(Long updateBy, List<Long> ids);

    int batchInsertOrUpdate(List<RoleUserRelationPO> roleUserRelationPOList);
}
