package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.question.ChoiceQuestionGroupOptionPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【choice_question_group_option】的数据库操作Mapper
* @createDate 2025-03-31 14:56:19
* @Entity com.unipus.digitalbook.model.po.question.ChoiceQuestionGroupOptionPO
*/
public interface ChoiceQuestionGroupOptionPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ChoiceQuestionGroupOptionPO option);

    int insertSelective(ChoiceQuestionGroupOptionPO option);

    ChoiceQuestionGroupOptionPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ChoiceQuestionGroupOptionPO option);

    int updateByPrimaryKey(ChoiceQuestionGroupOptionPO option);

    int batchInsertOrUpdate(List<ChoiceQuestionGroupOptionPO> list);

    List<ChoiceQuestionGroupOptionPO> selectByGroupId(Long groupId);

}
