package com.unipus.digitalbook.dao;


import com.unipus.digitalbook.model.po.paper.PaperPO;
import com.unipus.digitalbook.model.po.publish.BookVersionPO;
import com.unipus.digitalbook.model.po.publish.BookVersionPaperVersionRelationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description 针对表【book_version_paper_version_relation】的数据库操作Mapper
 * @createDate 2025-04-27 17:24:39
 */
public interface BookVersionPaperVersionRelationPOMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(BookVersionPaperVersionRelationPO bookVersionPaperVersionRelationPO);

    BookVersionPaperVersionRelationPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BookVersionPaperVersionRelationPO bookVersionPaperVersionRelationPO);

    List<String> selectPublishedPaperIds(@Param("paperIds") List<String> paperIds);

    List<BookVersionPaperVersionRelationPO> selectByBookVersionId(@Param("bookVersionId") Long bookVersionId);

    int batchInsert(@Param("list") List<BookVersionPaperVersionRelationPO> list);

    /**
     * 通过教材版本取得关联试卷信息
     * @param bookId 教材ID
     * @param bookVersionNumber 教材版本
     * @param paperId 试卷ID
     * @return 试卷版本
     */
    PaperPO getPaperVersionByBookVersion(@Param("bookId") String bookId,
                                        @Param("bookVersionNumber") String bookVersionNumber,
                                        @Param("paperId") String paperId);

    /**
     * 通过教材版本ID取得试卷版本
     * @param bookVersionId 教材版本ID
     * @return 试卷版本
     */
    List<PaperPO> getPaperVersionByBookVersionId(@Param("bookVersionId") Long bookVersionId);

    /**
     * 通过教材版本ID取得试卷版本
     * @param paperId 试卷ID
     * @param paperVersionNumber 试卷版本ID
     * @return 试卷版本
     */
    BookVersionPO getBookVersionByPaperVersion(@Param("paperId") String paperId, @Param("paperVersionNumber") String paperVersionNumber);

    BookVersionPaperVersionRelationPO selectLastPublishedByPaperId(@Param("paperId") String paperId);
}
