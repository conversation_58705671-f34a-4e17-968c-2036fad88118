package com.unipus.digitalbook.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unipus.digitalbook.model.entity.highconcurrency.BatchOperationData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 批量操作数据Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface BatchOperationDataMapper extends BaseMapper<BatchOperationData> {
    
    /**
     * 批量插入数据
     * 使用MySQL的批量插入语法，避免逐条插入的性能问题
     * 
     * @param dataList 数据列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<BatchOperationData> dataList);
    
    /**
     * 按ID列表批量更新状态
     * 按ID排序避免死锁
     * 
     * @param ids ID列表
     * @param status 新状态
     * @param updateUser 更新用户
     * @return 更新的记录数
     */
    int batchUpdateStatusByIds(@Param("ids") List<Long> ids, 
                               @Param("status") String status,
                               @Param("updateUser") Long updateUser);
    
    /**
     * 按ID列表批量更新业务数据
     * 
     * @param updateList 更新数据列表
     * @return 更新的记录数
     */
    int batchUpdateByIds(@Param("list") List<BatchOperationData> updateList);
    
    /**
     * 按ID列表批量删除
     * 按ID排序避免死锁
     * 
     * @param ids ID列表
     * @return 删除的记录数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);
    
    /**
     * 按批次号查询数据
     * 
     * @param batchNo 批次号
     * @return 数据列表
     */
    List<BatchOperationData> selectByBatchNo(@Param("batchNo") String batchNo);
    
    /**
     * 按状态和类型查询数据（分页）
     * 
     * @param status 状态
     * @param dataType 数据类型
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 数据列表
     */
    List<BatchOperationData> selectByStatusAndType(@Param("status") String status,
                                                   @Param("dataType") String dataType,
                                                   @Param("offset") Integer offset,
                                                   @Param("limit") Integer limit);
    
    /**
     * 统计各状态的记录数
     * 
     * @param batchNo 批次号
     * @return 状态统计结果
     */
    List<StatusCount> countByStatus(@Param("batchNo") String batchNo);
    
    /**
     * 状态统计结果
     */
    class StatusCount {
        private String status;
        private Integer count;
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
        
        public Integer getCount() {
            return count;
        }
        
        public void setCount(Integer count) {
            this.count = count;
        }
    }
}