package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.chapter.ChapterPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【chapter(章节表)】的数据库操作Mapper
* @createDate 2024-12-16 22:48:28
* @Entity com.unipus.digitalbook.model.po.chapter.ChapterPO
*/
public interface ChapterPOMapper {

    int deleteByPrimaryKey(String id);

    int insert(ChapterPO chapterPO);

    int insertSelective(ChapterPO chapterPO);

    ChapterPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ChapterPO chapterPO);

    int updateByPrimaryKey(ChapterPO chapterPO);

    List<ChapterPO> selectByBookId(String bookId);

    List<String> selectIdsByBookId(String bookId);

    List<String> selectIdsByChapterIds(@Param("chapterIds") List<String> chapterIds);

    ChapterPO selectByBookIdAndNumber(@Param("bookId") String bookId, @Param("chapterNumber") Integer chapterNumber);

    int batchUpdateChapterNumber(@Param("chapterList") List<ChapterPO> chapterList);

    boolean existsChapter(Collection<String> chapterIds);

    List<ChapterPO> selectAll();
}
