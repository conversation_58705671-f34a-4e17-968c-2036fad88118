package com.unipus.digitalbook.dao;


import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.po.template.PaperScoreTemplatePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模板数据
 */
public interface PaperScoreTemplatePOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PaperScoreTemplatePO template);

    int insertSelective(PaperScoreTemplatePO template);

    PaperScoreTemplatePO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaperScoreTemplatePO template);

    int updateByPrimaryKey(PaperScoreTemplatePO template);

    Long countByName(@Param("name") String name, @Param("id") Long id);

    int updateStatus(@Param("updateBy") Long currentUserId, @Param("id") Long id, @Param("status") Integer status);

    List<PaperScoreTemplatePO> selectTemplateList(@Param("name") String name,@Param("type") Integer type,@Param("status") Integer status,@Param("page") PageParams pageParams);

    Long countTemplateList(@Param("name") String name,@Param("type") Integer type,@Param("status") Integer status);

    List<PaperScoreTemplatePO> selectByIdList(@Param("idList") List<Long> paperScoreTemplateIdList);

    PaperScoreTemplatePO selectEffectTemplateByIdAndType(@Param("id") Long templateId,@Param("type") Integer type);
}