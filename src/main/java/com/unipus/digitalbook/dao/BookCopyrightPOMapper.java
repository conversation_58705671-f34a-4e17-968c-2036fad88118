package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.book.BookCopyrightPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【book_copyright(教材版权信息表)】的数据库操作Mapper
* @createDate 2025-03-25 15:04:54
* @Entity com.unipus.digitalbook.model.po.book.BookCopyrightPO
*/
public interface BookCopyrightPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BookCopyrightPO copyright);

    int insertSelective(BookCopyrightPO copyright);

    BookCopyrightPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BookCopyrightPO copyright);

    int updateByPrimaryKey(BookCopyrightPO copyright);

    BookCopyrightPO selectByBookIdAndVersion(String bookId, String versionNumber);

    /**
     * 根据教材版本id，查询版权信息
     * @param bookVersionId 教材版本id
     * @return 版权信息
     */
    BookCopyrightPO selectByBookVersion(@Param("bookVersionId") Long bookVersionId);

    /**
     * 批量查询教材版权信息
     *
     * @param bookIds 教材 ID 列表
     */
    List<BookCopyrightPO> selectListByBookIds(@Param("bookIds") List<String> bookIds);

    /**
     * 根据 教材 ID、版本号 列表批量查询教材版权信息
     */
    List<BookCopyrightPO> selectListByBookIdAndVersionList(@Param("list") List<BookCopyrightPO> list);

    /**
     * 根据教材版本记录 ID 列表批量查询教材版权信息
     */
    List<BookCopyrightPO> selectListByBookVersionIds(@Param("bookVersionIds") List<Long> bookVersionIds);

}
