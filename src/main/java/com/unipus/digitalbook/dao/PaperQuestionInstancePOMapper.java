package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.paper.PaperQuestionInstancePO;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

import java.util.List;

/**
 * 试卷试题实例表Mapper接口
 */
public interface PaperQuestionInstancePOMapper {

    /**
     * 批量插入或更新
     * @param records 待插入或更新的数据列表
     * @return 插入或更新成功的记录数
     */
    int batchInsertOrUpdate(List<PaperQuestionInstancePO> records);

    /**
     * 根据实例ID/轮次ID查询试卷试题实例列表
     * @param roundId 轮次ID/实例ID
     * @return 满足条件的试卷试题实例列表
     */
    List<PaperQuestionInstancePO> selectPaperQuestionsByRoundId(String roundId);

    /**
     * 根据轮次ID查询
     * @param roundIds 轮次ID列表
     * @return 轮次ID对应的试卷试题实例列表
     */
    List<PaperQuestionInstancePO> selectByRoundIds(List<String> roundIds);

    /**
     * 根据试卷id、版本号、用户id、租户id、状态查询用户答题记录
     * @param paperId 试卷业务ID
     * @param paperVersionNumber 试卷版本号
     * @param openId 用户ssoID
     * @param tenantId 租户ID
     * @param status 用户成绩提交状态
     * @return 用户答题记录
     */
    List<PaperQuestionInstancePO> getUserAnswerRecord(
            @Param("paperId") String paperId,
            @Param("paperVersionNumber")@Nullable String paperVersionNumber,
            @Param("openId") String openId,
            @Param("tenantId") Long tenantId,
            @Param("status") @Nullable Integer status );

    /**
     * 根据分数批次ID查询用户答题记录
     * @param scoreBatchId 分数批次ID
     */
    List<PaperQuestionInstancePO> getUserAnswerRecordByScoreBatchId(
            @Param("scoreBatchId") String scoreBatchId);

    /**
     * 根据轮次ID仅查询回答错误的小题（最小化返回字段）
     * 仅返回 question_biz_id 与 question_group_type，用于后续在内存中过滤客观题
     * @param roundId 轮次/实例ID
     * @return 回答错误的小题简要信息列表
     */
    List<PaperQuestionInstancePO> selectIncorrectByRoundId(@Param("roundId") String roundId);

}