package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.role.RolePO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【role(角色表)】的数据库操作Mapper
* @createDate 2024-11-25 14:04:19
* @Entity com.unipus.digitalbook.model.po.role.RolePO
*/
public interface RolePOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(RolePO rolePO);

    int insertSelective(RolePO rolePO);

    RolePO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RolePO rolePO);

    int updateByPrimaryKey(RolePO rolePO);

    int updateStatus(Long updateBy, Long id, Integer status);

    int logicalDelete(Long updateBy, Long id);

    List<RolePO> selectByIds(List<Long> ids);

    List<RolePO> search(String name, Integer status, int offset, int limit);

    Long count(String name, Integer status, int offset, int limit);

    List<RolePO> selectByName(String name);
}
