package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.role.OrgRoleRelationPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【org_role_relation(组织与角色关系表)】的数据库操作Mapper
* @createDate 2024-11-25 15:32:23
* @Entity com.unipus.digitalbook.model.po.role.OrgRoleRelationPO
*/
public interface OrgRoleRelationPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(OrgRoleRelationPO orgRoleRelationPO);

    int insertSelective(OrgRoleRelationPO orgRoleRelationPO);

    OrgRoleRelationPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrgRoleRelationPO orgRoleRelationPO);

    int updateByPrimaryKey(OrgRoleRelationPO orgRoleRelationPO);

    int bathLogicalDelete(Long updateBy, List<Long> ids);

    List<Long> selectIdByRoleId(Long roleId);

    List<Long> selectRoleIdByOrgId(Long orgId);

    int batchInsertOrUpdate(List<OrgRoleRelationPO> list);
}
