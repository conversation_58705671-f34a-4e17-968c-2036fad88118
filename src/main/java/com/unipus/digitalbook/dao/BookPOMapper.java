package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.book.BookPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【book(教材表)】的数据库操作Mapper
* @createDate 2024-12-16 22:44:45
* @Entity com.unipus.digitalbook.model.po.book.BookPO
*/
public interface BookPOMapper {

    int deleteByPrimaryKey(String id);

    int insert(BookPO bookPO);

    int insertSelective(BookPO bookPO);

    BookPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BookPO bookPO);

    int updateByPrimaryKey(BookPO bookPO);

    List<BookPO> selectByOrgIdAndIds(@Param("orgId") Long orgId, @Param("ids") Collection<String> ids);

    Long selectEditorId(@Param("bookId") String bookId);

    int updateTimeByBookId(@Param("bookId") String bookId);

    List<BookPO> selectByIds(@Param("ids") List<String> ids);

    /**
     * 根据机构 ID 获取教材 ID 列表
     */
    List<String> selectBookIdsByOrgIds(@Param("orgIds") List<Long> orgIds);

    /**
     * 根据租户 ID 获取教材 ID 列表
     */
    List<String> selectIdsByTenantId(long tenantId);

}
