package com.unipus.digitalbook.dao;


import com.unipus.digitalbook.model.po.template.PaperScoreTemplateRelationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PaperScoreTemplateRelationPOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PaperScoreTemplateRelationPO paperScoreTemplateRelationPO);

    int insertSelective(PaperScoreTemplateRelationPO paperScoreTemplateRelationPO);

    PaperScoreTemplateRelationPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaperScoreTemplateRelationPO paperScoreTemplateRelationPO);

    int updateByPrimaryKey(PaperScoreTemplateRelationPO paperScoreTemplateRelationPO);

    List<Long> selectTemplateIdListByBookId(@Param("bookId") String bookId);

    int batchInsert(@Param("list") List<PaperScoreTemplateRelationPO> paperScoreTemplateRelationPOList);

    int batchDisable(@Param("bookId") String bookId, @Param("updateUserId") Long updateUserId);
}