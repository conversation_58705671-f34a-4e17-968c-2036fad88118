package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.content.CustomContentPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:18
 */
public interface CustomContentPOMapper {

    /**
     * insert record to table selective
     * @param customContentPO the record
     * @return insert count
     */
    int insertSelective(CustomContentPO customContentPO);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    CustomContentPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param customContentPO the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CustomContentPO customContentPO);

    /**
     * 根据业务ID查询编写中的自建内容
     *
     * @param bizId 业务ID
     * @param tenantId 租户ID
     * @return 自建内容对象
     */
    CustomContentPO selectEditingByBizId(@Param("bizId") String bizId, @Param("tenantId") Long tenantId);

    /**
     * 根据业务ID列表批量查询编写中的自建内容
     *
     * @param bizIds 业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    List<CustomContentPO> selectEditingByBizIds(@Param("bizIds") List<String> bizIds, @Param("tenantId") Long tenantId);

    /**
     * 根据业务ID查询已发布的自建内容
     *
     * @param bizId 业务ID
     * @param tenantId 租户ID
     * @return 自建内容对象
     */
    CustomContentPO selectPublishedByBizId(@Param("bizId") String bizId, @Param("tenantId") Long tenantId);

    /**
     * 根据业务ID列表批量查询已发布的自建内容
     *
     * @param bizIds 业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    List<CustomContentPO> selectPublishedByBizIds(@Param("bizIds") List<String> bizIds, @Param("tenantId") Long tenantId);

    /**
     * 根据业务ID列表和状态批量查询自建内容
     *
     * @param bizIds   业务ID列表
     * @param status   内容状态
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    List<CustomContentPO> selectByBizIdsAndStatus(@Param("bizIds") List<String> bizIds, @Param("status") Integer status, @Param("tenantId") Long tenantId);

    /**
     * 根据业务ID列表批量删除编写中的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 删除数量
     */
    int deleteEditingByBizIds(@Param("bizIds") List<String> bizIds, @Param("tenantId") Long tenantId, @Param("userId") Long userId);

    /**
     * 根据业务ID列表批量删除已发布的自建内容
     *
     * @param bizIds 业务ID列表
     * @param tenantId 租户ID
     * @return 删除数量
     */
    int deletePublishedByBizIds(@Param("bizIds") List<String> bizIds, @Param("tenantId") Long tenantId, @Param("userId") Long userId);

    /**
     * 根据业务ID查询内容节点信息
     *
     * @param bizId 业务ID
     * @return 内容节点信息
     */
    CustomContentPO selectPublishContentNodeByBizId(@Param("bizId") String bizId);

    /**
     * 批量插入自建内容记录
     *
     * @param records 自建内容记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("records") List<CustomContentPO> records);

    /**
     * 根据业务ID列表批量查询编写中的自建内容目录信息
     *
     * @param bizIds 业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容目录信息列表
     */
    List<CustomContentPO> selectEditingCatalogByBizIds(@Param("bizIds") List<String> bizIds, @Param("tenantId") Long tenantId);

    /**
     * 根据业务ID列表批量查询已发布的自建内容目录信息
     *
     * @param bizIds 业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容目录信息列表
     */
    List<CustomContentPO> selectPublishedCatalogByBizIds(@Param("bizIds") List<String> bizIds, @Param("tenantId") Long tenantId);
}