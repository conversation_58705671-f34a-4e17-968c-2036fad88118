package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.paper.PaperInstanceRelationPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

/**
 * 试卷实例关系
 */
@Mapper
public interface PaperInstanceRelationPOMapper {
    /**
     * 新增
     * @param relation 实例关系
     * @return 影响行数
     */
    int insertOrUpdate(PaperInstanceRelationPO relation);

    /**
     * 判断实例关系是否存在
     * @param relation 实例关系条件实体
     * @return 实例关系数量
     */
    int selectCount(PaperInstanceRelationPO relation);

    /**
     * 新增
     * @param relation 实例关系
     * @return 影响行数
     */
    int insert(PaperInstanceRelationPO relation);

    /**
     * 更新目标实例ID
     * @param relation 实例关系
     * @return 更新数量
     */
    int updateTargetInstanceId(PaperInstanceRelationPO relation);

    /**
     * 查询推荐卷实例关系
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param type 实例关系类型
     * @param instanceId 实例ID
     * @return 实例关系
     */
    PaperInstanceRelationPO getRelationByInstanceId(
            @Param("instanceId") String instanceId,
            @Param("tenantId") Long tenantId,
            @Param("openId")  String openId,
            @Param("type")  Integer type);

    /**
     * 查询最新实例关系
     * @param paperId 试卷ID
     * @param paperVersionNumber 试卷版本号
     * @param type 实例关系类型
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @return 实例关系
     */
    PaperInstanceRelationPO selectLatestInstanceRelation(
            @Param("paperId") String paperId,
            @Param("paperVersionNumber") @Nullable String paperVersionNumber,
            @Param("type")  Integer type,
            @Param("tenantId") Long tenantId,
            @Param("openId")  String openId);

    /**
     * 删除
     * @param paperId 试卷ID
     * @param versionNumber 试卷版本号
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    int deletePreviewInfo(@Param("paperId") String paperId,
                          @Param("versionNumber") String versionNumber,
                          @Param("openId") String openId,
                          @Param("tenantId") Long tenantId);

}
