package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.question.QuestionGroupSettingPO;

/**
* <AUTHOR>
* @description 针对表【question_group_setting】的数据库操作Mapper
* @createDate 2025-03-06 09:31:50
* @Entity com.unipus.digitalbook.model.po.question.QuestionGroupSettingPO
*/
public interface QuestionGroupSettingPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(QuestionGroupSettingPO questionGroupSettingPO);

    int insertSelective(QuestionGroupSettingPO questionGroupSettingPO);

    QuestionGroupSettingPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QuestionGroupSettingPO questionGroupSettingPO);

    int insertOrUpdateSelective(QuestionGroupSettingPO questionGroupSettingPO);

    QuestionGroupSettingPO selectByGroupId(Long groupId);
}
