package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.question.QuestionPO;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【question】的数据库操作Mapper
* @createDate 2025-03-06 09:30:47
* @Entity com.unipus.digitalbook.model.po.question.QuestionPO
*/
public interface QuestionPOMapper {

    int deleteByPrimaryKey(Long id);

    int insertOrUpdateSelective(QuestionPO questionPO);

    QuestionPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QuestionPO questionPO);

    List<QuestionPO> selectByGroupIds(List<Long> groupIds);

    List<QuestionPO> selectByGroupId(Long groupId);

    List<QuestionPO> selectByBizQuestionIdsAndVersion(Collection<String> bizQuestionIds, String versionNumber);

    int batchInsertOrUpdate(Collection<QuestionPO> list);

    int deleteByIds(Collection<Long> ids, Long opsUserId);
}
