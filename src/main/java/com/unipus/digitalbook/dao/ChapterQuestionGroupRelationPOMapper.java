package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.book.ChapterQuestionGroupRelationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【chapter_question_group_relation】的数据库操作Mapper
* @createDate 2025-04-10 18:45:47
* @Entity com.unipus.digitalbook.model.po.book.ChapterQuestionGroupRelationPO
*/
public interface ChapterQuestionGroupRelationPOMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(ChapterQuestionGroupRelationPO relation);

    ChapterQuestionGroupRelationPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ChapterQuestionGroupRelationPO relation);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(List<ChapterQuestionGroupRelationPO> list);

    /**
     * 根据id查询关联的questionIds
     * @param versionChapterId
     * @return
     */
    List<Long> selectQuestionIdsByVersionChapterId(@Param("versionChapterId") Long versionChapterId);

    /**
     * 根据versionChapterId和bizGroupId查询questionId
     * @param versionChapterId
     * @param bizGroupId
     * @return
     */
    Long selectQuestionIdByVersionChapterIdAndBizGroupId(@Param("versionChapterId") Long versionChapterId, @Param("bizGroupId") String bizGroupId);

}
