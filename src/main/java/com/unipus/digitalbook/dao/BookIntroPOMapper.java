package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.book.BookIntroPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【book_intro(教材简介表)】的数据库操作Mapper
* @createDate 2024-12-16 22:47:31
* @Entity com.unipus.digitalbook.model.po.book.BookIntroPO
*/
public interface BookIntroPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BookIntroPO bookIntroPO);

    int insertSelective(BookIntroPO bookIntroPO);

    BookIntroPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BookIntroPO bookIntroPO);

    int updateByPrimaryKey(BookIntroPO bookIntroPO);

    BookIntroPO selectByBookId(String bookId);

    BookIntroPO selectByBookIdAndVersion(@Param("bookId") String bookId, @Param("version") String version);

    /**
     * 根据教材版本id，查询教材简介
     * @param bookVersionId
     * @return
     */
    BookIntroPO selectByBookVersion(@Param("bookVersionId") Long  bookVersionId);

    int generateVersion(@Param("bookId") String bookId, @Param("version") String version);

    /**
     * 批量查询教材简介信息
     *
     * @param bookIds 教材 ID 列表
     */
    List<BookIntroPO> selectListByBookIds(@Param("bookIds") List<String> bookIds);

    /**
     * 根据 教材 ID、版本号 列表批量查询教材简介信息
     */
    List<BookIntroPO> selectListByBookIdAndVersionList(@Param("list") List<BookIntroPO> list);

    /**
     * 根据教材版本记录 ID 列表批量查询教材简介信息
     */
    List<BookIntroPO> selectListByBookVersionIds(@Param("bookVersionIds") List<Long> bookVersionIds);

}
