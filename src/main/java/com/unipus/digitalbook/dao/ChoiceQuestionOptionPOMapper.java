package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.question.ChoiceQuestionOptionPO;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【choice_question_option】的数据库操作Mapper
* @createDate 2025-03-06 09:35:33
* @Entity com.unipus.digitalbook.model.po.question.ChoiceQuestionOptionPO
*/
public interface ChoiceQuestionOptionPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ChoiceQuestionOptionPO choiceQuestionOptionPO);

    int insertOrUpdateSelective(ChoiceQuestionOptionPO choiceQuestionOptionPO);

    ChoiceQuestionOptionPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ChoiceQuestionOptionPO choiceQuestionOptionPO);

    int batchInsertOrUpdate(Collection<ChoiceQuestionOptionPO> list);

    List<ChoiceQuestionOptionPO> selectByQuestionId(Long questionId);
    List<ChoiceQuestionOptionPO> selectByQuestionIds(List<Long> questionIds);

    int deleteByIds(Collection<Long> ids, Long opsUserId);
}
