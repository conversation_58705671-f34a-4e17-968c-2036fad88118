package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.paper.PaperRoundPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试卷轮次表(PaperRoundPO)表数据库访问层
 */
public interface PaperRoundPOMapper {
    /**
     * 新增或更新
     * @param paperRoundPO 实体对象
     * @return 影响行数
     */
    int insertOrUpdate(PaperRoundPO paperRoundPO);

    /**
     * 根据成绩批次id查询
     * @param scoreBatchIds 成绩批次ID列表
     * @return 实体对象集合
     */
    List<PaperRoundPO> selectByScoreBatchIds(List<String> scoreBatchIds);

    /**
     * 查询最新试卷实例
     * @param instanceId 试卷实例ID
     * @return 试卷实例
     */
    PaperRoundPO getPaperInstanceById(@Param("instanceId") String instanceId);

    /**
     * 查询最新试卷实例
     * @param paperId 试卷业务ID
     * @param versionNumber 试卷版本号
     * @param openId 用户ssoID
     * @param tenantId 租户ID
     * @param status 成绩提交状态
     * @return 最新试卷实例
     */
    PaperRoundPO getLatestPaperInstance(
            @Param("paperId") String paperId,
            @Param("versionNumber") String versionNumber,
            @Param("openId") String openId,
            @Param("tenantId") Long tenantId,
            @Param("status") Integer status);

    /**
     * 根据试卷成绩批次ID查询试卷实例列表
     * @param scoreBatchId 试卷业务ID
     * @return 试卷实例列表
     */
    List<PaperRoundPO> getLatestPaperInstanceByScoreBatchId(
            @Param("scoreBatchId") String scoreBatchId);

}