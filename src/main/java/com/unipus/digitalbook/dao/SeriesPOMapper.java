package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.SeriesPO;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【series(教材系列表)】的数据库操作Mapper
* @createDate 2024-12-18 15:34:56
* @Entity com.unipus.digitalbook.model.po.SeriesPO
*/
public interface SeriesPOMapper {

    int deleteByPrimaryKey(Long id);

    int insert(SeriesPO seriesPO);

    int insertSelective(SeriesPO seriesPO);

    SeriesPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SeriesPO seriesPO);

    int updateByPrimaryKey(SeriesPO seriesPO);

    List<SeriesPO> selectByIds(Collection<Long> seriesIds);

    List<SeriesPO> selectAll();
}
