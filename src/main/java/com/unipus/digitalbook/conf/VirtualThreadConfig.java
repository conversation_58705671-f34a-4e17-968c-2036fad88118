package com.unipus.digitalbook.conf;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * 虚拟线程配置
 * 配置JDK 21的虚拟线程，提升高并发性能
 * 
 * <AUTHOR>
 */
@Configuration
@EnableAsync
@ConfigurationProperties(prefix = "app.virtual-thread")
public class VirtualThreadConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(VirtualThreadConfig.class);
    
    /**
     * 是否启用虚拟线程
     */
    private boolean enabled = true;
    
    /**
     * 高并发执行器线程数量
     */
    private int highConcurrencyPoolSize = Runtime.getRuntime().availableProcessors() * 4;
    
    /**
     * 批处理执行器线程数量
     */
    private int batchProcessPoolSize = Runtime.getRuntime().availableProcessors() * 2;
    
    /**
     * 高并发执行器 - 使用虚拟线程
     * 适用于I/O密集型任务
     */
    @Bean(name = "highConcurrencyExecutor")
    public ExecutorService highConcurrencyExecutor() {
        if (enabled && isVirtualThreadSupported()) {
            logger.info("Creating virtual thread executor for high concurrency tasks");
            
            ThreadFactory virtualThreadFactory = Thread.ofVirtual()
                .name("high-concurrency-virtual-", 0)
                .factory();
            
            return Executors.newThreadPerTaskExecutor(virtualThreadFactory);
        } else {
            logger.info("Creating platform thread pool for high concurrency tasks, size: {}", highConcurrencyPoolSize);
            
            ThreadFactory platformThreadFactory = Thread.ofPlatform()
                .name("high-concurrency-platform-", 0)
                .factory();
            
            return Executors.newFixedThreadPool(highConcurrencyPoolSize, platformThreadFactory);
        }
    }
    
    /**
     * 批处理执行器 - 使用平台线程
     * 适用于CPU密集型任务
     */
    @Bean(name = "batchProcessExecutor")
    public ExecutorService batchProcessExecutor() {
        logger.info("Creating platform thread pool for batch processing, size: {}", batchProcessPoolSize);
        
        ThreadFactory threadFactory = Thread.ofPlatform()
            .name("batch-process-", 0)
            .daemon(false)
            .priority(Thread.NORM_PRIORITY)
            .factory();
        
        return Executors.newFixedThreadPool(batchProcessPoolSize, threadFactory);
    }
    
    /**
     * 异步任务执行器 - 使用虚拟线程
     */
    @Bean(name = "asyncTaskExecutor")
    public ExecutorService asyncTaskExecutor() {
        if (enabled && isVirtualThreadSupported()) {
            logger.info("Creating virtual thread executor for async tasks");
            
            ThreadFactory virtualThreadFactory = Thread.ofVirtual()
                .name("async-virtual-", 0)
                .factory();
            
            return Executors.newThreadPerTaskExecutor(virtualThreadFactory);
        } else {
            logger.info("Creating platform thread pool for async tasks");
            
            ThreadFactory platformThreadFactory = Thread.ofPlatform()
                .name("async-platform-", 0)
                .factory();
            
            return Executors.newCachedThreadPool(platformThreadFactory);
        }
    }
    
    /**
     * 数据库操作执行器 - 混合模式
     * 对于短时间的数据库操作使用虚拟线程
     */
    @Bean(name = "databaseOperationExecutor")
    public ExecutorService databaseOperationExecutor() {
        if (enabled && isVirtualThreadSupported()) {
            logger.info("Creating virtual thread executor for database operations");
            
            ThreadFactory virtualThreadFactory = Thread.ofVirtual()
                .name("db-virtual-", 0)
                .factory();
            
            return Executors.newThreadPerTaskExecutor(virtualThreadFactory);
        } else {
            int poolSize = Math.max(10, Runtime.getRuntime().availableProcessors());
            logger.info("Creating platform thread pool for database operations, size: {}", poolSize);
            
            ThreadFactory platformThreadFactory = Thread.ofPlatform()
                .name("db-platform-", 0)
                .factory();
            
            return Executors.newFixedThreadPool(poolSize, platformThreadFactory);
        }
    }
    
    /**
     * 检查是否支持虚拟线程
     */
    private boolean isVirtualThreadSupported() {
        try {
            // 检查JDK版本
            String javaVersion = System.getProperty("java.version");
            logger.info("Java version: {}", javaVersion);
            
            // JDK 21+支持虚拟线程
            String[] versionParts = javaVersion.split("\\.");
            int majorVersion = Integer.parseInt(versionParts[0]);
            
            if (majorVersion >= 21) {
                // 尝试创建虚拟线程来验证支持性
                Thread virtualThread = Thread.ofVirtual().start(() -> {});
                virtualThread.join();
                logger.info("Virtual thread support verified");
                return true;
            } else {
                logger.warn("Virtual threads require JDK 21+, current version: {}", javaVersion);
                return false;
            }
        } catch (Exception e) {
            logger.warn("Virtual thread support check failed, falling back to platform threads", e);
            return false;
        }
    }
    
    /**
     * 线程池监控信息
     */
    @Bean
    public ThreadPoolMonitor threadPoolMonitor() {
        return new ThreadPoolMonitor();
    }
    
    /**
     * 线程池监控器
     */
    public static class ThreadPoolMonitor {
        private static final Logger logger = LoggerFactory.getLogger(ThreadPoolMonitor.class);
        
        /**
         * 获取系统线程信息
         */
        public ThreadInfo getThreadInfo() {
            ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
            ThreadGroup parentGroup;
            while ((parentGroup = rootGroup.getParent()) != null) {
                rootGroup = parentGroup;
            }
            
            int activeThreads = rootGroup.activeCount();
            
            return new ThreadInfo()
                .setActiveThreads(activeThreads)
                .setAvailableProcessors(Runtime.getRuntime().availableProcessors())
                .setMaxMemory(Runtime.getRuntime().maxMemory())
                .setFreeMemory(Runtime.getRuntime().freeMemory())
                .setTotalMemory(Runtime.getRuntime().totalMemory());
        }
        
        /**
         * 记录线程池状态
         */
        public void logThreadPoolStatus() {
            ThreadInfo info = getThreadInfo();
            logger.info("Thread Pool Status - Active Threads: {}, Available Processors: {}, " +
                       "Memory - Max: {}MB, Free: {}MB, Total: {}MB",
                info.getActiveThreads(),
                info.getAvailableProcessors(),
                info.getMaxMemory() / 1024 / 1024,
                info.getFreeMemory() / 1024 / 1024,
                info.getTotalMemory() / 1024 / 1024);
        }
    }
    
    /**
     * 线程信息
     */
    public static class ThreadInfo {
        private int activeThreads;
        private int availableProcessors;
        private long maxMemory;
        private long freeMemory;
        private long totalMemory;
        
        public int getActiveThreads() {
            return activeThreads;
        }
        
        public ThreadInfo setActiveThreads(int activeThreads) {
            this.activeThreads = activeThreads;
            return this;
        }
        
        public int getAvailableProcessors() {
            return availableProcessors;
        }
        
        public ThreadInfo setAvailableProcessors(int availableProcessors) {
            this.availableProcessors = availableProcessors;
            return this;
        }
        
        public long getMaxMemory() {
            return maxMemory;
        }
        
        public ThreadInfo setMaxMemory(long maxMemory) {
            this.maxMemory = maxMemory;
            return this;
        }
        
        public long getFreeMemory() {
            return freeMemory;
        }
        
        public ThreadInfo setFreeMemory(long freeMemory) {
            this.freeMemory = freeMemory;
            return this;
        }
        
        public long getTotalMemory() {
            return totalMemory;
        }
        
        public ThreadInfo setTotalMemory(long totalMemory) {
            this.totalMemory = totalMemory;
            return this;
        }
    }
    
    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public int getHighConcurrencyPoolSize() {
        return highConcurrencyPoolSize;
    }
    
    public void setHighConcurrencyPoolSize(int highConcurrencyPoolSize) {
        this.highConcurrencyPoolSize = highConcurrencyPoolSize;
    }
    
    public int getBatchProcessPoolSize() {
        return batchProcessPoolSize;
    }
    
    public void setBatchProcessPoolSize(int batchProcessPoolSize) {
        this.batchProcessPoolSize = batchProcessPoolSize;
    }
}