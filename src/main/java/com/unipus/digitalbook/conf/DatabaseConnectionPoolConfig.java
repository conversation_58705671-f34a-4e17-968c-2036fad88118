package com.unipus.digitalbook.conf;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据库连接池优化配置
 * 针对高并发场景优化HikariCP连接池配置，防止死锁
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "app.datasource.hikari")
public class DatabaseConnectionPoolConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConnectionPoolConfig.class);
    
    /**
     * 最大连接数
     */
    private int maximumPoolSize = 20;
    
    /**
     * 最小空闲连接数
     */
    private int minimumIdle = 5;
    
    /**
     * 连接超时时间（毫秒）
     */
    private long connectionTimeout = 20000;
    
    /**
     * 空闲超时时间（毫秒）
     */
    private long idleTimeout = 300000;
    
    /**
     * 最大生存时间（毫秒）
     */
    private long maxLifetime = 1200000;
    
    /**
     * 连接泄漏检测阈值（毫秒）
     */
    private long leakDetectionThreshold = 60000;
    
    /**
     * 验证超时时间（毫秒）
     */
    private long validationTimeout = 5000;
    
    /**
     * 数据库URL
     */
    private String jdbcUrl;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 驱动类名
     */
    private String driverClassName = "com.mysql.cj.jdbc.Driver";
    
    /**
     * 主数据源配置（读写）
     */
    @Bean
    @Primary
    public DataSource primaryDataSource() {
        logger.info("Configuring primary datasource with optimized settings");
        
        HikariConfig config = new HikariConfig();
        
        // 基本连接信息
        config.setJdbcUrl(jdbcUrl);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName(driverClassName);
        
        // 连接池配置
        config.setMaximumPoolSize(maximumPoolSize);
        config.setMinimumIdle(minimumIdle);
        config.setConnectionTimeout(connectionTimeout);
        config.setIdleTimeout(idleTimeout);
        config.setMaxLifetime(maxLifetime);
        config.setLeakDetectionThreshold(leakDetectionThreshold);
        config.setValidationTimeout(validationTimeout);
        
        // 连接池名称
        config.setPoolName("PrimaryHikariPool");
        
        // 连接测试查询
        config.setConnectionTestQuery("SELECT 1");
        
        // MySQL特定配置 - 防止死锁优化
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        
        // 事务隔离级别 - 避免死锁
        config.addDataSourceProperty("transactionIsolation", "READ_COMMITTED");
        
        // 连接超时和锁等待时间设置
        config.addDataSourceProperty("connectTimeout", "10000");
        config.addDataSourceProperty("socketTimeout", "30000");
        
        // MySQL 8.0+特定优化
        config.addDataSourceProperty("serverTimezone", "Asia/Shanghai");
        config.addDataSourceProperty("useUnicode", "true");
        config.addDataSourceProperty("characterEncoding", "utf8mb4");
        config.addDataSourceProperty("allowPublicKeyRetrieval", "true");
        config.addDataSourceProperty("useSSL", "false");
        
        // 死锁检测和恢复
        config.addDataSourceProperty("deadlockTimeoutMs", "5000");
        config.addDataSourceProperty("lockWaitTimeout", "10");
        
        HikariDataSource dataSource = new HikariDataSource(config);
        
        logger.info("Primary datasource configured - Pool: {}, Max: {}, Min: {}", 
            config.getPoolName(), maximumPoolSize, minimumIdle);
        
        return dataSource;
    }
    
    /**
     * 只读数据源配置（用于读操作分离）
     */
    @Bean
    public DataSource readOnlyDataSource() {
        logger.info("Configuring read-only datasource");
        
        HikariConfig config = new HikariConfig();
        
        // 基本连接信息（可以配置为从库地址）
        config.setJdbcUrl(jdbcUrl); // 实际使用时应配置为从库地址
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName(driverClassName);
        
        // 只读连接池配置（可以设置更大的连接数）
        config.setMaximumPoolSize(Math.max(maximumPoolSize, 30));
        config.setMinimumIdle(Math.max(minimumIdle, 10));
        config.setConnectionTimeout(connectionTimeout);
        config.setIdleTimeout(idleTimeout);
        config.setMaxLifetime(maxLifetime);
        config.setLeakDetectionThreshold(leakDetectionThreshold);
        config.setValidationTimeout(validationTimeout);
        
        // 连接池名称
        config.setPoolName("ReadOnlyHikariPool");
        
        // 设置为只读
        config.setReadOnly(true);
        
        // 连接测试查询
        config.setConnectionTestQuery("SELECT 1");
        
        // 只读优化配置
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "300");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        
        // 只读事务隔离级别
        config.addDataSourceProperty("transactionIsolation", "READ_COMMITTED");
        
        // MySQL配置
        config.addDataSourceProperty("serverTimezone", "Asia/Shanghai");
        config.addDataSourceProperty("useUnicode", "true");
        config.addDataSourceProperty("characterEncoding", "utf8mb4");
        config.addDataSourceProperty("useSSL", "false");
        
        HikariDataSource dataSource = new HikariDataSource(config);
        
        logger.info("Read-only datasource configured - Pool: {}, Max: {}, Min: {}", 
            config.getPoolName(), config.getMaximumPoolSize(), config.getMinimumIdle());
        
        return dataSource;
    }
    
    /**
     * 数据源监控
     */
    @Bean
    public DataSourceMonitor dataSourceMonitor() {
        return new DataSourceMonitor();
    }
    
    /**
     * 数据源监控器
     */
    public static class DataSourceMonitor {
        private static final Logger logger = LoggerFactory.getLogger(DataSourceMonitor.class);
        
        /**
         * 获取连接池状态
         */
        public void logConnectionPoolStatus(DataSource dataSource) {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                
                logger.info("Connection Pool Status - Pool: {}, Active: {}, Idle: {}, Total: {}, Waiting: {}",
                    hikariDataSource.getPoolName(),
                    hikariDataSource.getHikariPoolMXBean().getActiveConnections(),
                    hikariDataSource.getHikariPoolMXBean().getIdleConnections(),
                    hikariDataSource.getHikariPoolMXBean().getTotalConnections(),
                    hikariDataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
            }
        }
        
        /**
         * 检查连接池健康状态
         */
        public boolean isHealthy(DataSource dataSource) {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                try {
                    // 尝试获取连接
                    hikariDataSource.getConnection().close();
                    return true;
                } catch (Exception e) {
                    logger.error("Connection pool health check failed", e);
                    return false;
                }
            }
            return true;
        }
    }
    
    // Getters and Setters
    public int getMaximumPoolSize() {
        return maximumPoolSize;
    }
    
    public void setMaximumPoolSize(int maximumPoolSize) {
        this.maximumPoolSize = maximumPoolSize;
    }
    
    public int getMinimumIdle() {
        return minimumIdle;
    }
    
    public void setMinimumIdle(int minimumIdle) {
        this.minimumIdle = minimumIdle;
    }
    
    public long getConnectionTimeout() {
        return connectionTimeout;
    }
    
    public void setConnectionTimeout(long connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }
    
    public long getIdleTimeout() {
        return idleTimeout;
    }
    
    public void setIdleTimeout(long idleTimeout) {
        this.idleTimeout = idleTimeout;
    }
    
    public long getMaxLifetime() {
        return maxLifetime;
    }
    
    public void setMaxLifetime(long maxLifetime) {
        this.maxLifetime = maxLifetime;
    }
    
    public long getLeakDetectionThreshold() {
        return leakDetectionThreshold;
    }
    
    public void setLeakDetectionThreshold(long leakDetectionThreshold) {
        this.leakDetectionThreshold = leakDetectionThreshold;
    }
    
    public long getValidationTimeout() {
        return validationTimeout;
    }
    
    public void setValidationTimeout(long validationTimeout) {
        this.validationTimeout = validationTimeout;
    }
    
    public String getJdbcUrl() {
        return jdbcUrl;
    }
    
    public void setJdbcUrl(String jdbcUrl) {
        this.jdbcUrl = jdbcUrl;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getDriverClassName() {
        return driverClassName;
    }
    
    public void setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
    }
}