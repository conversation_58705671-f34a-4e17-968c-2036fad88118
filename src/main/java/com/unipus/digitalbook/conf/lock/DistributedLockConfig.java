package com.unipus.digitalbook.conf.lock;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 分布式锁配置
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "app.distributed-lock")
public class DistributedLockConfig {
    
    /**
     * 默认等待时间（毫秒）
     */
    private long defaultWaitTime = 3000;
    
    /**
     * 默认锁定时间（毫秒）
     */
    private long defaultLeaseTime = 60000;
    
    /**
     * 锁key前缀
     */
    private String lockPrefix = "distributed_lock:";
    
    /**
     * 是否启用锁监控
     */
    private boolean enableMonitoring = true;
    
    /**
     * 锁超时告警阈值（毫秒）
     */
    private long timeoutAlertThreshold = 30000;
    
    public long getDefaultWaitTime() {
        return defaultWaitTime;
    }
    
    public void setDefaultWaitTime(long defaultWaitTime) {
        this.defaultWaitTime = defaultWaitTime;
    }
    
    public long getDefaultLeaseTime() {
        return defaultLeaseTime;
    }
    
    public void setDefaultLeaseTime(long defaultLeaseTime) {
        this.defaultLeaseTime = defaultLeaseTime;
    }
    
    public String getLockPrefix() {
        return lockPrefix;
    }
    
    public void setLockPrefix(String lockPrefix) {
        this.lockPrefix = lockPrefix;
    }
    
    public boolean isEnableMonitoring() {
        return enableMonitoring;
    }
    
    public void setEnableMonitoring(boolean enableMonitoring) {
        this.enableMonitoring = enableMonitoring;
    }
    
    public long getTimeoutAlertThreshold() {
        return timeoutAlertThreshold;
    }
    
    public void setTimeoutAlertThreshold(long timeoutAlertThreshold) {
        this.timeoutAlertThreshold = timeoutAlertThreshold;
    }
}