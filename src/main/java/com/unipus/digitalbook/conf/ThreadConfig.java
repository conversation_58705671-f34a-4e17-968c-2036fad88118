package com.unipus.digitalbook.conf;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
@Slf4j
public class ThreadConfig {

    @Bean(name = "virtualThreadExecutor")
    public ExecutorService virtualThreadExecutor() {
        ExecutorService virtualThreadPool = Executors.newVirtualThreadPerTaskExecutor();
        // 添加 Shutdown Hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            virtualThreadPool.close();
            log.info("Virtual thread pool has been shut down.");
        }));

        return virtualThreadPool;
    }
}
