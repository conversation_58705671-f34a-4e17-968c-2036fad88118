package com.unipus.digitalbook.conf.security;

import com.unipus.digitalbook.model.common.Response;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF保护
            .csrf(AbstractHttpConfigurer::disable)
            // 启用CORS
            .cors(cors -> cors.configurationSource(corsConfigurationSource())) // 启用CORS配置
            .authorizeHttpRequests(authorizeRequests ->
                    authorizeRequests
                        // 请求白名单URL（如果已经通过单点授权和拦截器鉴权，这里需要放行全部请求）
                        .requestMatchers("/**").permitAll()
                        // 其他请求需要认证
                        .anyRequest().authenticated()
            )
            // 配置访问拒绝处理器
            .exceptionHandling(exceptionHandling ->
                    exceptionHandling.accessDeniedHandler(accessDeniedHandler())
            );

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.addAllowedOriginPattern("*"); // 允许所有的域名请求
        configuration.addAllowedMethod("*");        // 允许所有的HTTP方法 (GET, POST, DELETE, etc.)
        configuration.addAllowedHeader("*");        // 允许所有的请求头
        configuration.setAllowCredentials(true);    // 允许发送Cookie等凭据
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration); // 应用到所有路径
        return source;
    }

    @Bean
    public AccessDeniedHandler accessDeniedHandler() {
        // 处理访问拒绝的逻辑，例如重定向到错误页面或返回特定的错误信息
        return (request, response, accessDeniedException) ->
            Response.writeErrorResponse(response, HttpServletResponse.SC_FORBIDDEN, "拒绝访问");

    }
}
