package com.unipus.digitalbook.grpc;

import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.grpc.format.IQuestion;
import com.unipus.digitalbook.grpc.format.QuestionTypeRelation;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.QuestionBusinessTypeEnum;
import com.unipus.digitalbook.model.enums.SystemSourceEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 题目详情
 */
public class QuesGrpcDetailBase implements Serializable {

    @Schema(description = "问题类型ID，参照【题目类型】枚举列表", example = "891897160732517503L")
    private Long quesTypeId = 0L;

    @Schema(description = "问题类型版本(默认值:1)", example = "1")
    private Integer quesTypeVersion = 1;

    @Schema(description = "题库ID(默认值:0)", example = "1")
    private Long bankId = 0L;

    @Schema(description = "问题标题(非必须)", example = "数学基础问题")
    private String title = "";

    @Schema(description = "问题描述", example = "本题主要考察数学基础知识")
    private String description;

    @Schema(description = "题目的难度: 0未知 1容易 2比较容易3一般 4比较困难 5困难", example = "3")
    private Integer quesDifficulty;

    @Schema(description = "源系统外部ID(非必须)", example = "EXT001")
    private String sourceSystemExternalId = "";

    @Schema(description = "源系统外部版本(非必须)", example = "v1.0")
    private String sourceSystemExternalVersion = "";

    @Schema(description = "问题年份:当前年", example = "2025")
    private Integer quesYear;

    @Schema(description = "试题来源(默认值0)：1001官方真题，1002外研社模拟题，1003北外题库，1004UNIPUS，2001原创，0其他", example = "0")
    private Integer sourceType = 0;

    @Schema(description = "科目(默认值1)：1英语，2俄语，3日语，4法语，5越南语，6汉语，7德语，8韩语，9西班牙语，10阿拉伯语，11其他 ,", example = "1")
    private Integer subject = 1;

    @Schema(description = "共享状态(默认值3)：0私有，1学校内部分教师可见，2全校可见，3跨应用用户可见 ,", example = "3")
    private Integer shareStatus = 3;

    @Schema(description = "题目content json", example = "content json")
    private String content;

    @Schema(description = "题目的拥有者ID(默认值0)", example = "0")
    private Long owner = 0L;

    @Schema(description = "来源系统", example = "(itest, ucontent等系统对应系统表的id)")
    private Integer sourceId;

    @Schema(description = "当前用户ID", example = "U001")
    private String userId;

    @Schema(description = "标签列表(非必须)")
    private List<Tag> tagList;

    public Long getQuesTypeId() {
        return quesTypeId;
    }

    public void setQuesTypeId(Long quesTypeId) {
        this.quesTypeId = quesTypeId;
    }

    public Integer getQuesTypeVersion() {
        return quesTypeVersion;
    }

    public void setQuesTypeVersion(Integer quesTypeVersion) {
        this.quesTypeVersion = quesTypeVersion;
    }

    public Long getBankId() {
        return bankId;
    }

    public void setBankId(Long bankId) {
        this.bankId = bankId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getQuesDifficulty() {
        return quesDifficulty;
    }

    public void setQuesDifficulty(Integer quesDifficulty) {
        this.quesDifficulty = quesDifficulty;
    }

    public String getSourceSystemExternalId() {
        return sourceSystemExternalId;
    }

    public void setSourceSystemExternalId(String sourceSystemExternalId) {
        this.sourceSystemExternalId = sourceSystemExternalId;
    }

    public String getSourceSystemExternalVersion() {
        return sourceSystemExternalVersion;
    }

    public void setSourceSystemExternalVersion(String sourceSystemExternalVersion) {
        this.sourceSystemExternalVersion = sourceSystemExternalVersion;
    }

    public Integer getQuesYear() {
        return quesYear;
    }

    public void setQuesYear(Integer quesYear) {
        this.quesYear = quesYear;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getSubject() {
        return subject;
    }

    public void setSubject(Integer subject) {
        this.subject = subject;
    }

    public Integer getShareStatus() {
        return shareStatus;
    }

    public void setShareStatus(Integer shareStatus) {
        this.shareStatus = shareStatus;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getOwner() {
        return owner;
    }

    public void setOwner(Long owner) {
        this.owner = owner;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<Tag> getTagList() {
        return tagList;
    }

    public void setTagList(List<Tag> tagList) {
        this.tagList = tagList;
    }

    public static class Tag {
        @Schema(description = "试题ID", example = "00001")
        private String quesId;

        @Schema(description = "标签ID", example = "1")
        private Long tagId;

        public Long getTagId() {
            return tagId;
        }

        public void setTagId(Long tagId) {
            this.tagId = tagId;
        }

        public String getQuesId() {
            return quesId;
        }

        public void setQuesId(String quesId) {
            this.quesId = quesId;
        }
    }

    public QuesGrpcDetailBase(){
    }

    // 构建请求参数
    public QuesGrpcDetailBase(BigQuestionGroup questionGroup, Long userId) {
        String businessTypeName = QuestionBusinessTypeEnum.getNameByCode(questionGroup.getType());
        QuestionTypeRelation questionTypeRelation = QuestionTypeRelation.getEnumByName(businessTypeName);
        if(questionTypeRelation ==null){
            String message = String.format("题型不存在，题型名称：%s", questionGroup.getType());
            throw new IllegalArgumentException(message);
        }

        // 对应题型的id
        this.setQuesTypeId(questionTypeRelation.getId());
        // 对应题型的version 默认值：1
        this.setQuesTypeVersion(1);
        // 题库id 默认值：0
        this.setBankId(0L);
        // 题目标题 非必须
        this.setTitle("");
        // 题目描述 非必须
        this.setDescription("");
        // 题目难度 非必须(题目的难度 0未知 1容易 2比较容易3一般 4比较困难 5困难)
        this.setQuesDifficulty(0);
        // 源系统外部id(同步用) 非必须
        this.setSourceSystemExternalId("");
        // 题目来源系统ID(同步用) 非必须
        this.setSourceSystemExternalVersion("");
        // 题目年份 非必须(默认当前年)
        this.setQuesYear(DateUtil.getYear(new Date()));
        // 题目来源类型(默认值0)：1001官方真题，1002外研社模拟题，1003北外题库，1004UNIPUS，2001原创，0其他
        this.setSourceType(0);
        // 题目科目(默认值1)：科目：1英语，2俄语，3日语，4法语，5越南语，6汉语，7德语，8韩语，9西班牙语，10阿拉伯语，11其他
        this.setSubject(1);
        // 题目共享状态(默认值3)：共享状态：0私有，1学校内部分教师可见，2全校可见，3跨应用用户可见 ,
        this.setShareStatus(3);
        // 题目内容(json)
        this.setContent(JsonUtil.toJsonString(new IQuestion(questionGroup)));
        // 题目的拥有者(默认值0)
        this.setOwner(0L);
        // 来源系统
        this.setSourceId(SystemSourceEnum.IPUBLISH.getId());
        // 当前用户ID
        this.setUserId(String.valueOf(userId));
        // 标签列表(非必须)
        this.setTagList(List.of());
    }

}

