package com.unipus.digitalbook.grpc.format;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QuestionTypeRelation {

//    SINGLE_CHOICE(891897160732517503L, "single_choice_group", QuestionCategory.BASIC, "basic", "singlechoice", "单选题"),
//    MULTIPLE_CHOICE(891897160732517504L, "multichoice_group", QuestionCategory.BASIC, "basic", "multichoice", "多选题"),
//    FILL_IN_BLANK(891897160732517505L, "fillblank_scoop_group", QuestionCategory.BASIC, "basic", "text-area", "选词填空"),
//    DROP_DOWN_SELECTION(891897160732517506L, "fillblank_scoop_dropdown_group", QuestionCategory.BASIC, "basic-scoop-content", "dropdownSelection", "下拉选择"),
//    MULTIMEDIA_UPLOAD(891897160732517507L, "multifile_upload_group", QuestionCategory.HEARING, "multiFileUpload", "multiFileUpload", "多媒体上传"),
//    DISCUSSION_QUESTION(891897160732517508L, "discussion_group", QuestionCategory.STUDY, "discussion", "discussion", "讨论题"),
//    TRANSLATION_QUESTION(891897160732517509L, "translation_group", QuestionCategory.BASIC, "basic", "text-area", "翻译题"),
//    WRITING_QUESTION(891897160732517510L, "writing_group", QuestionCategory.WRITING, "basic-scoop-content", "text-area", "写作题"),
//    TEXT_READING(891897160732517511L, "text_reading", QuestionCategory.ORAL, "paragraph-follow", "recordParagraph", "文本跟读"),
//    PERSONAL_STATEMENT(891897160732517512L, "oral_personal_state_group", QuestionCategory.ORAL, "oral-personal-state", "oral-personal-state", "个人陈述"),
//    SHORT_ANSWER(0L, "short_answer_group", QuestionCategory.BASIC, "basic-scoop-content", "text-area", "简答题"),
    ;

    // 题型ID
    private final Long id;
    // 题型名称
    private final String name;
    // 题型分类
    private final QuestionCategory category;
    // 题型组件类型
    private final String questionType;
    // 题型组件回复类型
    private final String questionReplyType;
    // 题型描述
    private final String desc;

    public static QuestionTypeRelation getEnumByName(String name) {
        if (name == null) {
            return null;
        }
        for (QuestionTypeRelation questionTypeRelation : QuestionTypeRelation.values()) {
            if (questionTypeRelation.getName().equals(name)) {
                return questionTypeRelation;
            }
        }
        return null;
    }

}
