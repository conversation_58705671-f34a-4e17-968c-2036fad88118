package com.unipus.digitalbook.grpc;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 题目返回信息实体类
 */
public class QuesGrpcStatus implements Serializable {

    @Schema(description = "返回值")
    private Integer code;

    @Schema(description = "消息内容")
    private String msg;

    public QuesGrpcStatus(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
    public String getMsg() {
        return msg;
    }
    public void setMsg(String msg) {
        this.msg = msg;
    }
}
